package com.chinamobile.retail.service;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.param.EStewardBannerListParam;
import com.chinamobile.retail.pojo.param.EStewardBannerOnOfflineParam;
import com.chinamobile.retail.pojo.param.EStewardDirectorySortParam;
import com.chinamobile.retail.pojo.param.SaveEStewardDirectoryParam;
import com.chinamobile.retail.pojo.vo.EStewardBannerDetailVO;
import com.chinamobile.retail.pojo.vo.EStewardBannerListVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
public class EStewardBannerServiceTest {

    @Resource
    private EStewardBannerService eStewardBannerService;


    @Test
    public void bannerList() {
        EStewardBannerListParam param = new EStewardBannerListParam();
        BaseAnswer<PageData<EStewardBannerListVO>> voidBaseAnswer = eStewardBannerService.bannerList(param);
        System.out.println(JSON.toJSONString(voidBaseAnswer));
    }

    @Test
    public void detail() {
        BaseAnswer<EStewardBannerDetailVO> detail = eStewardBannerService.getDetail("1366364750825721856");
        System.out.println(JSON.toJSONString(detail));

    }

    @Test
    public void onoffLine(){
        EStewardBannerOnOfflineParam param = new EStewardBannerOnOfflineParam();
        param.setStatus(2);
        param.setId("1366010016667328512");
        eStewardBannerService.onOffline(param);
    }



}
