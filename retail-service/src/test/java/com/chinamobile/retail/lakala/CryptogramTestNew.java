package com.chinamobile.retail.lakala;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.utils.BaseUtils;
import com.chinamobile.retail.pojo.dto.lakala.LakalaPublicResponseDTO;
import com.chinamobile.retail.util.LakalaRSAUtils;
import com.chinamobile.retail.util.RSAUtils;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLDecoder;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: cryptogram
 * @description: RSA2测试类
 * @author: WangRongRuo
 * @create: 2018-07-03 19:26
 **/
public class CryptogramTestNew {

    private static final Logger log = LoggerFactory.getLogger(CryptogramTestNew.class);

    private static final String privateKeyStr = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDTyrPZuAjILMsqTPxkkh3Rjp4/Cl94mw+8HP2lyN71m4GvOiAQQiT2JgsT/G//6XfcDqwPnc7VTIY7+3NN2dTR/D40dE9IHhLTBjMyuBAU4vJ9iwzBHv0dPfY17DTBNvd/MP7Rsp+j+YGaOhWaGlsgm6TVtlQey7QsmvWAV0YXGlyAf07ssmh1/M2bfxE95YrnpyUhjDZQzA+rq51kS7EAuPE/UNSOgDklwOKLi5guAhvI1Q6rLjtufdC+JqYrIPmxjRhjW1RFE2UNCw7LqBfByb9n8mxgP4LKBjiCLYAfEMfzieIQUNpLSj+a3xo1S4rpMGiR7TYxFLBlzdcxif6NAgMBAAECggEAWfSyxzq/oCZGdMUWPrFBMQzecfA59MQHvuKhaZUT/kX6oy6RB9bQVCx8cBS8jXngivtAYbGpdDd4nGmE5AAtwLLeyPDN69e6Lx9nB5feXMC4NlKlLDG5WH5E4UpebeKm5MMuuGqiG8eSIKujGT0wj30MWimDOUFUWc3HkKeBAbZOo7xRs28cSHk81G7mpiYmCQUJVyOCbdDOwGMkE51t07iVGNswXRqYaavVI85eEB4QRuPAdCGOZff/ll6rVrJPEBj8ch03oDEszdQb+0B39pUi3sfex2AGvLISSJTSy9cab43MCcGYZdL4leX7Eq+x3exZO3fmVPTm3dtJyhiPEQKBgQD0VWXMofRAHPl8cxf/lq1PAcalffBUo24Fv1DfsD9yNxagM8s5PzChsrTpi4BxEUdMZeeQrUtlrHgxWF9sBp21LczObkkqKQ9/b0M3rXIS91bqsQsFWS2OsGFzLo+voxtGip5KK9ORI34cwSaXxuJ5kwC5hAXnrG8I9Ef7IyWLewKBgQDd54gdKsR80HIuRLsirQZP/cKbHgcjQY1OATtk8q6b/rtvO9t0wSOeCCRMuv/EuKGk7POtqyuvwutqg1WKNxMy1bY9ty4bRXd44ii4aCYGDOAl8NFtJCSMWr++t6XzNHbwClbLSbY/tOGGIVumg1p+udu+btOb8lpHg7/WyHBblwKBgQC+3D6rPQ/JTVjuCBIFC7TR9Lcx61DjLM7zGmGYetr042d/OTZUv7HDfg+oJ9rrd+3UFf5vm488Gx/AnCHeBsUHFIHZ93ibwHtktosxYQGtIxz4M9hCE0ltHwbgrMx9DNJvpjTEB7w6shj/aTo2cZvUECsOv7zFHoOV4QyhdvELJQKBgBkluvwrM5c9fCMYMOjuGNAJ3vr7PS3WqO/VHekDw5v0E0O40db6aFHpdEupyYB+t/rby4W75ziE627nsVL3iNpy87MsxjHa/n4ZiynSy7RV0zUZhHJM7UNmqWIwp8LXCD+NvGZPVTMFCaXMs/k7246O+4MqRhrfLTH7kUsC7hDDAoGBAMs+rMVstr9udOIsKLmADBXPGK+UuWbtJAVCSyX61EuKz8gJL8KyXOlc3Dnokf4Qhh5ZMtslM6VPeSwGkHCH0CA66t/Aq56TCkbQi1IrJbc7gCL/CuX1jly8pOQs8widY/U7UrVxsJ87POVVXsf3I35FD198uYrqXzklLNGX7X6l";
    private static final String publicKeyStr = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA08qz2bgIyCzLKkz8ZJId0Y6ePwpfeJsPvBz9pcje9ZuBrzogEEIk9iYLE/xv/+l33A6sD53O1UyGO/tzTdnU0fw+NHRPSB4S0wYzMrgQFOLyfYsMwR79HT32New0wTb3fzD+0bKfo/mBmjoVmhpbIJuk1bZUHsu0LJr1gFdGFxpcgH9O7LJodfzNm38RPeWK56clIYw2UMwPq6udZEuxALjxP1DUjoA5JcDii4uYLgIbyNUOqy47bn3QviamKyD5sY0YY1tURRNlDQsOy6gXwcm/Z/JsYD+CygY4gi2AHxDH84niEFDaS0o/mt8aNUuK6TBoke02MRSwZc3XMYn+jQIDAQAB";

    private static final String lakalaPublicKeyStr = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoBkjQADiGzay1O8Ar3q3ky18dp0OdmkEaRnuARblzNZS0v5eEbl5MVgJWrRsT7kK7cMXdxsriAcvicSLiVFpP3oYLizbeTj7NiVH+J6MwggVYqnXZul3T6yVh5Jq0TJGXzyWHbtmMNLDxL/iWO4C8a1tNIhd07rVKF/SrMZ6jxpUyrUc8Y8cr59tND0IQpwVRcFevyMOLVZQPEtlGAq4rbHZ6JMTb6/I1f+k6MLqcnvXQ6Hd6MNwV9uOjmVDEtt0mgUeuSoUX1hXzMBZR7NO2OVXrJSWgFkP5TzTXXNoNIdURnWhUuJ6teJliMvEHYyFaPXVQmWvygzbSdPg2sX8RwIDAQAB";


    @Test
    public void crTest() {


        ImmutableMap.Builder<String, String> bizBuilder = ImmutableMap.builder();

        //公共参数
        ImmutableMap<String, String> publicParam = bizBuilder
                .put("channel", "看似苍南县")
                .put("method", "空隙大")
                .build();

        HashMap<String, String> hashMap = Maps.newHashMap(publicParam);



        //拼接传输参数
        try {
            PrivateKey privateKey = LakalaRSAUtils.getPrivateKey(privateKeyStr);
            PublicKey publicKey = LakalaRSAUtils.getPublic(publicKeyStr);

            //业务参数
            String businessParam = "{\"name\":\"mike\"}";
            System.out.println("bizContent的原文:"+businessParam);
            //将业务参数转化为 a=1&b=2的格式
            businessParam = LakalaRSAUtils.getEncodeSignContent(JSONObject.parseObject(businessParam, Map.class));

            //对业务参数使用私钥加密
            String bizContent = LakalaRSAUtils.encryptByPrivateKey(businessParam, privateKey);
            System.out.println("bizContent的密文:"+bizContent);
            //将业务参数密文放入请求参数中
            hashMap.put("bizContent",bizContent);
            //请求参数排序
            String prepareSign = LakalaRSAUtils.getEncodeSignContent(hashMap);
            System.out.println("准备签名的字符串:"+prepareSign);
            //加签
            String sign = LakalaRSAUtils.rsa256Sign(prepareSign, privateKey);
            log.info("加签结果:{}", sign);

            //增加签名参数
            hashMap.put("sign",sign);

            //发送请求

            //拉卡拉收到请求

            //sign之外的所有参数，排序，获得prepareSign

            //拉卡拉发送响应


            //收到响应

            //验签
            boolean a = LakalaRSAUtils.rsa256Check(prepareSign, sign, publicKey);
            log.info("验签结果:{}", a);

            //私钥加密
//            String s1 = LakalaRSAUtils.encryptByPrivateKey(prepareSign, privateKey);
//            log.info("私钥加密结果:{}", s1);
            //公钥解密
            String s11 = LakalaRSAUtils.decryptByPublicKey(bizContent, publicKey);
            log.info("公钥解密结果:{}", s11);

            //公钥加密
//            String s2 = LakalaRSAUtils.encryptByPublicKey(prepareSign, publicKey);
//            log.info("公钥加密结果:{}", s2);
            //私钥解密
//            String s21 = LakalaRSAUtils.decryptByPrivateKey(s2, privateKey);
//            log.info("私钥解密结果:{}", s21);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args){

        // 公共报文
        Map<String, String> pubMap = new HashMap<String, String>();
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timestamp = dateFormat.format(date);
        pubMap.put("channel", "ff03f5a1eeee490e");      // 业务渠道标识（见接口文档）
        pubMap.put("method", "getUserSignInfo");        // 接口名（见接口文档）
        pubMap.put("format", "JSON");                   // 请求数据格式（固定）
        pubMap.put("charset", "UTF-8");                 // 请求编码格式（固定）
        pubMap.put("signType", "RSA2");                 // 请求签名方式（固定）
        pubMap.put("timestamp", timestamp);             // 请求时间
        pubMap.put("version", "1.0");                   // 接口版本号

        // 业务参数（bizContent）
        ImmutableMap.Builder<String, String> bizBuilder = ImmutableMap.builder();
        ImmutableMap<String, String> map = bizBuilder
                .put("subChannel", "")
                .put("returnUrl", "http://sdffff.com")
                .build();
        HashMap<String, String> hashMap = Maps.newHashMap(map);
        // 拼接传输参数
        try {
            // 1、对业务参数进行URLEncoder转码（UTF-8），否则将出现中文乱码问题
            String bizContentJsonStr = JSON.toJSONString(hashMap);
            System.out.println("bizContent原生参数===：" + bizContentJsonStr);
            String bizContent = java.net.URLEncoder.encode(JSON.toJSONString(hashMap),"UTF-8");
            System.out.println("bizContent转码后===：" + bizContent);

            // 2、对业务参数进行RSA加密
            String biz_content = RSAUtils.encryptByPrivateKey(bizContent, privateKeyStr);
            System.out.println("bizContent加密后===: " + biz_content);

            pubMap.put("bizContent", biz_content);

            // 确认业务参数正常加解密是否正确
/*            String biz_content2 = java.net.URLDecoder.decode(RSAUtils.decryptByPublicKey(biz_content, publicKeyStr),"UTF-8");
        System.out.println("bizContent解密后===: " + biz_content2);*/

            // 3、对请求参数进行RSA私钥签名
            String signContent = RSAUtils.getEncodeSignContent(pubMap);
            System.out.println("签名原生内容: " + signContent);
            String sign = RSAUtils.signByPrivateKey(signContent.getBytes(),privateKeyStr);

            System.out.println("接口签名: " + sign);
            pubMap.put("sign", sign);

            // 确认签名是否正确（公钥验签）
/*            boolean a = RSAUtils.verifyByPublicKey(signContent.getBytes(),publicKeyStr,sign);
            System.out.println("验签结果:" + a);*/

            // 4、接口调用,获取响应
            String respStr = "{\"msg\":\"通信请求成功\",\"res\":true,\"code\":\"000000\",\"data\":\"dIcBh2nmnuRYN6wxmWZdFn8Css7x%2FFpLd01BGT9STy5nVFQ7bldb05LUlIudOdg1LxuWdQP7DV9A6lCGNhLpXlgz3N5qX2ifD2qH6i4qYPlKGWD0g%2F5EjM2xfXmazN%2Bz9b1th8FPRQuSQN1fSVMd1a1Y07R89YpgzECSM8%2BaUlQ0pqOHsCTiULK0Xuuq%2Fl5YGHxWGMPwjbGZ%2BF3p21l2VQy4bTzgpDzx5HYAbWrSUV7jY0eKOqb%2BVdwvMUIuKzkgQvcgVU%2FgIzYLpTf7Gie%2FBK%2FOJnPUp8dC4Zb1uKoSlWAKrNUr2oi195dvh%2BvqibcdCLQ7p%2B%2BAnLP3iumQVxQ5zg%3D%3D\",\"sign\":\"h4BA61f8ipcnLaps7G1Mpes9+LZgleEWoOIkFf4oabDOuTS7HOWgPGo8xYHl8Yq/63+W2OaS1UNszZcrkwK0lvv2yIzafFbmn/NKvbg2AUPoxkiosOv6mmWuKA783nzaDrccIQ7F+brMJM+sieEYRi3gUgUtQXRC4m9KMfP2KDbjkMu7pYb/dyFRFaX5ZLbwgKHSq5NhfBpbLDf5+6u2Cy3wV5BE4VQ8HeyP5bf/f1R03hTOCOKVGNqCF0vMvzrer5OhP46mq46jJlYlMkz3b77dGZoXxqgJj5PFO9Ibqx1LZatBJXYgmQAl1uX1Y2jlGf2v4FVjaR01XA4oBdTVqQ==\",\"signType\":\"RSA2\"}";

            LakalaPublicResponseDTO lakalaPublicResponseDTO = JSON.parseObject(respStr, LakalaPublicResponseDTO.class);
            String sign1 = lakalaPublicResponseDTO.getSign();
            Map<String, String> stringStringMap = BaseUtils.objectToStringMap(lakalaPublicResponseDTO);
            String prepareCheck = RSAUtils.getEncodeSignContent(stringStringMap);
            boolean a = RSAUtils.verifyByPublicKey(prepareCheck.getBytes(),lakalaPublicKeyStr, sign1);



            //5、使用平台公钥对响应数据（data） 进行解密
            String responseData = RSAUtils.decryptByPublicKey(URLDecoder.decode(lakalaPublicResponseDTO.getData(),"UTF-8"), lakalaPublicKeyStr);
            System.out.println("接口处理结果:" + responseData);



        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
