<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.MiniProgramInfoActivityMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.MiniProgramInfoActivity">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="info_id" jdbcType="VARCHAR" property="infoId" />
    <result column="activity_id" jdbcType="VARCHAR" property="activityId" />
    <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
    <result column="activity_img" jdbcType="VARCHAR" property="activityImg" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, info_id, activity_id, activity_name, activity_img, province_code, province_name
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramInfoActivityExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_program_info_activity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from mini_program_info_activity
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from mini_program_info_activity
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramInfoActivityExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from mini_program_info_activity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramInfoActivity">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_info_activity (id, info_id, activity_id, 
      activity_name, activity_img, province_code, 
      province_name)
    values (#{id,jdbcType=VARCHAR}, #{infoId,jdbcType=VARCHAR}, #{activityId,jdbcType=VARCHAR}, 
      #{activityName,jdbcType=VARCHAR}, #{activityImg,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, 
      #{provinceName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramInfoActivity">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_info_activity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="infoId != null">
        info_id,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="activityName != null">
        activity_name,
      </if>
      <if test="activityImg != null">
        activity_img,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="infoId != null">
        #{infoId,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="activityName != null">
        #{activityName,jdbcType=VARCHAR},
      </if>
      <if test="activityImg != null">
        #{activityImg,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramInfoActivityExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from mini_program_info_activity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    update mini_program_info_activity
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.infoId != null">
        info_id = #{record.infoId,jdbcType=VARCHAR},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=VARCHAR},
      </if>
      <if test="record.activityName != null">
        activity_name = #{record.activityName,jdbcType=VARCHAR},
      </if>
      <if test="record.activityImg != null">
        activity_img = #{record.activityImg,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    update mini_program_info_activity
    set id = #{record.id,jdbcType=VARCHAR},
      info_id = #{record.infoId,jdbcType=VARCHAR},
      activity_id = #{record.activityId,jdbcType=VARCHAR},
      activity_name = #{record.activityName,jdbcType=VARCHAR},
      activity_img = #{record.activityImg,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramInfoActivity">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    update mini_program_info_activity
    <set>
      <if test="infoId != null">
        info_id = #{infoId,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="activityName != null">
        activity_name = #{activityName,jdbcType=VARCHAR},
      </if>
      <if test="activityImg != null">
        activity_img = #{activityImg,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramInfoActivity">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    update mini_program_info_activity
    set info_id = #{infoId,jdbcType=VARCHAR},
      activity_id = #{activityId,jdbcType=VARCHAR},
      activity_name = #{activityName,jdbcType=VARCHAR},
      activity_img = #{activityImg,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_info_activity
    (id, info_id, activity_id, activity_name, activity_img, province_code, province_name
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.infoId,jdbcType=VARCHAR}, #{item.activityId,jdbcType=VARCHAR}, 
        #{item.activityName,jdbcType=VARCHAR}, #{item.activityImg,jdbcType=VARCHAR}, #{item.provinceCode,jdbcType=VARCHAR}, 
        #{item.provinceName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 03 11:26:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_info_activity (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'info_id'.toString() == column.value">
          #{item.infoId,jdbcType=VARCHAR}
        </if>
        <if test="'activity_id'.toString() == column.value">
          #{item.activityId,jdbcType=VARCHAR}
        </if>
        <if test="'activity_name'.toString() == column.value">
          #{item.activityName,jdbcType=VARCHAR}
        </if>
        <if test="'activity_img'.toString() == column.value">
          #{item.activityImg,jdbcType=VARCHAR}
        </if>
        <if test="'province_code'.toString() == column.value">
          #{item.provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="'province_name'.toString() == column.value">
          #{item.provinceName,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>