<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.MiniProgramHomeAdMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.MiniProgramHomeAd">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="home_id" jdbcType="VARCHAR" property="homeId" />
    <result column="img_url" jdbcType="VARCHAR" property="imgUrl" />
    <result column="activity_id" jdbcType="VARCHAR" property="activityId" />
    <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, name, home_id, img_url, activity_id, activity_name
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramHomeAdExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_program_home_ad
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from mini_program_home_ad
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from mini_program_home_ad
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramHomeAdExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from mini_program_home_ad
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramHomeAd">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_home_ad (id, name, home_id, 
      img_url, activity_id, activity_name
      )
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{homeId,jdbcType=VARCHAR}, 
      #{imgUrl,jdbcType=VARCHAR}, #{activityId,jdbcType=VARCHAR}, #{activityName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramHomeAd">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_home_ad
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="homeId != null">
        home_id,
      </if>
      <if test="imgUrl != null">
        img_url,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="activityName != null">
        activity_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="homeId != null">
        #{homeId,jdbcType=VARCHAR},
      </if>
      <if test="imgUrl != null">
        #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="activityName != null">
        #{activityName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramHomeAdExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from mini_program_home_ad
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_home_ad
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.homeId != null">
        home_id = #{record.homeId,jdbcType=VARCHAR},
      </if>
      <if test="record.imgUrl != null">
        img_url = #{record.imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=VARCHAR},
      </if>
      <if test="record.activityName != null">
        activity_name = #{record.activityName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_home_ad
    set id = #{record.id,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      home_id = #{record.homeId,jdbcType=VARCHAR},
      img_url = #{record.imgUrl,jdbcType=VARCHAR},
      activity_id = #{record.activityId,jdbcType=VARCHAR},
      activity_name = #{record.activityName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramHomeAd">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_home_ad
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="homeId != null">
        home_id = #{homeId,jdbcType=VARCHAR},
      </if>
      <if test="imgUrl != null">
        img_url = #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="activityName != null">
        activity_name = #{activityName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramHomeAd">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    update mini_program_home_ad
    set name = #{name,jdbcType=VARCHAR},
      home_id = #{homeId,jdbcType=VARCHAR},
      img_url = #{imgUrl,jdbcType=VARCHAR},
      activity_id = #{activityId,jdbcType=VARCHAR},
      activity_name = #{activityName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_home_ad
    (id, name, home_id, img_url, activity_id, activity_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.homeId,jdbcType=VARCHAR}, 
        #{item.imgUrl,jdbcType=VARCHAR}, #{item.activityId,jdbcType=VARCHAR}, #{item.activityName,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Oct 21 11:10:57 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into mini_program_home_ad (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'home_id'.toString() == column.value">
          #{item.homeId,jdbcType=VARCHAR}
        </if>
        <if test="'img_url'.toString() == column.value">
          #{item.imgUrl,jdbcType=VARCHAR}
        </if>
        <if test="'activity_id'.toString() == column.value">
          #{item.activityId,jdbcType=VARCHAR}
        </if>
        <if test="'activity_name'.toString() == column.value">
          #{item.activityName,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>