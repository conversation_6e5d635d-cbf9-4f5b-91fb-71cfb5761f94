package com.chinamobile.retail.pojo.vo.miniprogram;

import lombok.Data;

import java.util.Date;

@Data
public class ActivityDataRankMiniprogramVO {


    /**
     * 排名
     */
    private Integer ranking;
    /**
     * 名称
     */
    private String name;
    /**
     * 订单数量
     */
    private Integer orderCount;
    /**
     * 订单总额
     */
    private Long orderAmount;
    /**
      * 奖项
     */
    private String awardName;
    /**
     * 奖品Id
     */
    private String awardId;
    /**
     * 奖品类型
     */
    private Integer awardType;
    /**
     * 奖品名称
     */
    private String product;
    /**
     * 积分
     */
    private Long points;
    /**
     * 积分供应商
     */
    private String supplierId;
    /**
     * 获奖时间
     */
    private Date createTime;
    /**
     * 所属省份
     */
    private String provinceName;
    /**
     * 所属地市
     */
    private String cityName;




}
