package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 库存切换记录表
 *
 * <AUTHOR>
public class InventoryCutInfo implements Serializable {
    /**
     * 主键id
     *
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    private String id;

    /**
     * 当前已切换到的库存模式名称 1：拍下减库存  2：付款减库存
     *
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    private String inventoryNowName;

    /**
     * 库存模式切换指向名称
     *
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    private String inventoryPatternName;

    /**
     * 切换时间
     *
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    private Date cutTime;

    /**
     * 操作人
     *
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    private String operator;

    /**
     * 是否终止 0：有效 1：失效
     *
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    private Boolean isCancel;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..inventory_cut_info.id
     *
     * @return the value of supply_chain..inventory_cut_info.id
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public InventoryCutInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..inventory_cut_info.id
     *
     * @param id the value for supply_chain..inventory_cut_info.id
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..inventory_cut_info.inventory_now_name
     *
     * @return the value of supply_chain..inventory_cut_info.inventory_now_name
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public String getInventoryNowName() {
        return inventoryNowName;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public InventoryCutInfo withInventoryNowName(String inventoryNowName) {
        this.setInventoryNowName(inventoryNowName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..inventory_cut_info.inventory_now_name
     *
     * @param inventoryNowName the value for supply_chain..inventory_cut_info.inventory_now_name
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public void setInventoryNowName(String inventoryNowName) {
        this.inventoryNowName = inventoryNowName;
    }

    /**
     * This method returns the value of the database column supply_chain..inventory_cut_info.inventory_pattern_name
     *
     * @return the value of supply_chain..inventory_cut_info.inventory_pattern_name
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public String getInventoryPatternName() {
        return inventoryPatternName;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public InventoryCutInfo withInventoryPatternName(String inventoryPatternName) {
        this.setInventoryPatternName(inventoryPatternName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..inventory_cut_info.inventory_pattern_name
     *
     * @param inventoryPatternName the value for supply_chain..inventory_cut_info.inventory_pattern_name
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public void setInventoryPatternName(String inventoryPatternName) {
        this.inventoryPatternName = inventoryPatternName;
    }

    /**
     * This method returns the value of the database column supply_chain..inventory_cut_info.cut_time
     *
     * @return the value of supply_chain..inventory_cut_info.cut_time
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public Date getCutTime() {
        return cutTime;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public InventoryCutInfo withCutTime(Date cutTime) {
        this.setCutTime(cutTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..inventory_cut_info.cut_time
     *
     * @param cutTime the value for supply_chain..inventory_cut_info.cut_time
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public void setCutTime(Date cutTime) {
        this.cutTime = cutTime;
    }

    /**
     * This method returns the value of the database column supply_chain..inventory_cut_info.operator
     *
     * @return the value of supply_chain..inventory_cut_info.operator
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public String getOperator() {
        return operator;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public InventoryCutInfo withOperator(String operator) {
        this.setOperator(operator);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..inventory_cut_info.operator
     *
     * @param operator the value for supply_chain..inventory_cut_info.operator
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * This method returns the value of the database column supply_chain..inventory_cut_info.is_cancel
     *
     * @return the value of supply_chain..inventory_cut_info.is_cancel
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public Boolean getIsCancel() {
        return isCancel;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public InventoryCutInfo withIsCancel(Boolean isCancel) {
        this.setIsCancel(isCancel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..inventory_cut_info.is_cancel
     *
     * @param isCancel the value for supply_chain..inventory_cut_info.is_cancel
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public void setIsCancel(Boolean isCancel) {
        this.isCancel = isCancel;
    }

    /**
     * This method returns the value of the database column supply_chain..inventory_cut_info.create_time
     *
     * @return the value of supply_chain..inventory_cut_info.create_time
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public InventoryCutInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..inventory_cut_info.create_time
     *
     * @param createTime the value for supply_chain..inventory_cut_info.create_time
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..inventory_cut_info.update_time
     *
     * @return the value of supply_chain..inventory_cut_info.update_time
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public InventoryCutInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..inventory_cut_info.update_time
     *
     * @param updateTime the value for supply_chain..inventory_cut_info.update_time
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", inventoryNowName=").append(inventoryNowName);
        sb.append(", inventoryPatternName=").append(inventoryPatternName);
        sb.append(", cutTime=").append(cutTime);
        sb.append(", operator=").append(operator);
        sb.append(", isCancel=").append(isCancel);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        InventoryCutInfo other = (InventoryCutInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getInventoryNowName() == null ? other.getInventoryNowName() == null : this.getInventoryNowName().equals(other.getInventoryNowName()))
            && (this.getInventoryPatternName() == null ? other.getInventoryPatternName() == null : this.getInventoryPatternName().equals(other.getInventoryPatternName()))
            && (this.getCutTime() == null ? other.getCutTime() == null : this.getCutTime().equals(other.getCutTime()))
            && (this.getOperator() == null ? other.getOperator() == null : this.getOperator().equals(other.getOperator()))
            && (this.getIsCancel() == null ? other.getIsCancel() == null : this.getIsCancel().equals(other.getIsCancel()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getInventoryNowName() == null) ? 0 : getInventoryNowName().hashCode());
        result = prime * result + ((getInventoryPatternName() == null) ? 0 : getInventoryPatternName().hashCode());
        result = prime * result + ((getCutTime() == null) ? 0 : getCutTime().hashCode());
        result = prime * result + ((getOperator() == null) ? 0 : getOperator().hashCode());
        result = prime * result + ((getIsCancel() == null) ? 0 : getIsCancel().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Sep 09 10:25:53 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        inventoryNowName("inventory_now_name", "inventoryNowName", "VARCHAR", false),
        inventoryPatternName("inventory_pattern_name", "inventoryPatternName", "VARCHAR", false),
        cutTime("cut_time", "cutTime", "TIMESTAMP", false),
        operator("operator", "operator", "VARCHAR", false),
        isCancel("is_cancel", "isCancel", "BIT", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Sep 09 10:25:53 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}