package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 合伙人积分表
 *
 * <AUTHOR>
public class PartnerPoint {
    /**
     * 主键
     *
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    private String id;

    /**
     * 合伙人ID，对应user_retail表
     *
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    private String partnerId;

    /**
     * 积分供应商ID
     *
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    private String supplierId;

    /**
     * 总积分
     *
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    private Long total;

    /**
     * 本月预估积分（已付款订单对应的积分）
     *
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    private Long currentMonth;

    /**
     * 可兑换积分
     *
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    private Long available;

    /**
     * 已兑换积分
     *
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    private Long redeemed;

    /**
     * 兑换中积分
     *
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    private Long paying;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    private Date updateTime;

    /**
     * 渠道 0：订单积分 1：活动积分
     *
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    private Integer channel;

    /**
     * This method returns the value of the database column supply_chain..partner_point.id
     *
     * @return the value of supply_chain..partner_point.id
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public PartnerPoint withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..partner_point.id
     *
     * @param id the value for supply_chain..partner_point.id
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..partner_point.partner_id
     *
     * @return the value of supply_chain..partner_point.partner_id
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public String getPartnerId() {
        return partnerId;
    }

    /**
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public PartnerPoint withPartnerId(String partnerId) {
        this.setPartnerId(partnerId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..partner_point.partner_id
     *
     * @param partnerId the value for supply_chain..partner_point.partner_id
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId == null ? null : partnerId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..partner_point.supplier_id
     *
     * @return the value of supply_chain..partner_point.supplier_id
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public String getSupplierId() {
        return supplierId;
    }

    /**
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public PartnerPoint withSupplierId(String supplierId) {
        this.setSupplierId(supplierId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..partner_point.supplier_id
     *
     * @param supplierId the value for supply_chain..partner_point.supplier_id
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId == null ? null : supplierId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..partner_point.total
     *
     * @return the value of supply_chain..partner_point.total
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public Long getTotal() {
        return total;
    }

    /**
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public PartnerPoint withTotal(Long total) {
        this.setTotal(total);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..partner_point.total
     *
     * @param total the value for supply_chain..partner_point.total
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public void setTotal(Long total) {
        this.total = total;
    }

    /**
     * This method returns the value of the database column supply_chain..partner_point.current_month
     *
     * @return the value of supply_chain..partner_point.current_month
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public Long getCurrentMonth() {
        return currentMonth;
    }

    /**
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public PartnerPoint withCurrentMonth(Long currentMonth) {
        this.setCurrentMonth(currentMonth);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..partner_point.current_month
     *
     * @param currentMonth the value for supply_chain..partner_point.current_month
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public void setCurrentMonth(Long currentMonth) {
        this.currentMonth = currentMonth;
    }

    /**
     * This method returns the value of the database column supply_chain..partner_point.available
     *
     * @return the value of supply_chain..partner_point.available
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public Long getAvailable() {
        return available;
    }

    /**
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public PartnerPoint withAvailable(Long available) {
        this.setAvailable(available);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..partner_point.available
     *
     * @param available the value for supply_chain..partner_point.available
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public void setAvailable(Long available) {
        this.available = available;
    }

    /**
     * This method returns the value of the database column supply_chain..partner_point.redeemed
     *
     * @return the value of supply_chain..partner_point.redeemed
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public Long getRedeemed() {
        return redeemed;
    }

    /**
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public PartnerPoint withRedeemed(Long redeemed) {
        this.setRedeemed(redeemed);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..partner_point.redeemed
     *
     * @param redeemed the value for supply_chain..partner_point.redeemed
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public void setRedeemed(Long redeemed) {
        this.redeemed = redeemed;
    }

    /**
     * This method returns the value of the database column supply_chain..partner_point.paying
     *
     * @return the value of supply_chain..partner_point.paying
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public Long getPaying() {
        return paying;
    }

    /**
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public PartnerPoint withPaying(Long paying) {
        this.setPaying(paying);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..partner_point.paying
     *
     * @param paying the value for supply_chain..partner_point.paying
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public void setPaying(Long paying) {
        this.paying = paying;
    }

    /**
     * This method returns the value of the database column supply_chain..partner_point.create_time
     *
     * @return the value of supply_chain..partner_point.create_time
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public PartnerPoint withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..partner_point.create_time
     *
     * @param createTime the value for supply_chain..partner_point.create_time
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..partner_point.update_time
     *
     * @return the value of supply_chain..partner_point.update_time
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public PartnerPoint withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..partner_point.update_time
     *
     * @param updateTime the value for supply_chain..partner_point.update_time
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..partner_point.channel
     *
     * @return the value of supply_chain..partner_point.channel
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public Integer getChannel() {
        return channel;
    }

    /**
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public PartnerPoint withChannel(Integer channel) {
        this.setChannel(channel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..partner_point.channel
     *
     * @param channel the value for supply_chain..partner_point.channel
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", partnerId=").append(partnerId);
        sb.append(", supplierId=").append(supplierId);
        sb.append(", total=").append(total);
        sb.append(", currentMonth=").append(currentMonth);
        sb.append(", available=").append(available);
        sb.append(", redeemed=").append(redeemed);
        sb.append(", paying=").append(paying);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", channel=").append(channel);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        PartnerPoint other = (PartnerPoint) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getPartnerId() == null ? other.getPartnerId() == null : this.getPartnerId().equals(other.getPartnerId()))
            && (this.getSupplierId() == null ? other.getSupplierId() == null : this.getSupplierId().equals(other.getSupplierId()))
            && (this.getTotal() == null ? other.getTotal() == null : this.getTotal().equals(other.getTotal()))
            && (this.getCurrentMonth() == null ? other.getCurrentMonth() == null : this.getCurrentMonth().equals(other.getCurrentMonth()))
            && (this.getAvailable() == null ? other.getAvailable() == null : this.getAvailable().equals(other.getAvailable()))
            && (this.getRedeemed() == null ? other.getRedeemed() == null : this.getRedeemed().equals(other.getRedeemed()))
            && (this.getPaying() == null ? other.getPaying() == null : this.getPaying().equals(other.getPaying()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getChannel() == null ? other.getChannel() == null : this.getChannel().equals(other.getChannel()));
    }

    /**
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPartnerId() == null) ? 0 : getPartnerId().hashCode());
        result = prime * result + ((getSupplierId() == null) ? 0 : getSupplierId().hashCode());
        result = prime * result + ((getTotal() == null) ? 0 : getTotal().hashCode());
        result = prime * result + ((getCurrentMonth() == null) ? 0 : getCurrentMonth().hashCode());
        result = prime * result + ((getAvailable() == null) ? 0 : getAvailable().hashCode());
        result = prime * result + ((getRedeemed() == null) ? 0 : getRedeemed().hashCode());
        result = prime * result + ((getPaying() == null) ? 0 : getPaying().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getChannel() == null) ? 0 : getChannel().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Jul 17 14:30:45 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        partnerId("partner_id", "partnerId", "VARCHAR", false),
        supplierId("supplier_id", "supplierId", "VARCHAR", false),
        total("total", "total", "BIGINT", false),
        currentMonth("current_month", "currentMonth", "BIGINT", false),
        available("available", "available", "BIGINT", false),
        redeemed("redeemed", "redeemed", "BIGINT", false),
        paying("paying", "paying", "BIGINT", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        channel("channel", "channel", "INTEGER", false);

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Jul 17 14:30:45 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}