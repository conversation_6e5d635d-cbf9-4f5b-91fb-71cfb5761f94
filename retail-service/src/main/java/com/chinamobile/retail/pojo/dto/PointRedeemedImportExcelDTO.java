package com.chinamobile.retail.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合伙人积分管理列表表项
 * <AUTHOR>
 */
@ExcelTarget("point_operate")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PointRedeemedImportExcelDTO {
    /**客户经理姓名*/
    @Excel(name = "客户经理姓名",orderNum = "1")
    private String name;

    /**客户经理电话*/
    @Excel(name = "客户经理电话",orderNum = "2")
    private String phone;

    /**客户经理省份*/
    @Excel(name = "客户经理省份",orderNum = "3")
    private String province;

    /**客户经理地市*/
    @Excel(name = "客户经理地市",orderNum = "4")
    private String city;

    /**积分供应商*/
    @Excel(name = "积分供应商",orderNum = "5")
    private String supplier;

    /**发放积分*/
    @Excel(name = "线下发放积分",orderNum = "6",type = 10)
    private BigDecimal redeemed;

    /**可兑换积分*/
    @Excel(name = "积分发放时间",orderNum = "7")
    private Date time;
}
