package com.chinamobile.retail.pojo.param;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/4
 * @description 极客小程序订单查询类
 */
@Data
public class OrderInfoMiniProgramParam extends BasePageQuery {
    /**
     * 0-全部，1-待发货，2-待收货，3-交易成功，4-交易关闭
     */
    private Integer type;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 客户经理/渠道经理等用户操作员电话
     */
    private String custMgPhone;

    /**
     * 分销员编码
     */
    private String custCode;

    /**
     * 渠道商number
     */
    private String agentNumber;

    /**
     * 渠道商手机号
     */
    private String agentPhone;

    /**
     * 客户经理的操作员编码
     */
    private String createOprCode;

    /**
     * 搜索关键字
     */
    private String key;

    /**
     * 角色类型
     */
    private String roleType;

    /**
     * 用户ID（商城）
     */
    private String userId;
}
