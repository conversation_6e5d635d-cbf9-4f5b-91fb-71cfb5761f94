package com.chinamobile.retail.pojo.param.miniprogram;

import com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsTemplateQuestion;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 小程序场景需求模版
 *
 * <AUTHOR>
@Data
public class TemplateParam implements Serializable {
    /**
     * 主键

     */
    private String id;

    /**
     * 模版名称
     */
    @NotBlank(message = "模版名称不能为空")
    private String name;


    /**
     * 问题列表
     *
     */
    @NotEmpty(message = "问题列表不能为空")
    private List<MiniProgramSceneRequirementsTemplateQuestion> questions;
}