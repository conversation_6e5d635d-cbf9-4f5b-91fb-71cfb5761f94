package com.chinamobile.retail.pojo.param.unionpay;

import lombok.Data;

import java.util.List;

@Data
public class UploadOrderParam {

    //企业id
    private String company_id;

    //支付渠道：1：招商银行 2：支付宝 4：支付宝ISV 5：微信支付 6：工商银行
    private String payment_gateway = "1";

    //参考批次号，最大长度为64位字符串,全局唯一
    private String shop_batch_number;

    //回调地址
    private String notify_url;

    //orderItemList加密后的字符串
    private String orders;

    //订单列表
    private List<OrderItem> orderItemList;

    @Data
    public static class OrderItem{

        //业务参考订单号，最大长度为64位，同一批次内不可重复
        private String shop_order_number;

        //收款数据来源；1：接口推送，2绑定信息
        private String payee_data_source = "2";

        //手机号
        private String phone;

        //支付金额，单位：元，保留两位小数
        private String amount;

        //到账通道： 1：银行卡 2：支付宝 3：微信钱包
        private String payment_channel = "1";

    }

}
