package com.chinamobile.retail.pojo.param.miniprogram;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 16:23
 * @description TODO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityUserParam {

    @NotBlank(message = "用户id不能为空")
    private String userId;

    @NotBlank(message = "手机号码不能为空")
    private String phone;

}
