package com.chinamobile.retail.pojo.vo.miniprogram;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2025/4/7 09:52
 */
@Data
public class HomeSearchVO {

//   ======= 产品 ===========
    private String spuCode;

    private String spuName;

    private String spuRemark;

    private String image;

    private Long price;

    @JSONField(deserialize = false)
    private String subSaleLabel;

    @JSONField(deserialize = false)
    private String mainSaleLabel;

    private List<String> mainLabelList;

    private List<String> subLabelList;

    private Long amount;

    private String coreComponentName;

    private String coreComponentImg;
//   ======= 产品 ===========

//   ======= 活动 ===========
    /**
     * 活动id
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动图片
     */
    private String listImg;

    /**
     * 活动类型 1-排位赛，2-即客周周乐
     */
    private Integer activityType;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date stopTime;

    /**
     * 活动状态 0-待发布，1-待开始，2-进行中，3-结算中，4-已结束
     */
    private Integer status;

    /**
     * 是否参与
     */
    private Boolean isParticipate;
//   ======= 活动 ===========


//   ======= 资讯/素材/知识库 ===========
    private String infoId;

    private String infoName;

    //发布区域。 1-资讯中心 2-营销素材 4-知识库
    private Integer category;

    /**
     * 资讯头图url
     *
     */
    private String headImgUrl1;

    /**
     * 营销素材头图url
     *
     */
    private String headImgUrl2;

    private Date infoCreateTime;

    private String infoImage;

    private String infoContent;

    /**
     * 素材类型 1-图文素材 2-视频素材
     */
    private Integer contentType;
//   ======= 资讯/素材/知识库 ===========

}
