package com.chinamobile.retail.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class ExportDistributeOrderDTO {

    @Excel(name = "一级分销员电话")
    private String distributorPhoneOne;

    @Excel(name = "二级分销员电话")
    private String distributorPhoneTwo;

    @Excel(name = "操作员编码")
    private String createOperCode;

    @Excel(name = "操作员省工号")
    private String employeeNum;

    @Excel(name = "操作员姓名")
    private String custMgName;

    @Excel(name = "操作员电话")
    private String custMgPhone;

    @Excel(name = "下单时间")
    private String createTime;

    @Excel(name = "订单号")
    private String orderId;
}
