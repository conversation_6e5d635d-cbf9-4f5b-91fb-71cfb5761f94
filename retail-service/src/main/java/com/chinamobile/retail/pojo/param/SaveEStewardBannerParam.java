package com.chinamobile.retail.pojo.param;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class SaveEStewardBannerParam {

    private String id;

    // banner名称
    @NotEmpty(message = "banner名称不能为空")
    private String name;

    // 图片链接
    @NotEmpty(message = "图片链接不能为空")
    private String imgUrl;

    // 一级目录id
    private String firstDirectoryId;

    // 二级目录id
    @NotEmpty(message = "二级目录id不能为空")
    private String secondDirectoryId;

    // 展示类型 1外链 2商品详情页
    @NotNull(message = "展示类型不能为空")
    @Range(min = 1, max = 2, message = "展示类型只能是1,2")
    private Integer type;

    //商品编码
    private String spuCode;

    // 商品详情页链接
    private String productUrl;


}
