package com.chinamobile.retail.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/31
 * @description 审核用户头像参数类
 */
@Data
public class AuditUserHeaderParam {

    /**
     * 申请头像id
     */
    @NotEmpty(message = "头像id不能为空")
    private String id;

    /**
     * 审批状态 1--未审批 2--审批通过  3--审批未通过
     */
    @NotNull(message = "审批状态不能为空")
    private Integer auditStatus;

    /**
     * 审批原因
     */
    private String auditReason;
}
