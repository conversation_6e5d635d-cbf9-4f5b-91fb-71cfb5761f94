package com.chinamobile.retail.pojo.vo.miniprogram;

import com.chinamobile.retail.pojo.param.miniprogram.ActivityUserParam;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/25 09:34
 * @description TODO
 */
@Data
public class ActivityExtraWeeklyVO {

    /**
     * 活动规则
     */
    private String rule;

    /**
     * 活动说明
     */
    private String description;

    /**
     * 参与方式
     */
    private String participation;

    /**
     * 轮盘等分数量
     */
    private Integer wheelParts;

    /**
     * 订单开始时间
     */
    private Date orderStart;

    /**
     * 订单结束时间
     */
    private Date orderStop;

    /**
     * 参加活动需要完成的订单数量
     */
    private Integer orderCount;

    /**
     * 销售人员注册起始时间
     */
    private Date registerStart;

    /**
     * 销售人员注册结束时间
     */
    private Date registerStop;

    /**
     * 最大参与人数
     */
    private Long maxPlayer;
    /**
     * 标语
     */
    private String slogan;

    /**
     * 参与用户
     */
    private List<ActivityUserParam> activityUsers;

    /**
     * 达标奖励
     */
    private List<ActivityExtraWeeklyAwardVO> awards;
}
