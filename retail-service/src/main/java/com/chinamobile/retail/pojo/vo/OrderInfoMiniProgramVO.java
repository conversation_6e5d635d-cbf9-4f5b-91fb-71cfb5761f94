package com.chinamobile.retail.pojo.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/7 15:31
 * @description 订单信息
 */
@Data
public class OrderInfoMiniProgramVO {

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 下单时间
     */
    private String createTime;

    /**
     * 商品图片
     */
    private String imgUrl;

    /**
     * 商品名称
     */
    private String spuOfferingName;

    /**
     * 价格
     */
    private Integer price;

    /**
     * 价格
     */
    private String priceStr;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 描述
     */
    private String desc;

    /**
     * 订单状态
     * 0:订单创建
     * 1:订单验收（个人客户确认收货时，同步本状态）
     * 3:订单计收;（订单同步至CMIoT成功后，同步本状态）
     * 4.订单退款完成
     */
    private Integer status;

    /**状态中文字符串*/
    private String statusStr;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 订单状态变更时间
     */
    private Date orderStatusTime;
}
