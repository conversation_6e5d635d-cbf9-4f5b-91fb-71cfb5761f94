package com.chinamobile.retail.service.impl;

import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.SdkClientException;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import com.amazonaws.services.s3.transfer.Download;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import com.amazonaws.services.s3.transfer.Upload;
import com.amazonaws.services.s3.transfer.model.UploadResult;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.*;
import com.chinamobile.retail.config.OneNetStorageConfig;
import com.chinamobile.retail.config.ThreadPoolConfig;
import com.chinamobile.retail.service.BaseOssService;
import com.chinamobile.retail.util.qiniu.Utils;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.common.utils.BaseServiceUtils.doFailure;
import static com.chinamobile.iot.sc.common.utils.BaseServiceUtils.doSuccess;
import static com.chinamobile.retail.exception.StatusContant.OSS_DEL_OR_SETEXPIRED_ERROR;
import static com.chinamobile.retail.exception.StatusContant.OSS_UPLOAD_ERROR;


/**
 * OneNet对象存储服务  默认开启OneNet对象存储
 */
@Service
@ConditionalOnProperty(value = "storage.platform", havingValue = "OneNet")
@Slf4j
public class OneNetObjectStorageService extends BaseOssService {

    @Autowired
    private OneNetStorageConfig config;
    @Autowired
    private ThreadPoolConfig poolConfig;

    private AmazonS3 client;
    private TransferManager transferManager;


    @PostConstruct
    public void init() {
        this.client = getAmazonClient();
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(poolConfig.getCodeSize(),
                poolConfig.getMaxSize(), poolConfig.getKeepAliveTime(), TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(poolConfig.getQueueSize()),
                new ThreadFactoryBuilder().setNameFormat("handler-pool-%d").build(),
                (r, executor) -> {
                    try {
                        TimeUnit.MILLISECONDS.sleep(poolConfig.getRejectedTime());
                        executor.execute(r);
                    } catch (Throwable e) {
                        log.warn("线程池拒绝策略中发生运行时异常", e);
                    }
                });

        this.transferManager = buildTransferManager(client, threadPoolExecutor);
    }

    @Override
    public BaseAnswer<UpResult> uploadFile(File file, String fname, boolean isCover, int expiredDay) throws Exception {
        try {
            Upload upload = transferManager.upload(buildFileRequest(fname, file));
            return doSuccess(doResult(fname, upload, expiredDay));
        } catch (AmazonClientException e) {
            log.error("oneNet 对象存储失败：", e);
            return doFailure(OSS_UPLOAD_ERROR);
        }
    }

    @Override
    public BaseAnswer<UpResult> uploadByte(ByteArrayUpload data) throws Exception {
        try {
            String fname = data.getFileName();
            String key = fname;
            byte[] bytes = data.getBytes();
            if (!data.isCover()) {
                key = Utils.getKey(bytes, fname);
            }
            try (InputStream in = new ByteArrayInputStream(bytes)) {
                Upload upload = transferManager.upload(buildPutRequest(key, in));
                return doSuccess(doResult(fname, upload, data.getExpiredDay()));
            }
        } catch (AmazonClientException e) {
            log.error("oneNet 对象存储失败：", e);
            return doFailure(OSS_UPLOAD_ERROR);
        }
    }

    @Override
    public BaseAnswer<UpResult> uploadBase64(Base64Upload base64) throws Exception {
        final String[] info = base64.getBase64().split(",");
        byte[] data = Base64.getDecoder().decode(info.length == 2 ? info[1] : info[0]);
        return uploadByte(new ByteArrayUpload(data, base64.getFileName(), base64.isCover(), base64.getExpiredDay()));
    }

    @Override
    public BaseAnswer<DelResult> delete(String... keys) throws Exception {
        BaseAnswer<DelResult> answer = new BaseAnswer<>();
        DelResult delResult = new DelResult();
        try {
            DeleteObjectsRequest request = new DeleteObjectsRequest(config.getBucketName());
            request.withKeys(keys);
            DeleteObjectsResult result = client.deleteObjects(request);

            List<String> successKeys = result.getDeletedObjects().stream()
                    .map(DeleteObjectsResult.DeletedObject::getKey)
                    .collect(Collectors.toList());
            List<String> failKeys = Arrays.stream(keys).filter(key -> !successKeys.contains(key)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(failKeys)) {
                delResult.setFailKeys(failKeys);
                answer.setStatus(OSS_DEL_OR_SETEXPIRED_ERROR);
            }
        } catch (MultiObjectDeleteException e) {
            List<MultiObjectDeleteException.DeleteError> errors = e.getErrors();
            delResult.setFailKeys(errors.stream()
                    .map(MultiObjectDeleteException.DeleteError::getKey)
                    .collect(Collectors.toList()));
            answer.setStatus(OSS_DEL_OR_SETEXPIRED_ERROR);
            log.error("删除对象存储数据失败：", e);
        } catch (AmazonServiceException e) {
            log.error("删除对象存储数据失败：", e);
            answer.setStatus(OSS_DEL_OR_SETEXPIRED_ERROR);
        } catch (SdkClientException e) {
            log.error("删除对象存储数据失败：", e);
            answer.setStatus(OSS_DEL_OR_SETEXPIRED_ERROR);
        }
        return answer.setData(delResult);
    }


    /**
     * 删除path文件夹以文件夹中所有文件
     *
     * @param path
     */
    public void deleteCacheDir(String path) {
        String bucketName = config.getBucketName();
        log.info("bucketName = {}", bucketName);
        for (S3ObjectSummary file : client.listObjects(bucketName, path).getObjectSummaries()) {
            log.info("key = {}, storageClass = {}; ", file.getKey(), file.getStorageClass());
            client.deleteObject(config.getBucketName(), file.getKey());
        }
    }


    public void deleteDir(String bucket, String path) {

        for (S3ObjectSummary file : client.listObjects(bucket, path).getObjectSummaries()) {
            log.info("key = {}, storageClass = {}; ", file.getKey(), file.getStorageClass());
            client.deleteObject("mallos-test", file.getKey());
        }


//        ListObjectsRequest listObjectsRequest = new ListObjectsRequest();
//        listObjectsRequest.setBucketName("mallos-test");
//        listObjectsRequest.setPrefix("testOs");
//        ObjectListing listing1 = client.listObjects("testOs");
//        log.info("listing1 prefix = {}",listing1.getPrefix());
//        ObjectListing listing2 = client.listObjects("mallos-test","testOs");
//
//        ObjectListing listing3 = client.listObjects(listObjectsRequest);
//
//        client.deleteObject("mallos-test",path);

    }


    @Override
    public BaseAnswer<DelResult> deleteAfterDays(int days, String... keys) throws Exception {
        log.info("OneNetObStorageServ deleteAfterDays Enter days = {}", days);
        DelResult delResult = new DelResult();
        BaseAnswer<DelResult> answer = new BaseAnswer<>();
        for (String key : keys) {
            CopyObjectRequest request = new CopyObjectRequest(config.getBucketName(), key, config.getBucketName(), key);
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setExpirationTime(DateUtils.addDays(new Date(), days));
            //           metadata.setExpirationTime(DateUtils.addMinutes(new Date(), 5));
            request.setCannedAccessControlList(CannedAccessControlList.PublicRead);
            request.setNewObjectMetadata(metadata);
            try {
                client.copyObject(request);
            } catch (SdkClientException e) {
                log.error("删除对象存储数据失败：", e);
                delResult.setFailKey(key);
                answer.setStatus(OSS_DEL_OR_SETEXPIRED_ERROR);
            }
        }
        return answer.setData(delResult);
    }


    public void downloadFile(String fileKey, String filePath) throws Exception {

        File file = new File(filePath);
        Download download = transferManager.download(config.getBucketName(), fileKey, file);
        download.waitForCompletion();
        Double progress = download.getProgress().getPercentTransferred();
        log.info("downloadFile progress = {}", progress);
    }


    @Override
    public BaseAnswer<QueryResult> getUrl(String key) {
        //URL url = client.getUrl(config.getBucketName(), key);
        // final String strUrl = url.toString();
        QueryResult ret = new QueryResult();
        ret.setInnerUrl(config.getQueryHttpInner(key));
        ret.setOuterUrl(config.getQueryHttpOuter(key));
        return doSuccess(ret);
    }

    @Override
    public BaseAnswer<UpResult> copy(CopyUpload copyUpload) throws Exception {
        CopyObjectRequest copyObjectRequest = new CopyObjectRequest(config.getBucketName(), copyUpload.getFromKey(),
                config.getBucketName(), copyUpload.getToKey())
                .withCannedAccessControlList(CannedAccessControlList.PublicRead);
        CopyObjectResult result = client.copyObject(copyObjectRequest);
        return doSuccess(doResult(copyUpload.getToKey(), copyUpload.getExpiredDay(), copyUpload.getToKey()));
    }

    private UpResult doResult(String fname, Upload upload, int expiredDay) throws Exception {
        UploadResult uploadResult = upload.waitForUploadResult();
        UpResult result = doResult(fname, expiredDay, uploadResult.getKey());
        return result;
    }

    private UpResult doResult(String fname, int expiredDay, String key) throws Exception {
        //URL url = client.getUrl(config.getBucketName(), key);
        //final String strUrl = url.toString();
        UpResult result = new UpResult();
        result.setKey(key);
        result.setFileName(fname);
        result.setInnerUrl(config.getQueryHttpInner(key));
        result.setOuterUrl(config.getQueryHttpOuter(key));
        //设置过期
        if (expiredDay > 0) {
            deleteAfterDays(expiredDay, key);
        }
        return result;
    }

    /**
     * 返回新的S3客户端对象 {@link AmazonS3}
     *
     * @return AmazonS3 Client
     */
    private AmazonS3 getAmazonClient() {
        if (client == null) {
            AWSCredentials credentials = new BasicAWSCredentials(config.getAccessKey(), config.getSecretKey());
            AmazonS3 amazonS3client = AmazonS3ClientBuilder
                    .standard()
                    .withCredentials(new AWSStaticCredentialsProvider(credentials))
                    .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(config.getEndpoint(), null))
                    .withPathStyleAccessEnabled(true)
                    .build();
            this.client = amazonS3client;
            //跨域设置
            BucketCrossOriginConfiguration crossOriginConfiguration = new BucketCrossOriginConfiguration();
            ArrayList<CORSRule> putCorsRules = new ArrayList<CORSRule>();
            CORSRule corRule = new CORSRule();
            ArrayList<String> allowedOrigin = new ArrayList<String>(); //指定允许跨域请求的来源
            allowedOrigin.add("*"); //通配符
            ArrayList<CORSRule.AllowedMethods> allowedMethod = new ArrayList<CORSRule.AllowedMethods>(); //指定允许的跨域请求方法(GET/PUT/DELETE/POST/HEAD)
            allowedMethod.add(CORSRule.AllowedMethods.GET);
            allowedMethod.add(CORSRule.AllowedMethods.PUT);
            allowedMethod.add(CORSRule.AllowedMethods.DELETE);
            allowedMethod.add(CORSRule.AllowedMethods.HEAD);
            allowedMethod.add(CORSRule.AllowedMethods.POST);
            ArrayList<String> allowedHeader = new ArrayList<String>(); //控制在OPTIONS预取指令中Access-Control-Request-Headers头中指定的header是否允许。
            allowedHeader.add("*");
            corRule.setAllowedMethods(allowedMethod);
            corRule.setAllowedOrigins(allowedOrigin);
            corRule.setAllowedHeaders(allowedHeader);
            //指定浏览器对特定资源的预取(OPTIONS)请求返回结果的缓存时间,单位为秒。获取CORS规则 删除CORS
            corRule.setMaxAgeSeconds(100);
            putCorsRules.add(corRule);
            crossOriginConfiguration.setRules(putCorsRules);
            client.setBucketCrossOriginConfiguration(config.getBucketName(), crossOriginConfiguration);
        }
        return client;
    }

    /**
     * 使用已有的{@link AmazonS3}构造{@link TransferManager}
     *
     * @param client             AmazonS3客户端
     * @param threadPoolExecutor
     * @return TransferManager
     */
    private TransferManager buildTransferManager(AmazonS3 client, ThreadPoolExecutor threadPoolExecutor) {
        return TransferManagerBuilder.standard().withS3Client(client).withExecutorFactory(() -> threadPoolExecutor).build();
    }

    private PutObjectRequest buildPutRequest(String key, InputStream in) throws IOException {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(in.available());
        PutObjectRequest request = new PutObjectRequest(config.getBucketName(), key, in, metadata);
        request.setCannedAcl(CannedAccessControlList.PublicRead);
        return request;
    }
    //

    private PutObjectRequest buildFileRequest(String key, File file) throws IOException {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(file.length());
        metadata.setRestoreExpirationTime(new Date(System.currentTimeMillis() + 20000));
        PutObjectRequest request = new PutObjectRequest(config.getBucketName(), key, file);
        request.withCannedAcl(CannedAccessControlList.PublicRead).withMetadata(metadata);
        return request;
    }

    /*public static void main(String[] agrs){
        System.out.println(new Date().toGMTString());
        System.out.println(new Date().toString());
    }*/
    public String getStorageDomain(String fileKey) {
        return config.getQueryHttpOuter(fileKey);
    }


    // 新增方法：创建文件夹
    @Override
    public BaseAnswer<Void> createFolder(String folderPath) {
        try {
            // 规范化文件夹路径
            if (!folderPath.endsWith("/")) {
                folderPath += "/";
            }

            // 创建零字节文件夹标记对象
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(0);
            client.putObject(
                    config.getBucketName(),
                    folderPath,
                    new ByteArrayInputStream(new byte[0]),
                    metadata
            );
            return doSuccess(null);
        } catch (AmazonServiceException e) {
            log.error("创建文件夹失败（服务异常）: {}", folderPath, e);
            return doFailure(OSS_UPLOAD_ERROR);
        } catch (SdkClientException e) {
            log.error("创建文件夹失败（客户端异常）: {}", folderPath, e);
            return doFailure(OSS_UPLOAD_ERROR);
        }
    }

    // 增强文件夹删除方法
    @Override
    public BaseAnswer<Void> deleteFolder(String folderPath) {
        try {
            // 规范化路径
            if (!folderPath.endsWith("/")) {
                folderPath += "/";
            }

            ListObjectsV2Request listRequest = new ListObjectsV2Request()
                    .withBucketName(config.getBucketName())
                    .withPrefix(folderPath);

            ListObjectsV2Result listingResult;
            do {
                listingResult = client.listObjectsV2(listRequest);

                if (listingResult.getObjectSummaries().isEmpty()) {
                    break;
                }

                // 批量删除
                List<DeleteObjectsRequest.KeyVersion> keys = listingResult.getObjectSummaries()
                        .stream()
                        .map(obj -> new DeleteObjectsRequest.KeyVersion(obj.getKey()))
                        .collect(Collectors.toList());

                DeleteObjectsRequest deleteRequest = new DeleteObjectsRequest(config.getBucketName())
                        .withKeys(keys)
                        .withQuiet(true);

                client.deleteObjects(deleteRequest);

                // 处理分页
                listRequest.setContinuationToken(listingResult.getNextContinuationToken());
            } while (listingResult.isTruncated());

            return doSuccess(null);
        } catch (MultiObjectDeleteException e) {
            log.error("部分文件删除失败: {}", folderPath, e);
            return doFailure(OSS_DEL_OR_SETEXPIRED_ERROR);
        } catch (AmazonServiceException e) {
            log.error("删除文件夹失败（服务异常）: {}", folderPath, e);
            return doFailure(OSS_DEL_OR_SETEXPIRED_ERROR);
        } catch (SdkClientException e) {
            log.error("删除文件夹失败（客户端异常）: {}", folderPath, e);
            return doFailure(OSS_DEL_OR_SETEXPIRED_ERROR);
        }
    }

    // 增强上传方法支持路径
    @Override
    public BaseAnswer<UpResult> uploadFilePath(File file, String targetPath, boolean isCover, int expiredDay) throws Exception {
        try {
            String key = targetPath.endsWith("/") ?
                    targetPath + file.getName() :
                    targetPath + "/" + file.getName();

            Upload upload = transferManager.upload(buildFileRequest(key, file));
            return doSuccess(doResult(file.getName(), upload, expiredDay));
        } catch (AmazonClientException e) {
            log.error("上传文件到指定路径失败: {}", targetPath, e);
            return doFailure(OSS_UPLOAD_ERROR);
        }
    }

    // 新增文件/文件夹重命名方法
    @Override
    public BaseAnswer<Void> renameObject(String sourceKey, String targetKey) {
        try {
            // 复制对象
            client.copyObject(
                    config.getBucketName(),
                    sourceKey,
                    config.getBucketName(),
                    targetKey
            );

            // 删除原对象
            client.deleteObject(config.getBucketName(), sourceKey);

            // 如果是文件夹则递归处理
            if (sourceKey.endsWith("/")) {
                ListObjectsV2Request listRequest = new ListObjectsV2Request()
                        .withBucketName(config.getBucketName())
                        .withPrefix(sourceKey);

                ListObjectsV2Result listingResult;
                do {
                    listingResult = client.listObjectsV2(listRequest);

                    for (S3ObjectSummary obj : listingResult.getObjectSummaries()) {
                        String newKey = obj.getKey().replace(sourceKey, targetKey);
                        client.copyObject(config.getBucketName(), obj.getKey(), config.getBucketName(), newKey);
                        client.deleteObject(config.getBucketName(), obj.getKey());
                    }

                    listRequest.setContinuationToken(listingResult.getNextContinuationToken());
                } while (listingResult.isTruncated());
            }

            return doSuccess(null);
        } catch (AmazonServiceException e) {
            log.error("重命名操作失败（服务异常）: {} -> {}", sourceKey, targetKey, e);
            return doFailure(OSS_UPLOAD_ERROR);
        } catch (SdkClientException e) {
            log.error("重命名操作失败（客户端异常）: {} -> {}", sourceKey, targetKey, e);
            return doFailure(OSS_UPLOAD_ERROR);
        }
    }
}
