package com.chinamobile.retail.service;

import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.miniprogram.*;

import java.util.List;
import com.chinamobile.retail.pojo.vo.miniprogram.HomeSearchVO;
import com.chinamobile.retail.pojo.vo.miniprogram.HomeVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 09:43
 * @description TODO
 */
public interface IMiniHomeService {

    HomeVO getHomeDetail(String homeId, LoginIfo4Redis loginIfo4Redis);

    PageData<HomeVO> pageHomeList(PageHomeListParam param, LoginIfo4Redis loginIfo4Redis);

    HomeVO getHomeMini(String userId,String provinceCode);

    void create(HomeParam param, String userId);
    void edit(HomeParam param, String userId);

    void audit(InfoAuditParam param, LoginIfo4Redis loginIfo4Redis);

    void offline(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis);

    void publish(HomePublishParam param, LoginIfo4Redis loginIfo4Redis);

    void loadHome2Redis();
    void delete(String id);

    void migrateSpu();

    List<ActivityProvinceVO> getAllRegions();

    PageData<HomeSearchVO> search(HomeSearchParam param);
}
