package com.chinamobile.retail.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.param.EStewardBannerListParam;
import com.chinamobile.retail.pojo.param.EStewardBannerOnOfflineParam;
import com.chinamobile.retail.pojo.param.SaveEStewardBannerParam;
import com.chinamobile.retail.pojo.vo.EStewardBannerDetailVO;
import com.chinamobile.retail.pojo.vo.EStewardBannerListVO;

import javax.validation.Valid;

public interface EStewardBannerService {
    BaseAnswer<Void> saveBanner(@Valid SaveEStewardBannerParam param, String userId);

    BaseAnswer<EStewardBannerDetailVO> getDetail(String id);

    BaseAnswer<PageData<EStewardBannerListVO>> bannerList(EStewardBannerListParam param);

    BaseAnswer<Void> onOffline(@Valid EStewardBannerOnOfflineParam param);

    BaseAnswer<Void> delete(String id);
}
