package com.chinamobile.retail.dao.ext;

import com.chinamobile.retail.pojo.vo.miniprogram.SaleYearReportVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


@Mapper
public interface UserMinProgramMapperExt {

    List<SaleYearReportVO> selectSaleYearReportBasic(Integer year);

    List<SaleYearReportVO> selectSaleYearReportOrder(Integer year);

    List<SaleYearReportVO> selectSaleYearReportOrderPrice(Integer year);

    List<SaleYearReportVO> selectSaleYearReportSpu(Integer year);

    List<SaleYearReportVO> selectSaleYearReportSpuGroup(Integer year);

    List<SaleYearReportVO> selectSaleYearReportSpuIndividual(Integer year);

    List<SaleYearReportVO> selectSaleYearReportLastOrderTime(Integer year);

    List<SaleYearReportVO> selectSaleYearReportCustomerCount(Integer year);
}
