package com.chinamobile.retail.dao.ext;

import com.chinamobile.retail.pojo.entity.SpuSkuAttachment;
import com.chinamobile.retail.pojo.param.miniprogram.PageCoreComponentListParam;
import com.chinamobile.retail.pojo.param.miniprogram.PageHomeListParam;
import com.chinamobile.retail.pojo.vo.MiniProgramProductListVO;
import com.chinamobile.retail.pojo.vo.miniprogram.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 */
@Mapper
public interface SpuCoreComponentMapperExt {

    List<SpuCoreComponentVO> pageCoreComponentList(PageCoreComponentListParam param);

    Long countCoreComponentList(PageCoreComponentListParam param);

    String getUserName(@Param("userId") String userId);

    SpuCoreComponentVO getCoreComponentDetail(@Param("spuCode") String spuCode);

    List<SkuCoreComponentVO> getSkuCoreComponentList(@Param("spuCode") String spuCode);
    List<SpuSkuAttachment> getSkuCoreComponentAttachmentList(@Param("spuCode") String spuCode, @Param("skuCode") String skuCode);

    List<SkuCoreComponentVO> searchSku(@Param("keyword") String keyword);

    List<SpuCoreComponentVO> searchSpu(@Param("keyword") String keyword);

    List<SkuReleaseTargetVO> getSkuReleaseTargetList(@Param("skuList") List<String> skuList);
}
