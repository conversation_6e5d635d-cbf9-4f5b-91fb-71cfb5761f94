package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.ShopCustomerInfo;
import com.chinamobile.retail.pojo.entity.ShopCustomerInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ShopCustomerInfoMapper {
    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    long countByExample(ShopCustomerInfoExample example);

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    int deleteByExample(ShopCustomerInfoExample example);

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    int insert(ShopCustomerInfo record);

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    int insertSelective(ShopCustomerInfo record);

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    List<ShopCustomerInfo> selectByExample(ShopCustomerInfoExample example);

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    ShopCustomerInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    int updateByExampleSelective(@Param("record") ShopCustomerInfo record, @Param("example") ShopCustomerInfoExample example);

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    int updateByExample(@Param("record") ShopCustomerInfo record, @Param("example") ShopCustomerInfoExample example);

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    int updateByPrimaryKeySelective(ShopCustomerInfo record);

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    int updateByPrimaryKey(ShopCustomerInfo record);

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    int batchInsert(@Param("list") List<ShopCustomerInfo> list);

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    int batchInsertSelective(@Param("list") List<ShopCustomerInfo> list, @Param("selective") ShopCustomerInfo.Column ... selective);
}