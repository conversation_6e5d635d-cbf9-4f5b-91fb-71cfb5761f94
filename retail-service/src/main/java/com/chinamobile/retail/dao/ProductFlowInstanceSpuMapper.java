package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.ProductFlowInstanceSpu;
import com.chinamobile.retail.pojo.entity.ProductFlowInstanceSpuExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProductFlowInstanceSpuMapper {
    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    long countByExample(ProductFlowInstanceSpuExample example);

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    int deleteByExample(ProductFlowInstanceSpuExample example);

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    int insert(ProductFlowInstanceSpu record);

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    int insertSelective(ProductFlowInstanceSpu record);

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    List<ProductFlowInstanceSpu> selectByExample(ProductFlowInstanceSpuExample example);

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    ProductFlowInstanceSpu selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    int updateByExampleSelective(@Param("record") ProductFlowInstanceSpu record, @Param("example") ProductFlowInstanceSpuExample example);

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    int updateByExample(@Param("record") ProductFlowInstanceSpu record, @Param("example") ProductFlowInstanceSpuExample example);

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    int updateByPrimaryKeySelective(ProductFlowInstanceSpu record);

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    int updateByPrimaryKey(ProductFlowInstanceSpu record);

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    int batchInsert(@Param("list") List<ProductFlowInstanceSpu> list);

    /**
     *
     * @mbg.generated Tue Jun 04 15:44:18 CST 2024
     */
    int batchInsertSelective(@Param("list") List<ProductFlowInstanceSpu> list, @Param("selective") ProductFlowInstanceSpu.Column ... selective);
}