package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramActivityRankAward;
import com.chinamobile.retail.pojo.entity.MiniProgramActivityRankAwardExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramActivityRankAwardMapper {
    /**
     *
     * @mbg.generated Fri Jul 26 16:07:41 CST 2024
     */
    long countByExample(MiniProgramActivityRankAwardExample example);

    /**
     *
     * @mbg.generated Fri Jul 26 16:07:41 CST 2024
     */
    int deleteByExample(MiniProgramActivityRankAwardExample example);

    /**
     *
     * @mbg.generated Fri Jul 26 16:07:41 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri Jul 26 16:07:41 CST 2024
     */
    int insert(MiniProgramActivityRankAward record);

    /**
     *
     * @mbg.generated Fri Jul 26 16:07:41 CST 2024
     */
    int insertSelective(MiniProgramActivityRankAward record);

    /**
     *
     * @mbg.generated Fri Jul 26 16:07:41 CST 2024
     */
    List<MiniProgramActivityRankAward> selectByExample(MiniProgramActivityRankAwardExample example);

    /**
     *
     * @mbg.generated Fri Jul 26 16:07:41 CST 2024
     */
    MiniProgramActivityRankAward selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri Jul 26 16:07:41 CST 2024
     */
    int updateByExampleSelective(@Param("record") MiniProgramActivityRankAward record, @Param("example") MiniProgramActivityRankAwardExample example);

    /**
     *
     * @mbg.generated Fri Jul 26 16:07:41 CST 2024
     */
    int updateByExample(@Param("record") MiniProgramActivityRankAward record, @Param("example") MiniProgramActivityRankAwardExample example);

    /**
     *
     * @mbg.generated Fri Jul 26 16:07:41 CST 2024
     */
    int updateByPrimaryKeySelective(MiniProgramActivityRankAward record);

    /**
     *
     * @mbg.generated Fri Jul 26 16:07:41 CST 2024
     */
    int updateByPrimaryKey(MiniProgramActivityRankAward record);

    /**
     *
     * @mbg.generated Fri Jul 26 16:07:41 CST 2024
     */
    int batchInsert(@Param("list") List<MiniProgramActivityRankAward> list);

    /**
     *
     * @mbg.generated Fri Jul 26 16:07:41 CST 2024
     */
    int batchInsertSelective(@Param("list") List<MiniProgramActivityRankAward> list, @Param("selective") MiniProgramActivityRankAward.Column ... selective);
}