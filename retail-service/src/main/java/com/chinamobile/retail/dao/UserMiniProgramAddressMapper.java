package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.UserMiniProgramAddress;
import com.chinamobile.retail.pojo.entity.UserMiniProgramAddressExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserMiniProgramAddressMapper {
    /**
     *
     * @mbg.generated Tue Aug 20 09:14:06 CST 2024
     */
    long countByExample(UserMiniProgramAddressExample example);

    /**
     *
     * @mbg.generated Tue Aug 20 09:14:06 CST 2024
     */
    int deleteByExample(UserMiniProgramAddressExample example);

    /**
     *
     * @mbg.generated Tue Aug 20 09:14:06 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Aug 20 09:14:06 CST 2024
     */
    int insert(UserMiniProgramAddress record);

    /**
     *
     * @mbg.generated Tue Aug 20 09:14:06 CST 2024
     */
    int insertSelective(UserMiniProgramAddress record);

    /**
     *
     * @mbg.generated Tue Aug 20 09:14:06 CST 2024
     */
    List<UserMiniProgramAddress> selectByExample(UserMiniProgramAddressExample example);

    /**
     *
     * @mbg.generated Tue Aug 20 09:14:06 CST 2024
     */
    UserMiniProgramAddress selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Aug 20 09:14:06 CST 2024
     */
    int updateByExampleSelective(@Param("record") UserMiniProgramAddress record, @Param("example") UserMiniProgramAddressExample example);

    /**
     *
     * @mbg.generated Tue Aug 20 09:14:06 CST 2024
     */
    int updateByExample(@Param("record") UserMiniProgramAddress record, @Param("example") UserMiniProgramAddressExample example);

    /**
     *
     * @mbg.generated Tue Aug 20 09:14:06 CST 2024
     */
    int updateByPrimaryKeySelective(UserMiniProgramAddress record);

    /**
     *
     * @mbg.generated Tue Aug 20 09:14:06 CST 2024
     */
    int updateByPrimaryKey(UserMiniProgramAddress record);

    /**
     *
     * @mbg.generated Tue Aug 20 09:14:06 CST 2024
     */
    int batchInsert(@Param("list") List<UserMiniProgramAddress> list);

    /**
     *
     * @mbg.generated Tue Aug 20 09:14:06 CST 2024
     */
    int batchInsertSelective(@Param("list") List<UserMiniProgramAddress> list, @Param("selective") UserMiniProgramAddress.Column ... selective);
}