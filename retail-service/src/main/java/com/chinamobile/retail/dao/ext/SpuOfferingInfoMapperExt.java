package com.chinamobile.retail.dao.ext;

import com.chinamobile.retail.pojo.mapper.ProductFrontListDO;
import com.chinamobile.retail.pojo.param.miniprogram.HomeSearchParam;
import com.chinamobile.retail.pojo.param.miniprogram.PageProductParam;
import com.chinamobile.retail.pojo.param.miniprogram.ProductDetailParam;
import com.chinamobile.retail.pojo.param.miniprogram.SearchProductParam;
import com.chinamobile.retail.pojo.param.miniprogram.WebSearchProductParam;
import com.chinamobile.retail.pojo.vo.MiniProgramProductDetailVO;
import com.chinamobile.retail.pojo.vo.MiniProgramProductListVO;
import com.chinamobile.retail.pojo.vo.MiniProgramSkuInfoVO;
import com.chinamobile.retail.pojo.vo.miniprogram.HomeSearchVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SpuOfferingInfoMapperExt {

    List<ProductFrontListDO> pageQueryProductFrontList(String provinceCode, String cityCode, String queryParam, Integer orderType, Integer orderSort, Integer start, Integer pageSize, Integer partnerRoleId);

    int pageCountProductFrontList(String provinceCode, String cityCode, String queryParam, Integer orderType, Integer orderSort, Integer start, Integer pageSize);

    List<MiniProgramProductListVO> pageMiniProgramProduct(@Param("param") PageProductParam param);
    List<MiniProgramProductListVO> searchProduct(SearchProductParam param);
    Long searchProductCount(SearchProductParam param);

    Integer countMiniProgramProduct(@Param("param") PageProductParam param);

    List<MiniProgramSkuInfoVO> getMiniProgramSkuList(@Param("param") ProductDetailParam param);
    List<MiniProgramSkuInfoVO> getMiniProgramSkuListByActivityId(@Param("param") ProductDetailParam param);

    Long getMinSkuPrice(@Param("param") ProductDetailParam param);

    MiniProgramProductDetailVO productDetail(@Param("param") ProductDetailParam param);
    MiniProgramProductDetailVO productDetailByActivityId(@Param("param") ProductDetailParam param);

    long countSkuByProvinceAndCity(@Param("spuCode") String spuCode,
                                   @Param("provinceCode") String provinceCode,
                                   @Param("cityCode") String cityCode,
                                   @Param("roleType") String roleType);

    List<MiniProgramProductListVO> webSearchProduct(@Param("param") WebSearchProductParam param);
    Long countWebSearchProduct(@Param("param") WebSearchProductParam param);

    List<MiniProgramProductListVO> webSceneSearchProduct(@Param("param") WebSearchProductParam param);
    Long countWebSceneSearchProduct(@Param("param") WebSearchProductParam param);

}
