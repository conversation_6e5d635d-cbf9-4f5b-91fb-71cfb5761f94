package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.SpuOfferingInfoHistory;
import com.chinamobile.retail.pojo.entity.SpuOfferingInfoHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SpuOfferingInfoHistoryMapper {
    /**
     *
     * @mbg.generated Mon Feb 10 17:01:23 CST 2025
     */
    long countByExample(SpuOfferingInfoHistoryExample example);

    /**
     *
     * @mbg.generated Mon Feb 10 17:01:23 CST 2025
     */
    int deleteByExample(SpuOfferingInfoHistoryExample example);

    /**
     *
     * @mbg.generated Mon Feb 10 17:01:23 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Feb 10 17:01:23 CST 2025
     */
    int insert(SpuOfferingInfoHistory record);

    /**
     *
     * @mbg.generated Mon Feb 10 17:01:23 CST 2025
     */
    int insertSelective(SpuOfferingInfoHistory record);

    /**
     *
     * @mbg.generated Mon Feb 10 17:01:23 CST 2025
     */
    List<SpuOfferingInfoHistory> selectByExample(SpuOfferingInfoHistoryExample example);

    /**
     *
     * @mbg.generated Mon Feb 10 17:01:23 CST 2025
     */
    SpuOfferingInfoHistory selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Feb 10 17:01:23 CST 2025
     */
    int updateByExampleSelective(@Param("record") SpuOfferingInfoHistory record, @Param("example") SpuOfferingInfoHistoryExample example);

    /**
     *
     * @mbg.generated Mon Feb 10 17:01:23 CST 2025
     */
    int updateByExample(@Param("record") SpuOfferingInfoHistory record, @Param("example") SpuOfferingInfoHistoryExample example);

    /**
     *
     * @mbg.generated Mon Feb 10 17:01:23 CST 2025
     */
    int updateByPrimaryKeySelective(SpuOfferingInfoHistory record);

    /**
     *
     * @mbg.generated Mon Feb 10 17:01:23 CST 2025
     */
    int updateByPrimaryKey(SpuOfferingInfoHistory record);

    /**
     *
     * @mbg.generated Mon Feb 10 17:01:23 CST 2025
     */
    int batchInsert(@Param("list") List<SpuOfferingInfoHistory> list);

    /**
     *
     * @mbg.generated Mon Feb 10 17:01:23 CST 2025
     */
    int batchInsertSelective(@Param("list") List<SpuOfferingInfoHistory> list, @Param("selective") SpuOfferingInfoHistory.Column ... selective);
}