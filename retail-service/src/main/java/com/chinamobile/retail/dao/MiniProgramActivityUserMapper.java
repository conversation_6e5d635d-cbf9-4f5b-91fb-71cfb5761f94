package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramActivityUser;
import com.chinamobile.retail.pojo.entity.MiniProgramActivityUserExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramActivityUserMapper {
    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    long countByExample(MiniProgramActivityUserExample example);

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    int deleteByExample(MiniProgramActivityUserExample example);

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    int insert(MiniProgramActivityUser record);

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    int insertSelective(MiniProgramActivityUser record);

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    List<MiniProgramActivityUser> selectByExample(MiniProgramActivityUserExample example);

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    MiniProgramActivityUser selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    int updateByExampleSelective(@Param("record") MiniProgramActivityUser record, @Param("example") MiniProgramActivityUserExample example);

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    int updateByExample(@Param("record") MiniProgramActivityUser record, @Param("example") MiniProgramActivityUserExample example);

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    int updateByPrimaryKeySelective(MiniProgramActivityUser record);

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    int updateByPrimaryKey(MiniProgramActivityUser record);

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    int batchInsert(@Param("list") List<MiniProgramActivityUser> list);

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    int batchInsertSelective(@Param("list") List<MiniProgramActivityUser> list, @Param("selective") MiniProgramActivityUser.Column ... selective);
}