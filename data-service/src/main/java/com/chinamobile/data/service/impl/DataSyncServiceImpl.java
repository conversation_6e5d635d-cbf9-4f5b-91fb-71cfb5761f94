package com.chinamobile.data.service.impl;

import com.chinamobile.data.config.DataQueryAOP;
import com.chinamobile.data.dao.OrderAtomStatisticsProvinceMapper;
import com.chinamobile.data.dao.OrderAtomStatisticsSpuMapper;
import com.chinamobile.data.dao.ext.Order2cAtomInfoMapperExt;
import com.chinamobile.data.pojo.entity.OrderAtomStatisticsProvince;
import com.chinamobile.data.pojo.entity.OrderAtomStatisticsProvinceExample;
import com.chinamobile.data.pojo.entity.OrderAtomStatisticsSpu;
import com.chinamobile.data.pojo.entity.OrderAtomStatisticsSpuExample;
import com.chinamobile.data.pojo.mapper.Order2cAtomGroupProvinceDO;
import com.chinamobile.data.pojo.mapper.Order2cAtomGroupSpuDO;
import com.chinamobile.data.service.DataSyncService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.DateUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * created by liuxiang on 2022/4/13 10:26
 * <AUTHOR>
 */
@Service
@Slf4j
public class DataSyncServiceImpl implements DataSyncService {

    @Resource
    private Order2cAtomInfoMapperExt order2cAtomInfoMapperExt;

    @Resource
    private OrderAtomStatisticsProvinceMapper orderAtomStatisticsProvinceMapper;

    @Resource
    private OrderAtomStatisticsSpuMapper orderAtomStatisticsSpuMapper;

    @Autowired
    private DataQueryAOP dataQueryAOP;

    @Override
    public List<OrderAtomStatisticsProvince> countOrder2cByProvince(String day) {
        log.info("count by province {}",day);
        List<OrderAtomStatisticsProvince> countResult = new ArrayList<>();
        if (!(DateUtils.isDateFormat(day,DateTimeUtil.STANDARD_DAY))) {
            return countResult;
        }
        try {
            innerCountByProvince(day, countResult);
        }catch (Exception exception) {
            log.error("count province exception ",exception);
        }
        dataQueryAOP.clearRedisData();
        return countResult;
    }

    /**
     * 方法抽取出来，便于公共使用，抛出异常，外部捕获
     * @param day
     * @param countResult
     * @throws Exception
     */
    public void innerCountByProvince(String day, List<OrderAtomStatisticsProvince> countResult) throws Exception {
        HashMap<String,OrderAtomStatisticsProvince> resultsMap = new HashMap<>();
        Calendar calendar = Calendar.getInstance();
        Date date = DateTimeUtil.getFormatDate(day,DateTimeUtil.STANDARD_DAY);
        calendar.setTime(date);
        String startTimeStr = DateTimeUtil.getDayBeginTime(day);
        String endTimeStr = DateTimeUtil.getDayEndTime(day);
        Date endDate = DateTimeUtil.getFormatDate(endTimeStr,DateTimeUtil.DB_TIME_STR);
        List<Order2cAtomGroupProvinceDO> results = order2cAtomInfoMapperExt.countOrderProvince(
                startTimeStr,
                endTimeStr);
        for (Order2cAtomGroupProvinceDO result : results) {
            OrderAtomStatisticsProvince provinceCount = buildProvince(result);
            provinceCount.setYear(calendar.get(Calendar.YEAR));
            provinceCount.setMonth(calendar.get(Calendar.MONTH) + 1);
            provinceCount.setDay(calendar.get(Calendar.DAY_OF_MONTH));
            provinceCount.setDataTime(endDate);
            countResult.add(provinceCount);
            resultsMap.put(result.getBeId(),provinceCount);
        }
        // 非今天得数据落库
        if (DateUtils.betweenDays(date) > 0) {
            OrderAtomStatisticsProvinceExample deleteExample = new OrderAtomStatisticsProvinceExample();
            deleteExample = deleteExample.createCriteria().andDataTimeEqualTo(endDate).example();
            orderAtomStatisticsProvinceMapper.deleteByExample(deleteExample);
            if (countResult.size() > 0) {
                orderAtomStatisticsProvinceMapper.batchInsert(countResult);
            }
        }
    }

    @Override
    public List<OrderAtomStatisticsProvince> countOrder2cByProvince(String start, String end) {
        HashMap<String,OrderAtomStatisticsProvince> resultsMap = new HashMap<>();
        try {
            List<Order2cAtomGroupProvinceDO> results = order2cAtomInfoMapperExt.countOrderProvince(
                    start,
                    end);
            for (Order2cAtomGroupProvinceDO result : results) {
                OrderAtomStatisticsProvince provinceCount = buildProvince(result);
                resultsMap.put(provinceCount.getProvince(),provinceCount);
            }
            //统计时间段后发生交易失败的订单
            List<Order2cAtomGroupProvinceDO> failResults = order2cAtomInfoMapperExt.countOrderProvinceFail(
                    start,
                    end);
            for (Order2cAtomGroupProvinceDO failResult : failResults) {
                OrderAtomStatisticsProvince provinceCount = resultsMap.get(failResult.getBeId());
                if (provinceCount == null) {
                    provinceCount = buildProvince(failResult);
                    resultsMap.put(provinceCount.getProvince(),provinceCount);
                }else {
                    provinceCount.setOrderCount(provinceCount.getOrderCount() + failResult.getOrderCount());
                    provinceCount.setOrderAmount(provinceCount.getOrderAmount() + failResult.getCalculatePrice());
                    provinceCount.setAtomOrderQuantity(provinceCount.getAtomOrderQuantity() + failResult.getAtomQuantityCount());
                    provinceCount.setSkuOrderQuantity(provinceCount.getSkuOrderQuantity() + failResult.getSkuQuantityCount());
                    provinceCount.setAtomOrderAmount(provinceCount.getAtomOrderAmount() + failResult.getAtomOrderAmount());
                }
            }
        }catch (Exception exception) {
            log.error("count province exception ",exception);
        }
        return Lists.newArrayList(resultsMap.values());
    }

    @Override
    public List<OrderAtomStatisticsSpu> countOrder2cBySpu(String day) {
        log.info("count by spu {}",day);
        List<OrderAtomStatisticsSpu> countResult = new ArrayList<>();
        if (!(DateUtils.isDateFormat(day,DateTimeUtil.STANDARD_DAY))) {
            return countResult;
        }
        try {
            innerCountBySpu(day, countResult);
        }catch (Exception exception) {
            log.error("count spu exception ",exception);
        }
        dataQueryAOP.clearRedisData();
        return countResult;
    }

    public void innerCountBySpu(String day, List<OrderAtomStatisticsSpu> countResult) throws Exception {
        HashMap<String,OrderAtomStatisticsSpu> resultsMap = new HashMap<>();
        Date date = DateTimeUtil.getFormatDate(day,DateTimeUtil.STANDARD_DAY);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        String startTimeStr = DateTimeUtil.getDayBeginTime(day);
        String endTimeStr = DateTimeUtil.getDayEndTime(day);
        List<Order2cAtomGroupSpuDO> results = order2cAtomInfoMapperExt.countOrderSpu(
                startTimeStr,
                endTimeStr);
        Date endDate = DateTimeUtil.getFormatDate(endTimeStr,DateTimeUtil.DB_TIME_STR);
        for (Order2cAtomGroupSpuDO result : results) {
            OrderAtomStatisticsSpu orderAtomStatisticsSpu = buildSpu(result);
            orderAtomStatisticsSpu.setYear(calendar.get(Calendar.YEAR));
            orderAtomStatisticsSpu.setMonth(calendar.get(Calendar.MONTH) + 1);
            orderAtomStatisticsSpu.setDay(calendar.get(Calendar.DAY_OF_MONTH));
            orderAtomStatisticsSpu.setDataTime(endDate);
            countResult.add(orderAtomStatisticsSpu);
            resultsMap.put(orderAtomStatisticsSpu.getSpuOfferingCode(),orderAtomStatisticsSpu);
        }
        if (DateUtils.betweenDays(date) > 0) {
            OrderAtomStatisticsSpuExample deleteExample = new OrderAtomStatisticsSpuExample();
            deleteExample = deleteExample.createCriteria().andDataTimeEqualTo(endDate).example();
            orderAtomStatisticsSpuMapper.deleteByExample(deleteExample);
            if (countResult.size() > 0) {
                orderAtomStatisticsSpuMapper.batchInsert(countResult);
            }
        }
    }

    @Override
    public List<OrderAtomStatisticsSpu> countOrder2cBySpu(String start, String end) {
        HashMap<String,OrderAtomStatisticsSpu> resultsMap = new HashMap<>();
        double amount = 0.0;
        try {
            List<Order2cAtomGroupSpuDO> results = order2cAtomInfoMapperExt.countOrderSpu(start, end);
            for (Order2cAtomGroupSpuDO result : results) {
                OrderAtomStatisticsSpu spuCount = buildSpu(result);
                resultsMap.put(spuCount.getSpuOfferingCode(),spuCount);
                amount += spuCount.getOrderAmount();
            }
            //统计时间段后发生交易失败的订单
            List<Order2cAtomGroupSpuDO> failResults = order2cAtomInfoMapperExt.countOrderSpuFail(
                    start,
                    end);
            for (Order2cAtomGroupSpuDO failResult : failResults) {
                OrderAtomStatisticsSpu spuCount = resultsMap.get(failResult.getSpuOfferingCode());
                if (spuCount == null) {
                    spuCount = buildSpu(failResult);
                    resultsMap.put(spuCount.getSpuOfferingCode(),spuCount);
                }else {
                    spuCount.setOrderCount(spuCount.getOrderCount() + failResult.getOrderCount());
                    spuCount.setOrderAmount(spuCount.getOrderAmount() + failResult.getCalculatePrice());
                    spuCount.setAtomOrderQuantity(spuCount.getAtomOrderQuantity() + failResult.getAtomQuantityCount());
                    spuCount.setSkuOrderQuantity(spuCount.getSkuOrderQuantity() + failResult.getSkuQuantityCount());
                    spuCount.setAtomOrderAmount(spuCount.getAtomOrderAmount() + failResult.getAtomOrderAmount());
                }
                amount += failResult.getCalculatePrice().doubleValue();
            }
        }catch (Exception exception) {
            log.error("count province exception ",exception);
        }
        log.info("sum is {}",amount/1000);
        return Lists.newArrayList(resultsMap.values());
    }

    private OrderAtomStatisticsProvince buildProvince(Order2cAtomGroupProvinceDO result) {
        OrderAtomStatisticsProvince provinceCount = new OrderAtomStatisticsProvince();
        provinceCount.setId(UUID.randomUUID().toString());
        provinceCount.setProvince(result.getBeId());
        provinceCount.setAtomOrderQuantity(result.getAtomQuantityCount());
        provinceCount.setSkuOrderQuantity(result.getSkuQuantityCount());
        provinceCount.setOrderCount(result.getOrderCount());
        provinceCount.setOrderAmount(result.getCalculatePrice());
        provinceCount.setAtomOrderAmount(result.getAtomOrderAmount());
        provinceCount.setCreateTime(new Date());
        return provinceCount;
    }

    private OrderAtomStatisticsSpu buildSpu(Order2cAtomGroupSpuDO result) {
        OrderAtomStatisticsSpu spuCount = new OrderAtomStatisticsSpu();
        spuCount.setId(UUID.randomUUID().toString());
        spuCount.setSpuOfferingCode(result.getSpuOfferingCode());
        if (result.getSpuOfferingName() == null) {
            spuCount.setSpuOfferingName("未知");
        }else {
            spuCount.setSpuOfferingName(result.getSpuOfferingName());
        }
        spuCount.setOrderCount(result.getOrderCount());
        spuCount.setAtomOrderQuantity(result.getAtomQuantityCount());
        spuCount.setSkuOrderQuantity(result.getSkuQuantityCount());
        spuCount.setOrderAmount(result.getCalculatePrice());
        spuCount.setAtomOrderAmount(result.getAtomOrderAmount());
        spuCount.setCreateTime(new Date());
        return spuCount;
    }
}
