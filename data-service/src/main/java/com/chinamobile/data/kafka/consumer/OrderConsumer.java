package com.chinamobile.data.kafka.consumer;

import com.chinamobile.data.config.CommonConstant;
import com.chinamobile.data.config.DataQueryAOP;
import com.chinamobile.data.pojo.dto.OrderInfoContentDTO;
import com.chinamobile.data.pojo.dto.OrderInfoDTO;
import com.chinamobile.data.pojo.entity.OrderInfo;
import com.chinamobile.data.service.DataSyncService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 消费订单数据
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderConsumer {

    @Resource
    private SimpMessagingTemplate simpMessagingTemplate;

    @Resource
    private DataSyncService dataSyncService;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    private DataQueryAOP dataQueryAOP;

    @KafkaListener(topics = "supply_chain_order")
    public void listener(ConsumerRecord<String, byte[]> record) {
        String s = stringRedisTemplate.opsForValue().get(CommonConstant.WEBSOCKET_REDIS_KEY);
        if(StringUtils.isEmpty(s)){
            log.info("【record】:{}", new String(record.value()));
            //清掉各接口缓存数据，这里不进行全量接口缓存，等到各接口请求时再缓存
            dataQueryAOP.clearRedisData();

            try {
                OrderInfoContentDTO orderInfoContentDTO = new ObjectMapper().readValue(record.value(),OrderInfoContentDTO.class);
                OrderInfoDTO orderInfo = orderInfoContentDTO.getOrderInfo();
                Date date = DateTimeUtil.getFormatDate(orderInfo.getCreateTime(),DateTimeUtil.DB_TIME_STR);
                if(date != null){
                    //对于订单失败的kafka消息，没有创建时间，所以date为空
                    String todayDayStr = DateTimeUtil.formatDate(new Date(),DateTimeUtil.STANDARD_DAY);
                    String createDateStr = DateTimeUtil.formatDate(date,DateTimeUtil.STANDARD_DAY);
                    if (!todayDayStr.equals(createDateStr)) {
                        // 非今天的数据重新统计
                        dataSyncService.countOrder2cBySpu(createDateStr);
                        dataSyncService.countOrder2cByProvince(createDateStr);
                    }
                }

            }catch (Exception exception) {
                log.info("数据解析失败【record】:{}", new String(record.value()),exception);
            }
            // 发送websocket消息
            simpMessagingTemplate.convertAndSend("/statistics/monitor", 1);
            //写入redis
            stringRedisTemplate.opsForValue().set(CommonConstant.WEBSOCKET_REDIS_KEY,"1",30, TimeUnit.SECONDS);

        }else {
            log.info("消费kafka消息频率过快，暂不发送webSocket");
        }

    }
}
