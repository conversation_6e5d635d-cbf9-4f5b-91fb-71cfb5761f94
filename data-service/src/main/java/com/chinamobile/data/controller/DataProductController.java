package com.chinamobile.data.controller;

import com.chinamobile.data.pojo.param.TimeParam;
import com.chinamobile.data.pojo.vo.*;
import com.chinamobile.data.service.DataProductService;
import com.chinamobile.data.service.DataQueryService;
import com.chinamobile.iot.sc.common.BaseAnswer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * created by l<PERSON>xia<PERSON> on 2023/3/7 09:37
 * 产品部门看板controller类
 */
@RestController
@RequestMapping("/data/product")
public class DataProductController {

    @Autowired
    private DataQueryService dataQueryService;

    @Resource
    private DataProductService dataProductService;


    /**
     * 各范式销售情况
     */
    @GetMapping("/spuTypeData")
    public BaseAnswer<List<SpuItemVO>> getSpuTypeData(TimeParam param) {
        return dataProductService.getSpuTypeData(param);
    }

    /**
     * 销售额和订单量
     */
    @GetMapping("/statistics")
    public BaseAnswer<OrderStatisticsVO> statistics(TimeParam param) {
        return dataProductService.statistics(param);
    }


    /**
     * 各部门销售额和订单量
     */
    @GetMapping("/departmentStatistics")
    public BaseAnswer<List<OrderStatisticsVO>> departmentStatistics(TimeParam param) {
        return dataProductService.departmentStatistics(param);
    }

    /**
     * 产品上下架情况
     */
    @GetMapping("/shelfStatistics")
    public BaseAnswer<List<ShelfStatisticsVO>> shelfStatistics(TimeParam param) {
        return dataProductService.shelfStatistics(param);
    }

    /**
     * 产品部门用户情况
     *
     * @param timeParam
     * @return
     */
    @GetMapping("/dataProductUser")
    public BaseAnswer<DataProductUserVO> dataProductUser(TimeParam timeParam) {
        BaseAnswer<DataProductUserVO> baseAnswer = new BaseAnswer<>();
        DataProductUserVO dataProductUserVO = dataProductService.listDataProductUser(timeParam);
        baseAnswer.setData(dataProductUserVO);
        return baseAnswer;
    }

    /**
     * 产品部门产品销售情况
     * @param timeParam
     * @return
     */
    @GetMapping("/dataProductSale")
    public BaseAnswer<List<DataProductSaleVO>> dataProductSale(TimeParam timeParam) {
        BaseAnswer<List<DataProductSaleVO>> baseAnswer = new BaseAnswer<>();
        List<DataProductSaleVO> dataProductSaleVOList = dataProductService.listDataProductSale(timeParam);
        baseAnswer.setData(dataProductSaleVOList);
        return baseAnswer;
    }

    /**
     * 热销产品top15
     * @param param
     * @return
     */
    @GetMapping("/hotSellProductOrder")
    public BaseAnswer<List<ProductOrderHotDataVO>> getProductOrderHotDataTop15(TimeParam param) {
        return dataProductService.getProductOrderHotSellTop15(param);
    }

    /**
     * 查询今日实时产品订单数据
     *
     * @return
     */
    @GetMapping("/productOrderRealTime")
    public BaseAnswer<List<ProductOrderRealTimeVO>> getTodayRealProductOrder() {
        return dataProductService.getTodayRealProductOrder();
    }

}
