package com.chinamobile.data.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * created by l<PERSON><PERSON><PERSON> on 2022/4/19 16:05
 */
@Component
@Data
public class IotConfig {

    //用于解密
    @Value("${iot.encodeKey}")
    private String encodeKey;
}
