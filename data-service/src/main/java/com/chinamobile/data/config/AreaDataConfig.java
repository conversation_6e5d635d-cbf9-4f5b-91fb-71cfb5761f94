package com.chinamobile.data.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * created by l<PERSON>xia<PERSON> on 2022/7/20 14:43
 * 项目启动时，将区域信息从redis加载到本地
 */
@Component
@Slf4j
@Data
public class AreaDataConfig implements CommandLineRunner {

    private Map<Object,Object> provinceCodeNameMap = new HashMap<>();
    private Map<Object,Object> provinceNameCodeMap = new HashMap<>();
    private Map<Object,Object> locationCodeNameMap = new HashMap<>();
//    private Map<Object,Object> locationNameCodeMap = new HashMap<>();
    private Map<Object,Object> regionCodeNameMap = new HashMap<>();
//    private Map<Object,Object> regionNameCodeMap = new HashMap<>();

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public void run(String... args) throws Exception {
        log.info("从redis加载区域信息 start");
        provinceCodeNameMap = stringRedisTemplate.opsForHash().entries(CommonConstant.REDIS_PROVINCE_KEY);
        locationCodeNameMap = stringRedisTemplate.opsForHash().entries(CommonConstant.REDIS_LOCATION_KEY);
        regionCodeNameMap = stringRedisTemplate.opsForHash().entries(CommonConstant.REDIS_REGION_KEY);
        /*for (Map.Entry<Object, Object> entry : locationCodeNameMap.entrySet()) {
            locationNameCodeMap.put(entry.getValue(),entry.getKey());
        }
        for (Map.Entry<Object, Object> entry : regionCodeNameMap.entrySet()) {
            regionNameCodeMap.put(entry.getValue(),entry.getKey());
        }*/
        for (Map.Entry<Object, Object> entry : provinceCodeNameMap.entrySet()) {
            provinceNameCodeMap.put(entry.getValue(),entry.getKey());
        }
        //区县，有些名称相同但是编码不同，但是同一个城市下不存在重复的区县。所以根据编码可以获取到区县，只是可能其编码错误。不影响前端展示
        /*if(provinceNameCodeMap.size() != provinceCodeNameMap.size()
                ||  locationNameCodeMap.size() != locationCodeNameMap.size()
        ){
            throw new RuntimeException("redis省市区数量错误");
        }*/
        log.info("从redis加载区域信息 end,省数量:{},市数量:{},区县数量:{}", provinceCodeNameMap.size(), locationCodeNameMap.size(), regionCodeNameMap.size());
    }
}
