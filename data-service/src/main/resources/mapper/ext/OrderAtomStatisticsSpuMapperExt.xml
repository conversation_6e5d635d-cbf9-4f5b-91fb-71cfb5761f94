<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.data.dao.ext.OrderAtomStatisticsSpuMapperExt">
  <resultMap id="BaseResultMap" type="com.chinamobile.data.pojo.entity.OrderAtomStatisticsSpu">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 18 10:45:10 CST 2022. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="spu_offering_name" jdbcType="VARCHAR" property="spuOfferingName" />
    <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode" />
    <result column="atom_order_count" jdbcType="INTEGER" property="atomOrderCount" />
    <result column="atom_order_amount" jdbcType="DOUBLE" property="atomOrderAmount" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="month" jdbcType="INTEGER" property="month" />
    <result column="day" jdbcType="INTEGER" property="day" />
    <result column="data_time" jdbcType="TIMESTAMP" property="dataTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <select id="getDataList" parameterType="java.util.Date" resultType="com.chinamobile.data.pojo.mapper.OrderStatisticsBySpuDO">
  SELECT
      spu_offering_name name,
      sum( atom_order_amount ) amount,
      sum( atom_order_count) orderCount
  FROM
      order_atom_statistics_spu
  GROUP BY
      spu_offering_name
  ORDER BY
      amount DESC
  </select>
</mapper>