<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.data.dao.ext.ShopCustomerInfoHistoryMapperExt">

  <select id="listShopDistributor" resultType="com.chinamobile.data.pojo.mapper.ShopDistributorDO">
      select
          sui.province_name provinceName,
          sui.city_name cityName,
          sui.region_name regionName,
          sui.cust_name custName,
          sui.cust_id custId,
          DATE_FORMAT(sui.register_date,'%Y-%m-%d %H:%i:%s') registerDateStr,
          case
              when sui.role_type = 1 then '一级分销员'
              when sui.role_type = 2 then '二级分销员'
          else '未知级别分销员'
          end roleTypeName,
          sui.distributor_channel_id distributorChannelId,
          sui.distributor_channel_name distributorChannelName,
          sui.distributor_referral_code distributorReferralCode,
          sui.distributor_mrg_inf distributorMrgInf,
          sui.distributor_mrg_code distributorMrgCode,
          sum(suih.distributor_invitation_register_successful_quantity) distributorInvitationRegisterSuccessfulQuantity,
          sum(suih.number_logins) numberLogins
      from
          shop_customer_info sui
      left join shop_customer_info_history suih on sui.cust_code = suih.cust_code
              and sui.cust_id = suih.cust_id
              and sui.role_type = suih.role_type
              and sui.be_id = suih.be_id
              and sui.location = suih.location
              and sui.region_id = suih.region_id
      where
          sui.role_type in (1,2)
      <if test="pageShopDistributorParam.beId != null and pageShopDistributorParam.beId != ''">
        and sui.be_id = #{pageShopDistributorParam.beId}
      </if>
      <if test="pageShopDistributorParam.beginRegisterDate != null and pageShopDistributorParam.beginRegisterDate != ''">
        and DATE_FORMAT(sui.register_date,'%Y-%m-%d') <![CDATA[ >= ]]> #{pageShopDistributorParam.beginRegisterDate}
      </if>
      <if test="pageShopDistributorParam.endRegisterDate != null and pageShopDistributorParam.endRegisterDate != ''">
        and DATE_FORMAT(sui.register_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{pageShopDistributorParam.endRegisterDate}
      </if>
      <if test="pageShopDistributorParam.beginRegisterLoginDate != null and pageShopDistributorParam.beginRegisterLoginDate != ''">
        and DATE_FORMAT(suih.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{pageShopDistributorParam.beginRegisterLoginDate}
      </if>
      <if test="pageShopDistributorParam.endRegisterLoginDate != null and pageShopDistributorParam.endRegisterLoginDate != ''">
        and DATE_FORMAT(suih.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{pageShopDistributorParam.endRegisterLoginDate}
      </if>
      group by sui.cust_code,sui.be_id,sui.location,sui.region_id,sui.role_type
      <if test="pageShopDistributorParam.beginRegisterLoginDate != null and pageShopDistributorParam.beginRegisterLoginDate != ''">
          union
          select
          sui.province_name provinceName,
          sui.city_name cityName,
          sui.region_name regionName,
          sui.cust_name custName,
          sui.cust_id custId,
          DATE_FORMAT(sui.register_date,'%Y-%m-%d %H:%i:%s') registerDateStr,
          case
          when sui.role_type = 1 then '一级分销员'
          when sui.role_type = 2 then '二级分销员'
          else '未知级别分销员'
          end roleTypeName,
          sui.distributor_channel_id distributorChannelId,
          sui.distributor_channel_name distributorChannelName,
          sui.distributor_referral_code distributorReferralCode,
          sui.distributor_mrg_inf distributorMrgInf,
          sui.distributor_mrg_code distributorMrgCode,
          0 distributorInvitationRegisterSuccessfulQuantity,
          0 numberLogins
          from
          shop_customer_info sui
          left join shop_customer_info_history suih on sui.cust_code = suih.cust_code
          and sui.cust_id = suih.cust_id
          and sui.role_type = suih.role_type
          and sui.be_id = suih.be_id
          and sui.location = suih.location
          and sui.region_id = suih.region_id
          where
          sui.role_type in (1,2)
          <if test="pageShopDistributorParam.beId != null and pageShopDistributorParam.beId != ''">
              and sui.be_id = #{pageShopDistributorParam.beId}
          </if>
          <if test="pageShopDistributorParam.beginRegisterDate != null and pageShopDistributorParam.beginRegisterDate != ''">
              and DATE_FORMAT(sui.register_date,'%Y-%m-%d') <![CDATA[ >= ]]> #{pageShopDistributorParam.beginRegisterDate}
          </if>
          <if test="pageShopDistributorParam.endRegisterDate != null and pageShopDistributorParam.endRegisterDate != ''">
              and DATE_FORMAT(sui.register_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{pageShopDistributorParam.endRegisterDate}
          </if>
          <if test="pageShopDistributorParam.beginRegisterLoginDate != null and pageShopDistributorParam.beginRegisterLoginDate != ''">
              and sui.cust_code not in
              (
              select distinct
              sui.cust_code
              from
              shop_customer_info sui
              left join shop_customer_info_history suih on sui.cust_code = suih.cust_code
              and sui.cust_id = suih.cust_id
              and sui.role_type = suih.role_type
              and sui.be_id = suih.be_id
              and sui.location = suih.location
              and sui.region_id = suih.region_id
              where
              sui.role_type in (1,2)
              and DATE_FORMAT(suih.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{pageShopDistributorParam.beginRegisterLoginDate}
              and DATE_FORMAT(suih.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{pageShopDistributorParam.endRegisterLoginDate}

              )
          </if>
          <if test="pageShopDistributorParam.endRegisterLoginDate != null and pageShopDistributorParam.endRegisterLoginDate != ''">
              and sui.cust_code  in
              (
              select distinct
              sui.cust_code
              from
              shop_customer_info sui
              where
              sui.role_type in (1,2)
              and DATE_FORMAT(sui.register_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{pageShopDistributorParam.endRegisterLoginDate}
              )
          </if>
          group by sui.cust_code,sui.be_id,sui.location,sui.region_id,sui.role_type
      </if>
  </select>

  <select id="listShopRegisterUser" resultType="com.chinamobile.data.pojo.mapper.ShopRegisterUserDO">
        select
            sui.province_name provinceName,
            sui.city_name cityName,
            sui.region_name regionName,
            sui.cust_id custId,
            sui.cust_code custCode,
            sui.cust_name custName,
            DATE_FORMAT(sui.register_date,'%Y-%m-%d %H:%i:%s') registerDateStr,
            case
                when sui.client_status = 0 then '待激活'
                when sui.client_status = 1 then '激活'
                when sui.client_status = 2 then '暂停'
                when sui.client_status = 3 then '失效'
            else '无效状态'
            end clientStatusName,
            sum(suih.number_logins) numberLogins
        from
            shop_customer_info sui
        left join shop_customer_info_history suih on sui.cust_code = suih.cust_code
              and sui.cust_id = suih.cust_id
              and sui.role_type = suih.role_type
              and sui.be_id = suih.be_id
              and sui.location = suih.location
              and sui.region_id = suih.region_id
        where
          sui.role_type = 0
          <if test="pageShopRegisterUserParam.beId != null and pageShopRegisterUserParam.beId != ''">
              and sui.be_id = #{pageShopRegisterUserParam.beId}
          </if>
          <if test="pageShopRegisterUserParam.beginRegisterDate != null and pageShopRegisterUserParam.beginRegisterDate != ''">
              and DATE_FORMAT(sui.register_date,'%Y-%m-%d') <![CDATA[ >= ]]> #{pageShopRegisterUserParam.beginRegisterDate}
          </if>
          <if test="pageShopRegisterUserParam.endRegisterDate != null and pageShopRegisterUserParam.endRegisterDate != ''">
              and DATE_FORMAT(sui.register_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{pageShopRegisterUserParam.endRegisterDate}
          </if>
          <if test="pageShopRegisterUserParam.clientStatus != null and pageShopRegisterUserParam.clientStatus != ''">
              and sui.client_status = #{pageShopRegisterUserParam.clientStatus}
          </if>
          <if test="pageShopRegisterUserParam.beginRegisterLoginDate != null and pageShopRegisterUserParam.beginRegisterLoginDate != ''">
              and DATE_FORMAT(suih.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{pageShopRegisterUserParam.beginRegisterLoginDate}
          </if>
          <if test="pageShopRegisterUserParam.endRegisterLoginDate != null and pageShopRegisterUserParam.endRegisterLoginDate != ''">
              and DATE_FORMAT(suih.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{pageShopRegisterUserParam.endRegisterLoginDate}
          </if>
        group by sui.cust_code,sui.be_id,sui.location,sui.region_id
      <if test="pageShopRegisterUserParam.beginRegisterLoginDate != null and pageShopRegisterUserParam.beginRegisterLoginDate != ''">
          union
          select
          sui.province_name provinceName,
          sui.city_name cityName,
          sui.region_name regionName,
          sui.cust_id custId,
          sui.cust_code custCode,
          sui.cust_name custName,
          DATE_FORMAT(sui.register_date,'%Y-%m-%d %H:%i:%s') registerDateStr,
          case
          when sui.client_status = 0 then '待激活'
          when sui.client_status = 1 then '激活'
          when sui.client_status = 2 then '暂停'
          when sui.client_status = 3 then '失效'
          else '无效状态'
          end clientStatusName,
          0 numberLogins
          from
          shop_customer_info sui
          left join shop_customer_info_history suih on sui.cust_code = suih.cust_code
          and sui.cust_id = suih.cust_id
          and sui.role_type = suih.role_type
          and sui.be_id = suih.be_id
          and sui.location = suih.location
          and sui.region_id = suih.region_id
          where
          sui.role_type = 0
          <if test="pageShopRegisterUserParam.beId != null and pageShopRegisterUserParam.beId != ''">
              and sui.be_id = #{pageShopRegisterUserParam.beId}
          </if>
          <if test="pageShopRegisterUserParam.beginRegisterDate != null and pageShopRegisterUserParam.beginRegisterDate != ''">
              and DATE_FORMAT(sui.register_date,'%Y-%m-%d') <![CDATA[ >= ]]> #{pageShopRegisterUserParam.beginRegisterDate}
          </if>
          <if test="pageShopRegisterUserParam.endRegisterDate != null and pageShopRegisterUserParam.endRegisterDate != ''">
              and DATE_FORMAT(sui.register_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{pageShopRegisterUserParam.endRegisterDate}
          </if>
          <if test="pageShopRegisterUserParam.clientStatus != null and pageShopRegisterUserParam.clientStatus != ''">
              and sui.client_status = #{pageShopRegisterUserParam.clientStatus}
          </if>
          <if test="pageShopRegisterUserParam.beginRegisterLoginDate != null and pageShopRegisterUserParam.beginRegisterLoginDate != ''">
              and sui.cust_code not in
              (
              select distinct
              sui.cust_code
              from
              shop_customer_info sui
              left join shop_customer_info_history suih on sui.cust_code = suih.cust_code
              and sui.cust_id = suih.cust_id
              and sui.role_type = suih.role_type
              and sui.be_id = suih.be_id
              and sui.location = suih.location
              and sui.region_id = suih.region_id
              where
              sui.role_type = 0
              and DATE_FORMAT(suih.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{pageShopRegisterUserParam.beginRegisterLoginDate}
              and DATE_FORMAT(suih.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{pageShopRegisterUserParam.endRegisterLoginDate}
              )
          </if>
          <if test="pageShopRegisterUserParam.endRegisterLoginDate != null and pageShopRegisterUserParam.endRegisterLoginDate != ''">
              and sui.cust_code  in
              (
              select distinct
              sui.cust_code
              from
              shop_customer_info sui
              where
              sui.role_type = 0
              and DATE_FORMAT(sui.register_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{pageShopRegisterUserParam.endRegisterLoginDate}
              )
          </if>
          group by sui.cust_code,sui.be_id,sui.location,sui.region_id
      </if>
  </select>
</mapper>