<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.data.dao.ext.Order2cAtomInfoMapperExt">
  <resultMap id="BaseResultMap" type="com.chinamobile.data.pojo.entity.Order2cAtomInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 14 16:10:40 CST 2022. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode" />
    <result column="sku_offering_code" jdbcType="VARCHAR" property="skuOfferingCode" />
    <result column="sku_offering_name" jdbcType="VARCHAR" property="skuOfferingName" />
    <result column="sku_quantity" jdbcType="BIGINT" property="skuQuantity" />
    <result column="sku_price" jdbcType="BIGINT" property="skuPrice" />
    <result column="market_name" jdbcType="VARCHAR" property="marketName" />
    <result column="market_code" jdbcType="VARCHAR" property="marketCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="color" jdbcType="VARCHAR" property="color" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="atom_offering_class" jdbcType="VARCHAR" property="atomOfferingClass" />
    <result column="atom_offering_code" jdbcType="VARCHAR" property="atomOfferingCode" />
    <result column="atom_offering_name" jdbcType="VARCHAR" property="atomOfferingName" />
    <result column="atom_price" jdbcType="BIGINT" property="atomPrice" />
    <result column="atom_settle_price" jdbcType="BIGINT" property="atomSettlePrice" />
    <result column="atom_quantity" jdbcType="BIGINT" property="atomQuantity" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="region_id" jdbcType="VARCHAR" property="regionId" />
    <result column="ex_handle_id" jdbcType="VARCHAR" property="exHandleId" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="getOrderSumData" resultType="com.chinamobile.data.pojo.mapper.OrderStatisticsScreenDO">
      SELECT
          count( occ.order_id ) AS atomOrderCount,
          SUM( occ.atomOrderQuantity ) AS atomOrderQuantity,
          SUM( occ.atomOrderAmount ) AS atomOrderAmount
      FROM
          (
          SELECT
          oc.order_id AS order_id,
          sku_quantity * sku_price  AS atomOrderAmount,
          sku_quantity atomOrderQuantity
          FROM
          order_2c_atom_info oc
          WHERE
          ((oc.order_type = '01' and oc.order_status != 8)  or ((oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') and oc.order_status in (12,7)))
          <if test="startTime != null">
              and
              CASE
              when order_type ='01' then create_time <![CDATA[ >= ]]> #{startTime}
              when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ >= ]]> #{startTime}
              ELSE create_time <![CDATA[ >= ]]> #{startTime}
              END
          </if>
          <if test="endTime != null">
              and
              CASE
              when order_type ='01' then create_time <![CDATA[ <= ]]> #{endTime}
              when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ <= ]]> #{endTime}
              ELSE create_time <![CDATA[ <= ]]> #{endTime}
              END
          </if>
         <if test="areaCode != null and areaCode !=''">
          and  oc.be_id = #{areaCode}
         </if>
          GROUP BY
          oc.order_id ) occ
  </select>

    <select id="getSpuDataList" resultType="com.chinamobile.data.pojo.mapper.OrderStatisticsBySpuDO">
    SELECT
        sp.offering_name NAME,
        sum(occ.amount ) amount,
        count(occ.order_id) orderCount
    FROM
        (
        SELECT
        oc.order_id AS order_id,
        oc.spu_offering_code,
        oc.sku_quantity * oc.sku_price AS amount
        FROM
        order_2c_atom_info oc
        WHERE
        ((oc.order_type = '01' and oc.order_status != 8)  or ((oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') and oc.order_status in (12,7)))
        <if test="startTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ >= ]]> #{startTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ >= ]]> #{startTime}
            ELSE create_time <![CDATA[ >= ]]> #{startTime}
            END
        </if>
        <if test="endTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ <= ]]> #{endTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ <= ]]> #{endTime}
            ELSE create_time <![CDATA[ <= ]]> #{endTime}
            END
        </if>
        <if test="areaCode != null and areaCode !=''">
            and oc.be_id = #{areaCode}
        </if>
        GROUP BY
        oc.order_id ) occ
        JOIN spu_offering_info sp ON sp.offering_code = occ.spu_offering_code
    GROUP BY
        sp.offering_name
    ORDER BY
        amount DESC
    <if test="limit != null">
        LIMIT #{limit}
    </if>
    </select>

    <select id="getOrderAreaData" resultType="com.chinamobile.data.pojo.mapper.OrderStatisticsByProvinceDO" >
        SELECT
        <if test="areaType == 0">
            oi.be_id areaCode,
        </if>
        <if test="areaType == 1">
            oi.location areaCode,
        </if>
        <if test="areaType == 2">
            oi.region_ID areaCode,
        </if>
        sum( amount ) amount,
        count( occ.order_id ) orderCount
        FROM
        (
        SELECT
        oc.order_id AS order_id,
        oc.sku_quantity * oc.sku_price AS amount
        FROM
        order_2c_atom_info oc
        WHERE
        ((oc.order_type = '01' and oc.order_status != 8)  or ((oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') and oc.order_status in (12,7)))
        <if test="startTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ >= ]]> #{startTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ >= ]]> #{startTime}
            ELSE create_time <![CDATA[ >= ]]> #{startTime}
            END
        </if>
        <if test="endTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ <= ]]> #{endTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ <= ]]> #{endTime}
            ELSE create_time <![CDATA[ <= ]]> #{endTime}
            END
        </if>
        GROUP BY
        oc.order_id
        ) occ
        JOIN order_2c_info oi ON oi.order_id = occ.order_id
        where 1=1
        and oi.be_id not in ('000','001','002','01')
        <if test="areaType == 1">
         and  oi.be_id = #{areaCode}
        </if>
        <if test="areaType == 2">
         and  oi.location = #{areaCode}
        </if>
        GROUP BY
        CASE
        when #{areaType}=0 then
        oi.be_id
        when #{areaType}=1 then
        oi.location
        when #{areaType}=2 then
        oi.region_ID
        END
        ORDER BY
        amount DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <select id="countOrderProvince" resultType="com.chinamobile.data.pojo.mapper.Order2cAtomGroupProvinceDO" parameterType="java.lang.String">
        SELECT
        occ.be_id AS be_id,
        count( occ.order_id ) AS orderCount,
        SUM( occ.atomQuantityCount ) AS atomQuantityCount,
        SUM( occ.skuQuantityCount ) AS skuQuantityCount,
        IFNULL( SUM( occ.atomOrderAmount ), 0 ) AS atomOrderAmount,
        SUM( occ.calculatePrice ) AS calculatePrice
        FROM
        (SELECT
        oc.be_id as be_id,
        oc.order_id as order_id,
        sku_quantity * sku_price AS atomOrderAmount,
        sku_quantity * sku_price AS calculatePrice,
        sku_quantity AS skuQuantityCount,
        SUM( sku_quantity * atom_quantity ) AS atomQuantityCount
        FROM
            order_2c_atom_info oc
        WHERE
        ((oc.order_type = '01' and oc.order_status != 8)  or ((oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') and oc.order_status in (12,7)))
        <if test="startTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ >= ]]> #{startTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ >= ]]> #{startTime}
            ELSE create_time <![CDATA[ >= ]]> #{startTime}
            END
        </if>
        <if test="endTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ <= ]]> #{endTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ <= ]]> #{endTime}
            ELSE create_time <![CDATA[ <= ]]> #{endTime}
            END
        </if>
        GROUP BY
            oc.order_id) occ
        GROUP BY
            occ.be_id
    </select>

    <select id="countOrderSpu" resultType="com.chinamobile.data.pojo.mapper.Order2cAtomGroupSpuDO" parameterType="java.lang.String">
        SELECT occ.spu_offering_code AS spu_offering_code,
        spu.offering_name AS spu_offering_name,
        count( occ.order_id ) AS orderCount,
        SUM( occ.atomQuantityCount ) AS atomQuantityCount,
        SUM( occ.skuQuantityCount ) AS skuQuantityCount,
        IFNULL( SUM( occ.atomOrderAmount ), 0 ) AS atomOrderAmount,
        SUM( occ.calculatePrice ) AS calculatePrice
        FROM
        (SELECT
        oc.spu_offering_code as spu_offering_code,
        oc.order_id as order_id,
        sku_quantity * sku_price AS atomOrderAmount,
        sku_quantity * sku_price AS calculatePrice,
        sku_quantity AS skuQuantityCount,
        SUM( sku_quantity * atom_quantity ) AS atomQuantityCount
        FROM
        order_2c_atom_info oc
        WHERE
        ((oc.order_type = '01' and oc.order_status != 8)  or ((oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') and oc.order_status in (12,7)))
        <if test="startTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ >= ]]> #{startTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ >= ]]> #{startTime}
            ELSE create_time <![CDATA[ >= ]]> #{startTime}
            END
        </if>
        <if test="endTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ <= ]]> #{endTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ <= ]]> #{endTime}
            ELSE create_time <![CDATA[ <= ]]> #{endTime}
            END
        </if>
        GROUP BY
        oc.order_id) occ
        LEFT JOIN
            spu_offering_info spu on occ.spu_offering_code = spu.offering_code
        GROUP BY
            occ.spu_offering_code
    </select>
    <select id="countOrderSpuFail" resultType="com.chinamobile.data.pojo.mapper.Order2cAtomGroupSpuDO" parameterType="java.lang.String">
        SELECT occ.spu_offering_code AS spu_offering_code,
        spu.offering_name AS spu_offering_name,
        count( occ.order_id ) AS orderCount,
        SUM( occ.atomQuantityCount ) AS atomQuantityCount,
        SUM( occ.skuQuantityCount ) AS skuQuantityCount,
        IFNULL( SUM( occ.atomOrderAmount ), 0 ) AS atomOrderAmount,
        SUM( occ.calculatePrice ) AS calculatePrice
        FROM
        (SELECT
        oc.spu_offering_code as spu_offering_code,
        oc.order_id as order_id,
        sku_quantity * sku_price AS atomOrderAmount,
        sku_quantity * sku_price AS calculatePrice,
        sku_quantity  AS skuQuantityCount,
        SUM( sku_quantity * atom_quantity ) AS atomQuantityCount
        FROM
        order_2c_atom_info oc
        WHERE
        oc.order_status = 8
        <if test="startTime != null">
            and oc.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and oc.create_time  <![CDATA[ <= ]]> #{endTime}
            and oc.update_time  <![CDATA[ >= ]]> #{endTime}
        </if>
        GROUP BY
        oc.order_id) occ
        LEFT JOIN
        spu_offering_info spu on occ.spu_offering_code = spu.offering_code
        GROUP BY
        occ.spu_offering_code
    </select>

    <select id="countOrderProvinceFail" resultType="com.chinamobile.data.pojo.mapper.Order2cAtomGroupProvinceDO" parameterType="java.lang.String">
        SELECT
        occ.be_id AS be_id,
        count( occ.order_id ) AS orderCount,
        SUM( occ.atomQuantityCount ) AS atomQuantityCount,
        SUM( occ.skuQuantityCount ) AS skuQuantityCount,
        IFNULL( SUM( occ.atomOrderAmount ), 0 ) AS atomOrderAmount,
        SUM( occ.calculatePrice ) AS calculatePrice
        FROM
        (SELECT
        oc.be_id as be_id,
        oc.order_id as order_id,
        sku_quantity * sku_price AS calculatePrice,
        sku_quantity * sku_price AS atomOrderAmount,
        sku_quantity AS skuQuantityCount,
        SUM( sku_quantity * atom_quantity ) AS atomQuantityCount
        FROM
        order_2c_atom_info oc
        WHERE
        oc.order_status = 8
        <if test="startTime != null">
            and oc.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and oc.create_time  <![CDATA[ <= ]]> #{endTime}
            and oc.update_time  <![CDATA[ >= ]]> #{endTime}
        </if>
        GROUP BY
        oc.order_id) occ
        GROUP BY
        occ.be_id
    </select>


    <select id="getOrderAmountBySpuType" resultType="com.chinamobile.data.pojo.mapper.OrderStatisticsBySpuTypeDO" parameterType="java.lang.String">
        SELECT
        oi.spu_offering_class spuType,
        sum(occ.amount ) amount
        FROM
        (
        SELECT
        oc.order_id AS order_id,
        oc.sku_quantity * oc.sku_price AS amount
        FROM
        order_2c_atom_info oc
        WHERE
        ((oc.order_type = '01' and oc.order_status != 8)  or ((oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') and oc.order_status in (12,7)))
        <if test="startTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ >= ]]> #{startTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ >= ]]> #{startTime}
            ELSE create_time <![CDATA[ >= ]]> #{startTime}
            END
        </if>
        <if test="endTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ <= ]]> #{endTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ <= ]]> #{endTime}
            ELSE create_time <![CDATA[ <= ]]> #{endTime}
            END
        </if>
        GROUP BY
        oc.order_id ) occ
        JOIN order_2c_info oi ON oi.order_id = occ.order_id
        GROUP BY
        oi.spu_offering_class
    </select>

<!--    <select id="getOrderDetailsList" resultType="com.chinamobile.data.pojo.mapper.OrderMarketMessageDO" parameterType="com.chinamobile.data.pojo.param.OrderStatisticsParam">
        SELECT
        COUNT( occ.createTime ) atomOrderCount,
        SUM( atomOrderAmount ) AS atomOrderAmount,
        SUM( atomOrderQuantity ) AS atomOrderQuantity,
        createTime
        FROM
        (
        SELECT
        if(#{timeType}=0,substring( oc.create_time, 1, 6 ),substring( oc.create_time, 1, 8 )) createTime,
        SUM( sku_quantity * sku_price ) AS atomOrderAmount,
        SUM( sku_quantity ) AS atomOrderQuantity
        FROM
        order_2c_atom_info oc
        WHERE
        oc.order_status != 8 and (oc.atom_offering_class != 'S' or oc.atom_offering_class is null)
        <if test="startTime != null">
            and oc.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and  oc.create_time  <![CDATA[ <= ]]> #{endTime}
        </if>
        GROUP BY
        oc.order_id ) occ
        JOIN order_2c_info oi ON oi.order_id = occ.order_id
        GROUP BY
        oi.spu_offering_class
    </select>-->

    <select id="getOrderTimeStatisticsWithSpu" resultType="com.chinamobile.data.pojo.mapper.OrderTimeStatisticsWithSpuDO" parameterType="java.lang.String">
    SELECT
	oi.spu_offering_class spuType,
	occ.amount,
	occ.create_time createTime
    FROM
        (
        SELECT
            oc.order_id AS order_id,
            oc.sku_quantity * oc.sku_price AS amount ,
            create_time
        FROM
            order_2c_atom_info oc
        WHERE
            ((oc.order_type = '01' and oc.order_status != 8)  or ((oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') and oc.order_status in (12,7)))
            <if test="startTime != null">
                and
                CASE
                when order_type ='01' then create_time <![CDATA[ >= ]]> #{startTime}
                when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ >= ]]> #{startTime}
                ELSE create_time <![CDATA[ >= ]]> #{startTime}
                END
            </if>
            <if test="endTime != null">
                and
                CASE
                when order_type ='01' then create_time <![CDATA[ <= ]]> #{endTime}
                when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ <= ]]> #{endTime}
                ELSE create_time <![CDATA[ <= ]]> #{endTime}
                END
            </if>
        GROUP BY
            oc.order_id
        ) occ
        JOIN order_2c_info oi ON oi.order_id = occ.order_id
        ORDER BY occ.create_time
    </select>

    <select id="getOrderTimeStatisticsWithProvince" parameterType="java.lang.String" resultType="com.chinamobile.data.pojo.mapper.OrderTimeStatisticsWithProvinceDO">
    SELECT
        sku_quantity * sku_price AS amount,
        be_id province,
        create_time  createTime
    FROM
        order_2c_atom_info oc
    WHERE
        ((oc.order_type = '01' and oc.order_status != 8)  or ((oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') and oc.order_status in (12,7)))
        <if test="startTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ >= ]]> #{startTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ >= ]]> #{startTime}
            ELSE create_time <![CDATA[ >= ]]> #{startTime}
            END
        </if>
        <if test="endTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ <= ]]> #{endTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ <= ]]> #{endTime}
            ELSE create_time <![CDATA[ <= ]]> #{endTime}
            END
        </if>
    GROUP BY
        oc.order_id
    ORDER BY oc.create_time

    </select>

    <select id="getOrderTimeStatistics" parameterType="java.lang.String" resultType="com.chinamobile.data.pojo.mapper.OrderTimeStatisticsDO">
        SELECT
        sku_quantity * sku_price AS amount,
        create_time  createTime
        FROM
        order_2c_atom_info oc
        WHERE
        ((oc.order_type = '01' and oc.order_status != 8)  or ((oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') and oc.order_status in (12,7)))
        <if test="startTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ >= ]]> #{startTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ >= ]]> #{startTime}
            ELSE create_time <![CDATA[ >= ]]> #{startTime}
            END
        </if>
        <if test="endTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ <= ]]> #{endTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ <= ]]> #{endTime}
            ELSE create_time <![CDATA[ <= ]]> #{endTime}
            END
        </if>
        GROUP BY
        oc.order_id
        ORDER BY oc.create_time
    </select>
    
    <select id="listOfferingClassTotalSellByProvince" resultType="com.chinamobile.data.pojo.mapper.ProvinceOfferingClassTotalSellDO">
        SELECT
        occ.be_id,
        occ.spu_offering_class spuOfferingClass,
        CASE
        WHEN occ.spu_offering_class = 'A04' THEN '产品增值服务包'
        WHEN occ.spu_offering_class = 'A06' THEN '联合销售'
        WHEN occ.spu_offering_class = 'A07' THEN '合同履约'
        WHEN occ.spu_offering_class = 'A08' THEN 'OneNET独立服务'
        WHEN occ.spu_offering_class = 'A09' THEN 'OnePark独立'
        WHEN occ.spu_offering_class = 'A12' THEN '行车卫士标准产品'
        WHEN occ.spu_offering_class = 'A13' THEN '软件服务'
        WHEN occ.spu_offering_class = 'A14' THEN 'OneCyber标准产品'
        ELSE '其他'
        END offeringClassName,
        SUM( occ.atomOrderAmount ) AS offeringClassSell
        FROM
        (
        SELECT
        oc.order_id AS order_id,
        sku_quantity * sku_price AS atomOrderAmount,
        sku_quantity atomOrderQuantity,
        oi.spu_offering_class spu_offering_class,
        oc.be_id be_id
        FROM
        order_2c_atom_info oc
        JOIN order_2c_info oi ON oc.order_id = oi.order_id
        WHERE
        ((oc.order_type = '01' and oc.order_status != 8) or ((oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') and
        oc.order_status in (12,7)))
        <if test="productRunParam.startTime != null and productRunParam.startTime !=  ''">
            and
            CASE
            when oc.order_type ='01' then substring(oc.create_time,1,8) <![CDATA[ >= ]]> #{productRunParam.startTime}
            when (oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') then substring(oc.valet_order_complete_time,1,8)
            <![CDATA[ >= ]]> #{productRunParam.startTime}
            ELSE substring(oc.create_time,1,8) <![CDATA[ >= ]]> #{productRunParam.startTime}
            END
        </if>
        <if test="productRunParam.endTime != null and productRunParam.endTime != ''">
            and
            CASE
            when oc.order_type ='01' then substring(oc.create_time,1,8) <![CDATA[ <= ]]> #{productRunParam.endTime}
            when (oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') then substring(oc.valet_order_complete_time,1,8)
            <![CDATA[ <= ]]> #{productRunParam.endTime}
            ELSE substring(oc.create_time,1,8) <![CDATA[ <= ]]> #{productRunParam.endTime}
            END
        </if>
        GROUP BY
        oc.order_id ) occ
        GROUP BY occ.be_id , occ.spu_offering_class
    </select>
    
    <select id="listSpuCodeUseByProvince" resultType="com.chinamobile.data.pojo.mapper.ProvinceSpuCodeUseDO">
        SELECT
        occ.be_id beId,
        occ.spu_offering_class spuOfferingClass,
        CASE
            WHEN occ.spu_offering_class = 'A04' THEN '产品增值服务包'
            WHEN occ.spu_offering_class = 'A06' THEN '联合销售'
            WHEN occ.spu_offering_class = 'A07' THEN '合同履约'
            WHEN occ.spu_offering_class = 'A08' THEN 'OneNET独立服务'
            WHEN occ.spu_offering_class = 'A09' THEN 'OnePark独立'
            WHEN occ.spu_offering_class = 'A12' THEN '行车卫士标准产品'
            WHEN occ.spu_offering_class = 'A13' THEN '软件服务'
            WHEN occ.spu_offering_class = 'A14' THEN 'OneCyber标准产品'
            ELSE '其他'
        END offeringClassName,
        COUNT(occ.spu_offering_class) spuOfferingCode
        FROM
            (
            SELECT
            oi.spu_offering_class spu_offering_class,
            oc.be_id be_id
            FROM
            order_2c_atom_info oc
            JOIN order_2c_info oi ON oc.order_id = oi.order_id
            WHERE
             ((oc.order_type = '01' and oc.order_status != 8)  or ((oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') and oc.order_status in (12,7)))
            <if test="productRunParam.startTime != null and productRunParam.startTime !=  ''">
                and
                CASE
                when oc.order_type ='01' then substring(oc.create_time,1,8) <![CDATA[ >= ]]> #{productRunParam.startTime}
                when (oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') then substring(oc.valet_order_complete_time,1,8) <![CDATA[ >= ]]> #{productRunParam.startTime}
                ELSE substring(oc.create_time,1,8) <![CDATA[ >= ]]> #{productRunParam.startTime}
                END
            </if>
            <if test="productRunParam.endTime != null and productRunParam.endTime != ''">
                and
                CASE
                when oc.order_type ='01' then substring(oc.create_time,1,8) <![CDATA[ <= ]]> #{productRunParam.endTime}
                when (oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') then substring(oc.valet_order_complete_time,1,8) <![CDATA[ <= ]]> #{productRunParam.endTime}
                ELSE substring(oc.create_time,1,8) <![CDATA[ <= ]]> #{productRunParam.endTime}
                END
            </if>
            GROUP BY
            oc.order_id
            ) occ
        GROUP BY occ.be_id , occ.spu_offering_class
    </select>

    <select id="listProductRealNameSell" resultType="com.chinamobile.data.pojo.mapper.ProductRealNameSellDO">
        SELECT
        ss.real_product_name realProductName,
        oi.spu_offering_class spuOfferingClass,
        CASE
            WHEN oi.spu_offering_class = 'A04' THEN '产品增值服务包'
            WHEN oi.spu_offering_class = 'A06' THEN '联合销售'
            WHEN oi.spu_offering_class = 'A07' THEN '合同履约'
            WHEN oi.spu_offering_class = 'A08' THEN 'OneNET独立服务'
            WHEN oi.spu_offering_class = 'A09' THEN 'OnePark独立'
            WHEN oi.spu_offering_class = 'A12' THEN '行车卫士标准产品'
            WHEN occ.spu_offering_class = 'A13' THEN '软件服务'
            WHEN occ.spu_offering_class = 'A14' THEN 'OneCyber标准产品'
            ELSE '其他'
        END offeringClassName,
        pp.name productProperty,
        SUM(oa.sku_quantity * oa.atom_quantity * oa.atom_price) realNameSell
        FROM
        order_2c_atom_info oa
        JOIN order_2c_info oi ON oa.order_id = oi.order_id
        JOIN atom_offering_info aoi ON aoi.offering_code = oa.atom_offering_code and oa.spu_offering_code = aoi.spu_code AND oa.sku_offering_code = aoi.sku_code
        JOIN atom_std_service ass ON ass.atom_id = aoi.id
        JOIN standard_service ss ON ass.std_service_id = ss.id
        LEFT JOIN  product_property pp ON ss.product_property_id = pp.id
        WHERE
        ((oa.order_type = '01' and oa.order_status != 8)  or ((oa.order_type = '00' or oa.order_type = '02' or oa.order_type = '03') and oa.order_status in (12,7)))
        <if test="productRealNameSellParam.startTime != null and productRealNameSellParam.startTime !=  ''">
            and
            CASE
            when oa.order_type ='01' then substring(oa.create_time,1,8) <![CDATA[ >= ]]> #{productRealNameSellParam.startTime}
            when (oa.order_type = '00' or oa.order_type = '02' or oa.order_type = '03') then substring(oa.valet_order_complete_time,1,8) <![CDATA[ >= ]]> #{productRealNameSellParam.startTime}
            ELSE substring(oa.create_time,1,8) <![CDATA[ >= ]]> #{productRealNameSellParam.startTime}
            END
        </if>
        <if test="productRealNameSellParam.endTime != null and productRealNameSellParam.endTime != ''">
            and
            CASE
            when oa.order_type ='01' then substring(oa.create_time,1,8) <![CDATA[ <= ]]> #{productRealNameSellParam.endTime}
            when (oa.order_type = '00' or oa.order_type = '02' or oa.order_type = '03') then substring(oa.valet_order_complete_time,1,8) <![CDATA[ <= ]]> #{productRealNameSellParam.endTime}
            ELSE substring(oa.create_time,1,8) <![CDATA[ <= ]]> #{productRealNameSellParam.endTime}
            END
        </if>
        <if test="productRealNameSellParam.realProductName != null and productRealNameSellParam.realProductName !=  ''">
            and ss.real_product_name like '%${productRealNameSellParam.realProductName}%'
        </if>
        <if test="productRealNameSellParam.productPropertyId != null and productRealNameSellParam.productPropertyId != ''">
            and ss.product_property_id = #{productRealNameSellParam.productPropertyId}
        </if>
        <if test="productRealNameSellParam.spuOfferingClass != null and productRealNameSellParam.spuOfferingClass !=  ''">
            and oi.spu_offering_class = #{productRealNameSellParam.spuOfferingClass}
        </if>
        GROUP BY ss.real_product_name,oi.spu_offering_class
        HAVING realNameSell > 0
        <if test="productRealNameSellParam.startSell != null">
            and realNameSell <![CDATA[ >= ]]> #{productRealNameSellParam.startSell}
        </if>
        <if test="productRealNameSellParam.endSell != null">
            and realNameSell  <![CDATA[ <= ]]> #{productRealNameSellParam.endSell}
        </if>
        ORDER BY realNameSell DESC
    </select>

    <select id="listDepartmentSell" resultType="com.chinamobile.data.pojo.mapper.DepartmentSellDO">
        SELECT
            d.id departmentId,
            d.short_name departmentName,
            IFNULL(ds.provinceSell, 0) provinceSell,
            IFNULL(ds.networkSell, 0) networkSell
        FROM
            department d
        LEFT JOIN
        (
            SELECT
                ss.product_department_id,
                SUM(oai.sku_quantity * oai.atom_price * oai.atom_quantity) provinceSell,
                SUM(oai.sku_quantity * oai.atom_settle_price * oai.atom_quantity) networkSell
            FROM
                order_2c_atom_info oai,
                order_2c_info oi,
                atom_offering_info aoi,
                atom_std_service ass,
                standard_service ss,
                product_property pp
            WHERE
            ((oai.order_type = '01' and oai.order_status != 8)  or ((oai.order_type = '00' or oai.order_type = '02' or oai.order_type = '03') and oai.order_status in (12,7)))
            AND oi.order_id = oai.order_id
            AND oi.spu_offering_class IN ('A04' , 'A06', 'A07', 'A08', 'A09','A12','A14')
            AND oai.spu_offering_code = aoi.spu_code
            AND oai.sku_offering_code = aoi.sku_code
            AND oai.atom_offering_code = aoi.offering_code
            AND aoi.id = ass.atom_id
            AND ass.std_service_id = ss.id
            AND ss.product_property_id = pp.id
            <if test="productRunParam.startTime != null and productRunParam.startTime !=  ''">
                and
                CASE
                when oai.order_type ='01' then substring(oai.create_time,1,8) <![CDATA[ >= ]]> #{productRunParam.startTime}
                when (oai.order_type = '00' or oai.order_type = '02' or oai.order_type = '03') then substring(oai.valet_order_complete_time,1,8) <![CDATA[ >= ]]> #{productRunParam.startTime}
                ELSE substring(oai.create_time,1,8) <![CDATA[ >= ]]> #{productRunParam.startTime}
                END
            </if>
            <if test="productRunParam.endTime != null and productRunParam.endTime != ''">
                and
                CASE
                when oai.order_type ='01' then substring(oai.create_time,1,8) <![CDATA[ <= ]]> #{productRunParam.endTime}
                when (oai.order_type = '00' or oai.order_type = '02' or oai.order_type = '03') then substring(oai.valet_order_complete_time,1,8) <![CDATA[ <= ]]> #{productRunParam.endTime}
                ELSE substring(oai.create_time,1,8) <![CDATA[ <= ]]> #{productRunParam.endTime}
                END
            </if>
            GROUP BY ss.product_department_id
        ) ds ON ds.product_department_id = d.id
        ORDER BY d.id ASC
    </select>

    <select id="getFirstOrderTime" resultType="java.lang.String">
        SELECT MIN(create_time) FROM order_2c_atom_info
    </select>

    <select id="getOrderProvinceCount" parameterType="java.lang.String"  resultType="java.lang.Integer">
        SELECT
        count(be_id) totalCount
        FROM
        (
        SELECT
        be_id
        FROM
        order_2c_atom_info
        WHERE
        ((order_type = '01' and order_status != 8)  or ((order_type = '00' or order_type = '02' or order_type = '03') and order_status in (12,7)))
        <if test="startTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ >= ]]> #{startTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ >= ]]> #{startTime}
            ELSE create_time <![CDATA[ >= ]]> #{startTime}
            END
        </if>
        <if test="endTime != null">
            and
            CASE
            when order_type ='01' then create_time <![CDATA[ <= ]]> #{endTime}
            when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ <= ]]> #{endTime}
            ELSE create_time <![CDATA[ <= ]]> #{endTime}
            END
        </if>
        GROUP BY  be_id
        ) occ
    </select>
</mapper>