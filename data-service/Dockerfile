# dockerfile配置
#FROM hub.iot.chinamobile.com/runtime/jre:8
#解决验证码的API用到了AWT的东西，openjdk基础镜像无AWT组件
FROM hub.iot.chinamobile.com/ai_team/iotmall-jdk:v1.0.1
#时区设置
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
&& echo "Asia/Shanghai" > /etc/timezone
COPY ./data-service/target/data-service.jar /opt/java/data-service.jar
WORKDIR /opt/java/
ENTRYPOINT ["java","-XX:+UnlockExperimentalVMOptions", "-XX:+UseCGroupMemoryLimitForHeap","-jar","data-service.jar"]
EXPOSE 9696
