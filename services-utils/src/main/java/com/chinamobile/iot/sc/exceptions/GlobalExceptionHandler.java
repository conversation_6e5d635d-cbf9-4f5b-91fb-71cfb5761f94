package com.chinamobile.iot.sc.exceptions;

import com.chinamobile.iot.sc.annotation.LogAspectIgnore;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.netflix.client.ClientException;
import feign.FeignException;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.http.fileupload.impl.FileSizeLimitExceededException;
import org.apache.tomcat.util.http.fileupload.impl.SizeLimitExceededException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;
import java.net.ConnectException;
import java.net.URLEncoder;
import java.util.Objects;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.*;


/**
 * 全局异常拦截处理
 */
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
	@ExceptionHandler(Throwable.class)
	@ResponseBody
	@LogAspectIgnore
	public BaseAnswer handleStatusException(Throwable ex) {
        //异常错误检查
        ExcepStatus status = checkError2Status(ex);
		Throwable rootThrowable = ex;
		if(ex instanceof BusinessException) {
            //对于异常，在头部添加message以及statCode，方便前端针对类似二进制流接收类型时处理异常处理
            final ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            final HttpServletResponse response = requestAttr.getResponse();
            if (!response.containsHeader("stateCode")) {
                response.addHeader("stateCode",((BusinessException)ex).getStatus().getStateCode());
            }

            if (!response.containsHeader("message")) {
                try {
                    response.addHeader("message", URLEncoder.encode(((BusinessException)ex).getStatus().getMessage(),"UTF-8"));
                } catch (Exception exception) {
                    exception.printStackTrace();
                }
            }

			rootThrowable = ((BusinessException)ex).getRootException();
		}
        if(rootThrowable == null && (Objects.equals(PARAM_ERROR,status)
                || Objects.equals(SPECIAL_SERVER_ERROR_NO_HANDLE,status) )) {
            rootThrowable = ex;
        }
        if(rootThrowable != null) {
            final ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            final HttpServletRequest request = requestAttr.getRequest();
            log.error("异常堆栈信息，uri:{}", request.getRequestURI(), rootThrowable);
        }
        return new BaseAnswer<>().setStatus(status);
	}

    /**
     * 异常检查
     * @param ex
     * @return
     */
    public  static ExcepStatus checkError2Status(Throwable ex) {
        if(ex instanceof BusinessException) {
            return ((BusinessException)ex).getStatus();
        }  else if(ex instanceof RetryableException ||   ex instanceof FeignException) {
            return SYSTEM_BUSY;
        }  else if(ex instanceof MultipartException) {
            Throwable rootCause = ((MultipartException)ex).getRootCause();
            //这个请求大小
            if (rootCause instanceof SizeLimitExceededException){
                //这个返回存在问题：无法返回
                return EXCEED_MAX_REQUEST_SIZE;
            }
            else if(rootCause instanceof FileSizeLimitExceededException){
                //文件大小
                return EXCEED_MAX_FILE_SIZE;
            } else {
                return PARAM_ERROR;
            }
        }else if(ex instanceof MethodArgumentNotValidException) {
            //校验body出现的异常
            MethodArgumentNotValidException te = (MethodArgumentNotValidException) ex;
            FieldError error = te.getBindingResult().getFieldError();
            if(error!=null){
                String defaultMessage = error.getDefaultMessage();
                return ExcepStatus.createInstance(PARAM_ERROR.getStateCode(), defaultMessage);
            }
            return PARAM_ERROR;
        }else if(ex instanceof BindException){
            //校验form-data出现的异常
            //校验出错
            BindException bindException = (BindException)ex;
            FieldError fieldError = bindException.getBindingResult().getFieldError();
            String defaultMessage = fieldError.getDefaultMessage();
            return ExcepStatus.createInstance(PARAM_ERROR.getStateCode(),defaultMessage);
        }else if (ex instanceof ConstraintViolationException){
            //校验param出现的异常
            ConstraintViolationException constraintViolationException = (ConstraintViolationException)ex;
            String message = constraintViolationException.getMessage();
            return ExcepStatus.createInstance(PARAM_ERROR.getStateCode(),message);
        }else if(ex instanceof HttpClientErrorException.NotFound) {
            //404 服务不可用
            return NOT_FOUND;
        }else if(ex instanceof HttpMediaTypeNotSupportedException){
            //Content type类型不对
            return CONTENT_TYPE_ERROR;
        } else if(ex instanceof HttpRequestMethodNotSupportedException){
            //POST/GET等方法类型错误
            return METHOD_TYPE_ERROR;
        }else if(ex instanceof MissingServletRequestParameterException){
            MissingServletRequestParameterException paramEx = (MissingServletRequestParameterException) ex;
            return ExcepStatus.createInstance(PARAM_ERROR.getStateCode(),"参数:"+paramEx.getParameterName()+"不能为空");
        }else if(ex instanceof MissingRequestHeaderException){
            MissingRequestHeaderException exception = (MissingRequestHeaderException) ex;
            String headerName = exception.getHeaderName();
            return ExcepStatus.createInstance(PARAM_ERROR.getStateCode(),"请求头:"+headerName+"不能为空");
        }
        else{
            Throwable rootCase = ex.getCause();
            if (rootCase instanceof ClientException) {
                return SPECIAL_SERVER_ERROR_NO_HANDLE;
            } else if (rootCase instanceof ConnectException){
                return SYSTEM_BUSY;
            } else {
                return INTERNAL_ERROR;
            }
        }
    }
}
