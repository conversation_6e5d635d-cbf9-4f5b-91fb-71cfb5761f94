package com.chinamobile.iot.sc.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ByteArrayUpload {
    @NotEmpty(message = "字节数组信息不能为空")
    private byte[] bytes;
    @NotEmpty(message = "存储的文件名称不能为空")
    private String fileName;
    private boolean isCover = true;
    //过期时间 -1 无过期,单位天
    private int expiredDay = -1;

    public ByteArrayUpload setExpiredDay(int expiredDay) {
        if(expiredDay>0)
            this.expiredDay = expiredDay;
        return  this;
    }
    public ByteArrayUpload(byte[] bytes, String fileName, boolean isCover) {
        this.bytes = bytes;
        this.fileName = fileName;
        this.isCover = isCover;
    }

    public ByteArrayUpload(byte[] bytes, String fileName) {
        this.bytes = bytes;
        this.fileName = fileName;
    }

}
