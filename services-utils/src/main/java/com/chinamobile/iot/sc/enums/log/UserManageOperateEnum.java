package com.chinamobile.iot.sc.enums.log;

/**
 * 用户管理操作枚举
 * <AUTHOR>
 */
public enum UserManageOperateEnum {

    /**操作枚举*/
    ACCOUNT_PERMISSION_MANAGE(0,"账户权限管理"),
    USER_ACCOUNT_MANAGE(1,"用户账号管理"),
    USER_PERMISSION_MANAGE(2,"角色权限管理"),
    USER_OPERATION_MANAGE(3,"运营账号管理"),
    USER_PARTNER_MANAGE(4,"合作伙伴管理/账号管理"),
    USER_SYSTEM_QUERY_MANAGE(5,"系统账号查询"),
    ;

    public Integer code;
    public String name;

    UserManageOperateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static UserManageOperateEnum fromCode(Integer code){
        UserManageOperateEnum[] values = UserManageOperateEnum.values();
        for (UserManageOperateEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return value;
            }
        }
        return null;
    }
}
