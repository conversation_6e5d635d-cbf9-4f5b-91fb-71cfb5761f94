package com.chinamobile.iot.sc.util;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.Arrays;

/**
 * @Author: YSC
 * @Date: 2021/11/18 16:54
 * @Description: IOT加解密工具类
 */
@Slf4j
public class IOTEncodeUtils {
    private static final String Algorithm = "DESede";

    public static String encryptIOTMessage(String secretContent, String stringKey){
        if (StringUtils.isEmpty(secretContent)) {
            return secretContent;
        }
        try {
            byte[] key = Arrays.copyOf(stringKey.getBytes(StandardCharsets.UTF_8), 24);
            SymmetricCrypto des = new SymmetricCrypto(SymmetricAlgorithm.DESede, key);
            //加密
            byte[] encrypt = des.encrypt(secretContent, StandardCharsets.UTF_8);
            String encrptStr = new String(HexUtil.encodeHex(encrypt));
            return Base64.encode(encrptStr);
        } catch (Exception e) {
            log.error("解密失败，解密原文:{}", secretContent);
            //解密失败直接返回密文
            return secretContent;
        }
    }

    public static String decryptIOTMessage(String secretContent, String stringKey) {
        if (StringUtils.isEmpty(secretContent)) {
            return secretContent;
        }
        try {
            byte[] hex = HexUtil.decodeHex(Base64.decodeStr(secretContent));

            byte[] key = Arrays.copyOf(stringKey.getBytes(StandardCharsets.UTF_8), 24);

            SymmetricCrypto des = new SymmetricCrypto(SymmetricAlgorithm.DESede, key);
//解密
            byte[] decrypt = des.decrypt(hex);
            return new String(decrypt, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("解密失败，解密原文:{}", secretContent);
            //解密失败直接返回密文
            return secretContent;
        }
    }

    /**商家加密的密文是upper之后再base64的，可以用此方法加密搜索商城密文*/
    public static String encryptIOTMessageWithUpper(String secretContent, String stringKey){
        if (StringUtils.isEmpty(secretContent)) {
            return secretContent;
        }
        try {
            byte[] key = Arrays.copyOf(stringKey.getBytes(StandardCharsets.UTF_8), 24);
            SymmetricCrypto des = new SymmetricCrypto(SymmetricAlgorithm.DESede, key);
            //加密
            byte[] encrypt = des.encrypt(secretContent, StandardCharsets.UTF_8);
            String encrptStr = new String(HexUtil.encodeHex(encrypt)).toUpperCase();
            return Base64.encode(encrptStr);
        } catch (Exception e) {
            log.error("加密失败，解密原文:{}", secretContent);
            //解密失败直接返回密文
            return secretContent;
        }
    }
    public static String encryptSM4(String encryptStr,String key,String iv){
        if (StringUtils.isEmpty(encryptStr)) {
            return "";
        }
        try{
            byte[] data = encryptStr.getBytes();
            byte[] keyBytes = key.getBytes(); // 16字节密钥
            byte[] ivBytes = iv.getBytes();
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("SM4/CBC/PKCS5Padding", BouncyCastleProvider.PROVIDER_NAME);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(keyBytes, "SM4"), new IvParameterSpec(ivBytes));

            return Base64.encode(cipher.doFinal(data));
        }catch (Exception e){
            log.error("加密失败，加密原文:{}", encryptStr);
            //解密失败直接返回密文
            return encryptStr;
        }
    }

    public static String decryptSM4(String decryptStr,String key,String iv){
        if (StringUtils.isEmpty(decryptStr)) {
            return "";
        }
        try{
            byte[] data = Base64.decode(decryptStr);
            byte[] keyBytes = key.getBytes(); // 16字节密钥
            byte[] ivBytes = iv.getBytes();
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("SM4/CBC/PKCS5Padding", BouncyCastleProvider.PROVIDER_NAME);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(keyBytes, "SM4"), new IvParameterSpec(ivBytes));

            return new String(cipher.doFinal(data));
        }catch (Exception e){
            log.error("解密失败，解密原文:{}", decryptStr);
            //解密失败直接返回密文
            return decryptStr;
        }
    }

    public static void main(String[] args) {
        System.out.println(decryptIOTMessage("QjgwQ0QwQjNEMUY0QzA2Qw==", "3D88F1C1AAE7"));
    }
}
