package com.chinamobile.iot.sc.enums.log;

/**
 * 订单管理操作枚举
 * <AUTHOR>
 */
public enum OrderManageOperateEnum {

    /**操作枚举*/
    MALL_SYNC_ORDER(0,"商城同步订单"),
    CONTRACT_PERFORMANCE_ORDER(1,"合同履约订单"),
    AFTER_SALE_ORDER(2,"售后服务订单"),
    ORDER_GET_ORDER(3,"订单接单"),
    GOODS_ORDER(4,"商品订单"),
    GOODS_ORDER_MAIN(5,"主商品订单")
    ;

    public Integer code;
    public String name;

    OrderManageOperateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static OrderManageOperateEnum fromCode(Integer code){
        OrderManageOperateEnum[] values = OrderManageOperateEnum.values();
        for (OrderManageOperateEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return value;
            }
        }
        return null;
    }
}
