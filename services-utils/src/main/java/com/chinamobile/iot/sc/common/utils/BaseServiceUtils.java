package com.chinamobile.iot.sc.common.utils;


import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import lombok.extern.slf4j.Slf4j;

/**
 * 基础工具类
 */
@Slf4j
public class BaseServiceUtils extends BaseUtils {

    /**
     * 处理异常结果
     * @param e
     * @return
     */
    public static BaseAnswer doExp(BusinessException e) {
        log.warn("异常错误：",e.getRootException()!=null?e.getRootException():e);
        return new BaseAnswer(e);
    }

    /**
     * 失败结果
     * @param status
     * @return
     */
    public static BaseAnswer doFailure(ExcepStatus status) {
        return new BaseAnswer(status);
    }

    /**
     * 正常结果
     * @return
     */
    public static BaseAnswer doSuccess() {
        return new BaseAnswer();
    }

    /**
     * 正常结果
     * @param data 结果数据
     * @return
     */
    public static <T> BaseAnswer<T> doSuccess(T data) {
        BaseAnswer<T> res =  new BaseAnswer();
        if(data!=null)
            res.setData(data);
        return res;
    }

    /**
     * 生成主键id
     * @return id号
     */
    public static String getId() {
        return SnowIdUtils.getInstance().nextStrId();
    }

    /**
     * 生成主键id：long型
     * @return
     */
    public static long getLongId() {
        return SnowIdUtils.getInstance().nextId();
    }

}
