package com.chinamobile.iot.sc.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class Base64Upload {
    @NotEmpty(message = "base64信息不能为空")
    private String base64;
    @NotEmpty(message = "存储的文件名称不能为空")
    private String fileName;
    private boolean isCover = true;
    //过期时间 -1 无过期,单位天
    private int expiredDay = -1;

    public Base64Upload setExpiredDay(int expiredDay) {
        if(expiredDay>0)
            this.expiredDay = expiredDay;
        return  this;
    }

    public Base64Upload(String base64,String fileName) {
        this.base64 = base64;
        this.fileName = fileName;
    }

    public Base64Upload(String base64,String fileName, boolean isCover) {
        this.base64 = base64;
        this.fileName = fileName;
        this.isCover = isCover;
    }

}
