package com.chinamobile.iot.sc.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 对应模块权限配置信息
 * @author: z<PERSON><PERSON><PERSON>
 * @data: 2021/8/8
 **/
@Data
public class PeimiData implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 模块
     */
    private String moduleId;

    /**
     * 具有的权限类型 1 表示有查询权限 2 表示有操作权限（增、修以及处理）3表示删除
     */
    private Integer authType;

    public PeimiData() {
    }

    public PeimiData(String moduleId, Integer authType) {
        this.moduleId = moduleId;
        this.authType = authType;
    }
}
