package com.chinamobile.iot.sc.feign;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.iot.*;
import com.chinamobile.iot.sc.mode.AddMessageParam;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.mode.LogisticsVO;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Service
@FeignClient(name = "supply-chain-iot-svc")
public interface IotFeignClient {
    @PostMapping("/osweb/order/logisticsSyncInternal")
    BaseAnswer<Void> logisticsSyncInternal(@RequestBody @Valid LogisticsInfoRequest request);

    @PostMapping("/osweb/order/confirmReturnOrderInternal")
    BaseAnswer<Void> confirmReturnOrderInternal(@RequestBody @Valid ConfirmReturnOrder confirmReturnOrder);

    /**
     * 判断用户是否绑定指定商品，判断用户是否还有未完成订单
     * true 包含
     * false 不包含
     *
     * @param userId
     * @return
     */
    @GetMapping("/osweb/internal/judgeInfoByUserId")
    BaseAnswer<Boolean> judgeInfoByUserId(@RequestParam("userId") String userId);

    @PostMapping("/osweb/product/internal/getShareUrl")
    BaseAnswer<String> getShareUrl(@RequestBody IotSearchMallLinkRequest request);
    /**
     * 获取在产品引入及产品上下架  审核中的产品
     * @param userId
     * @return
     */
    @GetMapping(value = "/osweb/newProduct/getInReview")
    BaseAnswer<Integer> getProductInReviewCount(@RequestParam("userId") String userId);

    @PostMapping ("/os/log/create")
    BaseAnswer<Void> createLog(@RequestBody @Valid CreateOperateRecordParam param);

    /**
     * 获取组装后的原子订单和合作伙伴列表
     * @param orderCooperatorInfoByGroupParam
     * @return
     */
    @GetMapping(value = "/osweb/orderCooperator/listCooperatorInfoByGroup")
    BaseAnswer<List<OrderCooperatorInfoByGroupDTO>> listCooperatorInfoByGroup(OrderCooperatorInfoByGroupParam orderCooperatorInfoByGroupParam);


    /**
     * 查询售后人员是否存在还在派单中的订单数量
     * @param installUserId
     * @return
     */
    @GetMapping(value = "/osweb/serviceOrder/installCount")
    BaseAnswer<Long> getInstallUserAfterSaleOrder(@RequestParam("installUserId") String installUserId);

    @PostMapping ("/os/orderservice/sendServiceOrderResult")
    BaseAnswer<String> sendServiceOrderResult(@RequestHeader(required = false,name = "options") Request.Options options,
                                              @RequestBody ServiceOrderResultRequest request);
    /**
     * 查询人员是否有未完成的保理融资流程
     * */
    @GetMapping ("/financing/judgeUserIfHaveEffectiveTrade")
    BaseAnswer<Boolean> judgeUserIfHaveEffectiveTrade(@RequestParam("userId") String userId);

    @PostMapping("/osweb/message/add")
    BaseAnswer<Void> addMessage(@RequestBody @Valid AddMessageParam param);

    /**
     * 判断某个用户是否有运行中的流程
     * @param userId
     * @return
     */
    @GetMapping("/osweb/product/flow/instance/hasRunningFlow")
    BaseAnswer<Boolean> hasRunningFlowInstance(@RequestParam("userId") String userId);

    /**
     * 获取当前库存模式名称
     */
    @GetMapping("/osweb/userkx/inventory/nowName")
    BaseAnswer<String> getInventoryCutNow();

    /**
     * 小程序查询库存
     */
    @PostMapping("/os/inventoryservice/qryInventoryFromMini")
    IOTAnswer qryInventoryFromMini(@RequestBody IOTRequest baseRequest);


    /**
     * 小程序查询库存
     */
    @GetMapping ("/osweb/order/logistics")
    BaseAnswer<LogisticsVO> getOrderLogisticsMessage(@RequestParam("logisticsCode") String logisticsCode,
                                                     @RequestParam("supplierName") String supplierName,
                                                     @RequestParam(value = "contactPhone",required = false) String contactPhone);

}
