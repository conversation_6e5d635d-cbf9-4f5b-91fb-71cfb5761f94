package com.chinamobile.iot.sc.feign;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.b2b.*;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

@Service
@FeignClient(name = "supply-chain-b2b-svc")
public interface B2BFeignClient {

    @PostMapping("/other/orderCreateInternal")
    BaseAnswer<Void> orderCreateInternal(@RequestBody @Valid OrderDTO orderDTO);

    @PostMapping("/other/orderSaleSnInternal")
    BaseAnswer<Void> orderSaleSnInternal(@RequestBody @Valid OrderSaleSnDTO orderSaleSnDTO);

    @PostMapping("/other/backOrCancelOrderInternal")
    BaseAnswer<Void> backOrCancelOrderInternal(@RequestBody @Valid OrderBackOrCancelDTO orderBackOrCancelDTO);

    @PostMapping("/other/updateReceiveGoodsOrderInternal")
    BaseAnswer<Void> updateReceiveGoodsOrderInternalInternal(@RequestBody @Valid OrderBackOrCancelDTO orderBackOrCancelDTO,
                                                             @RequestParam(value = "rcvContactOld") String rcvContactOld,
                                                             @RequestParam(value = "rcvContactPhoneOld") String rcvContactPhoneOld,
                                                             @RequestParam(value = "rcvContactAddressOld") String rcvContactAddressOld,
                                                             @RequestParam(value = "userId") String userId
                                                             );

    @PostMapping("/other/queryInventoryInternal")
    BaseAnswer<Qry3rdInventoryResp> queryInventoryInternal(@RequestBody @Valid QueryInventoryDTO qryDto,@RequestParam(value = "province") String provoince);

    @PostMapping("/other/reserveInventoryInternal")
    BaseAnswer<Void> reserveInventoryInternal(@RequestBody @Valid ReserveInventoryDTO reserveDto,@RequestParam(value = "province") String province);

    @PostMapping("/other/releaseInventoryInternal")
    BaseAnswer<Void> releaseInventoryInternal(@RequestParam(value = "bookId") String bookId);

    @PostMapping("/other/order/install/syncToHenan")
    BaseAnswer<Void> syncToHenan(@RequestBody HenanZwSendOrderBusiParam param);

}
