package com.chinamobile.iot.sc.mode;

import lombok.Data;

/**
 * @Author: YSC
 * @Date: 2021/11/5 10:03
 * @Description: IOT请求体
 */
@Data
public class IOTRequest {
    /**
     * 消息序列，唯一标识一个请求消息。	格式：BEID+SEQ
     * SEQ只支持数字、字母和下划线。
     */
    private String messageSeq;
    /**
     * 路由类型	必填，
     * 1 按照省份路由
     * 2 按号码路由
     * 3 按操作员路由
     */
    private String routeType;
    /**
     * 路由省份或卡号	必填，
     * Route_type为1，则入参为beid;
     * Route_type为2，则入参为物联卡号码;
     * Route_type为3.则入参为营业员工号
     */
    private String routeValue;
    /**
     * 鉴权用户标识
     * 用户名及密码在各自网元安装时预置
     * 暂不启用;
     */
    private String loginSystemCode;
    /**
     * 鉴权用户密码	加密算法：
     * 密码摘要
     * 摘要算法如下Hash算法：PBKDF2
     * Password = Base64 (PBKDF2 (username + “$” + password )).
     * HashAlg –推荐SHA256，
     * Salt     – 可协商配置
     * Count – 可协商配置，推荐1000
     * dkLen – 可协商配置
     * 暂不启用;
     */
    private String password;
    /**
     * 请求方IP地址。
     */
    private String remoteIP;
    /**
     * 操作员标识。
     */
    private String operatorId;
    /**
     * 省编码
     */
    private String beId;
    /**
     * 区域编码，参考4.2地市编码或4.7区县编码
     */
    private String regionId;
    /**
     * 鉴权签名
     */
    private String sign;
    /**
     * 接入渠道
     */
    private String channelId;
    /**
     * 符合查询条件的总记录数。
     * 首次查询时该字段取值为“0”，
     * 返回满足查询条件的总记录数。
     * 后续查询首次查询结果记录时，固定填写总记录数。
     * 列表查询类接口必传字段
     */
    private String totalNum;
    /**
     * 起始游标，即分页查询时的起始记录行号。
     * 首次查询传入“0”。
     * 后续查询其他分页时传入该分页的第一条记录的序号。
     * 返回记录中第一条记录序号为“0”，后续依次排序。
     * 例如每页为10条记录，查询第二页时应传入“10”。
     * 列表查询类接口必传字段
     */
    private String startNum;
    /**
     * 每页记录数。最大不超过“10”。
     * 列表查询类接口必传字段
     */
    private String pageSize;
    /**
     * 请求体内容
     */
    private String content;
}
