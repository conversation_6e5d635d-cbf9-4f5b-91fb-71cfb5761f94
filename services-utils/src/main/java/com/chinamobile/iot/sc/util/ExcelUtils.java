package com.chinamobile.iot.sc.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.poifs.crypt.EncryptionInfo;
import org.apache.poi.poifs.crypt.EncryptionMode;
import org.apache.poi.poifs.crypt.Encryptor;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;


/**
 * Excel导入导出工具类
 */
@Slf4j
public class ExcelUtils {

    /**
     * excel 导出
     *
     * @param list     数据列表
     * @param fileName 导出时的excel名称
     * @param response
     */
    public static CommonResponseUtils exportExcel(List<Map<String, Object>> list, String fileName, HttpServletResponse response) throws IOException {
        return defaultExport(list, fileName, response);
    }

    /**
     * 默认的 excel 导出
     *
     * @param list     数据列表
     * @param fileName 导出时的excel名称
     * @param response
     */
    public static CommonResponseUtils defaultExport(List<Map<String, Object>> list, String fileName, HttpServletResponse response) throws IOException {
        //把数据添加到excel表格中
        Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.HSSF);
        return downLoadExcel(fileName, response, workbook);
    }

    /**
     * excel 导出
     *
     * @param list         数据列表
     * @param pojoClass    pojo类型
     * @param fileName     导出时的excel名称
     * @param response
     * @param exportParams 导出参数（标题、sheet名称、是否创建表头，表格类型）
     */
    private static CommonResponseUtils defaultExport(List<?> list, Class<?> pojoClass, String fileName, HttpServletResponse response, ExportParams exportParams) throws IOException {
        //把数据添加到excel表格中
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, pojoClass, list);
        return downLoadExcel(fileName, response, workbook);
    }

    /**
     * excel 导出
     *
     * @param list         数据列表
     * @param pojoClass    pojo类型
     * @param fileName     导出时的excel名称
     * @param response
     * @param exportParams 导出参数（标题、sheet名称、是否创建表头，表格类型）
     */
    private static CommonResponseUtils defaultExportFileInput(List<?> list, Class<?> pojoClass, String fileName, HttpServletResponse response, ExportParams exportParams) throws IOException {
        //把数据添加到excel表格中
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, pojoClass, list);
        return downLoadExcelFileInput(fileName, response, workbook);
    }

    private static CommonResponseUtils defaultExportToLocal(List<?> list,
                                                            Class<?> pojoClass,
                                                            String fileName,
                                                            String filePath,
                                                            ExportParams exportParams) throws IOException {
        //把数据添加到excel表格中
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, pojoClass, list);
        return downLoadExcelToLocal(fileName, filePath, workbook);
    }

    /**
     * excel 导出
     *
     * @param list         数据列表
     * @param pojoClass    pojo类型
     * @param fileName     导出时的excel名称
     * @param exportParams 导出参数（标题、sheet名称、是否创建表头，表格类型）
     * @param response
     */
    public static CommonResponseUtils exportExcel(List<?> list, Class<?> pojoClass, String fileName, ExportParams exportParams, HttpServletResponse response) throws IOException {
        return defaultExport(list, pojoClass, fileName, response, exportParams);
    }

    /**
     * excel 导出
     *
     * @param list      数据列表
     * @param title     表格内数据标题
     * @param sheetName sheet名称
     * @param pojoClass pojo类型
     * @param fileName  导出时的excel名称
     * @param response
     */
    public static CommonResponseUtils exportExcel(List<?> list, String title, String sheetName, Class<?> pojoClass, String fileName, HttpServletResponse response) throws IOException {
        return defaultExport(list, pojoClass, fileName, response, new ExportParams(title, sheetName, ExcelType.XSSF));
    }

    /**
     * excel 导出
     *
     * @param list      数据列表
     * @param title     表格内数据标题
     * @param sheetName sheet名称
     * @param pojoClass pojo类型
     * @param fileName  导出时的excel名称
     * @param response
     */
    public static CommonResponseUtils exportExcelFileInput(List<?> list, String title, String sheetName, Class<?> pojoClass, String fileName, HttpServletResponse response) throws IOException {
        return defaultExportFileInput(list, pojoClass, fileName, response, new ExportParams(title, sheetName, ExcelType.XSSF));
    }

    /**
     * excel 导出
     *
     * @param list           数据列表
     * @param title          表格内数据标题
     * @param sheetName      sheet名称
     * @param pojoClass      pojo类型
     * @param fileName       导出时的excel名称
     * @param isCreateHeader 是否创建表头
     * @param response
     */
    public static CommonResponseUtils exportExcel(List<?> list, String title, String sheetName, Class<?> pojoClass, String fileName, boolean isCreateHeader, HttpServletResponse response) throws IOException {
        ExportParams exportParams = new ExportParams(title, sheetName, ExcelType.XSSF);
        exportParams.setCreateHeadRows(isCreateHeader);
        return defaultExport(list, pojoClass, fileName, response, exportParams);
    }

    public static CommonResponseUtils exportExcelToLocal(List<?> list,
                                                         String title,
                                                         String sheetName,
                                                         Class<?> pojoClass,
                                                         String fileName,
                                                         boolean isCreateHeader,
                                                         String filePath) throws IOException {
        ExportParams exportParams = new ExportParams(title, sheetName, ExcelType.XSSF);
        exportParams.setCreateHeadRows(isCreateHeader);
        return defaultExportToLocal(list, pojoClass, fileName, filePath, exportParams);
    }

    /**
     * 导出加密的Excel
     *
     * @param workbook
     * @param fileName
     * @param response
     * @param password
     * @throws Exception
     */
    public static void exportEncryptExcel(Workbook workbook, String fileName, HttpServletResponse response, String password) throws Exception {
        if (password != null && !"".equals(password)) {
            // 文件名
            fileName = fileName + ".xlsx";

            // 创建一个字节数组输出流
            ByteArrayOutputStream workbookOutput = new ByteArrayOutputStream();
            workbook.write(workbookOutput);
            workbookOutput.flush();

            // 创建一个字节数组输入流
            ByteArrayInputStream workbookInput = new ByteArrayInputStream(workbookOutput.toByteArray());

            // 加密
            EncryptionInfo info = new EncryptionInfo(EncryptionMode.agile);
            Encryptor enc = info.getEncryptor();
            enc.confirmPassword(password);

            // 创建一个POIFS 文件系统
            POIFSFileSystem poifsFileSystem = new POIFSFileSystem();
            OPCPackage opc = OPCPackage.open(workbookInput);
            OutputStream outputStream = enc.getDataStream(poifsFileSystem);
            opc.save(outputStream);
            outputStream.close();
            opc.close();
            workbookOutput = new ByteArrayOutputStream();
            poifsFileSystem.writeFilesystem(workbookOutput);
            workbookOutput.flush();

            // 获取文件名并转码
            String name = URLEncoder.encode(fileName, "UTF-8");
            // 编码
            response.setCharacterEncoding("UTF-8");
            // 设置强制下载不打开
            response.setContentType("application/force-download");
            // 下载文件的默认名称
            response.setHeader("Content-Disposition", "attachment;filename=" + name);
            byte[] buff = new byte[1024];
            BufferedInputStream bufferedInputStream = null;
            OutputStream responseOutputStream = null;
            try {
                responseOutputStream = response.getOutputStream();
                bufferedInputStream = new BufferedInputStream(new ByteArrayInputStream(workbookOutput.toByteArray()));

                for (int i = bufferedInputStream.read(buff); i != -1; i = bufferedInputStream.read(buff)) {
                    responseOutputStream.write(buff, 0, buff.length);
                    responseOutputStream.flush();
                }
            } catch (IOException e) {
                log.error("文件导出失败,详情如下:{}", e.getMessage());
            } finally {
                if (bufferedInputStream != null) {
                    try {
                        bufferedInputStream.close();
                    } catch (IOException e) {
                        log.error("文件导出关闭输出流失败,详情如下:{}", e.getMessage());
                    }
                }
                if (workbookOutput != null) {
                    workbookOutput.close();
                    ;
                }
                if (workbookInput != null) {
                    workbookInput.close();
                }
                if (responseOutputStream != null) {
                    responseOutputStream.close();
                }
            }
        }
    }


    /**
     * excel下载
     *
     * @param fileName 下载时的文件名称
     * @param response
     * @param workbook excel数据
     */
    public static CommonResponseUtils downLoadExcel(String fileName, HttpServletResponse response, Workbook workbook) {
        OutputStream fos = null;
        try {
       /*     response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + "." + ExcelTypeEnum.XLSX.getValue(), "UTF-8"));
//            response.setHeader("Content-Disposition", "attachment;filename=" + toUtf8String(fileName + "." + ExcelTypeEnum.XLSX.getValue()));
            fos = response.getOutputStream();
            workbook.write(fos);*/
            fileName = fileName
                    + DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATE_FORMAT)
                    + ".xlsx";
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setHeader("content-type", "application/octet-stream");
            response.setHeader("Access-Control-Expose-Headers", "content-disposition");
            fos = response.getOutputStream();
            workbook.write(fos);
        } catch (Exception e) {
            log.error("导出{}时失败{}", fileName, e);
            return CommonResponseUtils.error(ResponseCode.EXPORT_FAILED, fileName + "Excel导出失败");
        } finally {
            //ExcelExportUtil.closeExportBigExcel();
            try {
                workbook.close();
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                log.error("{}Excel关闭导出异常{}", fileName, e);
                return CommonResponseUtils.error(ResponseCode.FAILURE, fileName + "Excel关闭导出异常");
            }
        }
        return new CommonResponseUtils();
    }

    /**
     * excel下载
     *
     * @param fileName 下载时的文件名称
     * @param response
     * @param workbook excel数据
     */
    public static CommonResponseUtils downLoadExcelFileInput(String fileName, HttpServletResponse response, Workbook workbook) {
        OutputStream fos = null;
        try {
       /*     response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + "." + ExcelTypeEnum.XLSX.getValue(), "UTF-8"));
//            response.setHeader("Content-Disposition", "attachment;filename=" + toUtf8String(fileName + "." + ExcelTypeEnum.XLSX.getValue()));
            fos = response.getOutputStream();
            workbook.write(fos);*/
            fileName = fileName
                    + ".xlsx";
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setHeader("content-type", "application/octet-stream");
            response.setHeader("Access-Control-Expose-Headers", "content-disposition");
            fos = response.getOutputStream();
            workbook.write(fos);
        } catch (Exception e) {
            log.error("导出{}时失败{}", fileName, e);
            return CommonResponseUtils.error(ResponseCode.EXPORT_FAILED, fileName + "Excel导出失败");
        } finally {
            //ExcelExportUtil.closeExportBigExcel();
            try {
                workbook.close();
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                log.error("{}Excel关闭导出异常{}", fileName, e);
                return CommonResponseUtils.error(ResponseCode.FAILURE, fileName + "Excel关闭导出异常");
            }
        }
        return new CommonResponseUtils();
    }

    private static CommonResponseUtils downLoadExcelToLocal(String fileName, String filePath, Workbook workbook) throws IOException {
        FileOutputStream fileOutputStream = null;
        try {
            fileName = fileName
                    + DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL)
                    + ".xlsx";
            File file = new File(filePath);
            if (!file.exists()) {
                file.mkdirs();
            }
            file = new File(filePath.concat(File.separator).concat(fileName));
            fileOutputStream = new FileOutputStream(file);
            workbook.write(fileOutputStream);
            String absolutePath = file.getAbsolutePath();
            return CommonResponseUtils.success().put("filePath", absolutePath);
        } catch (Exception e) {
            log.error("导出{}时失败{}", fileName, e);
            return CommonResponseUtils.error(ResponseCode.EXPORT_FAILED, fileName + "Excel导出失败");
        } finally {
            //ExcelExportUtil.closeExportBigExcel();

            try {
                workbook.close();
                if (fileOutputStream != null) {
                    fileOutputStream.close();
                }
            } catch (IOException e) {
                log.error("{}Excel关闭导出异常{}", fileName, e);
                return CommonResponseUtils.error(ResponseCode.FAILURE, fileName + "Excel关闭导出异常");
            }
        }
    }


    /**
     * excel 导入
     *
     * @param file      excel文件
     * @param pojoClass pojo类型
     * @param <T>
     * @return
     */
    public static <T> List<T> importExcel(MultipartFile file, Class<T> pojoClass) throws IOException {
        return importExcel(file, 1, 1, pojoClass);
    }

    /**
     * excel 导入
     *
     * @param filePath   excel文件路径
     * @param titleRows  表格内数据标题行
     * @param headerRows 表头行
     * @param pojoClass  pojo类型
     * @param <T>
     * @return
     */
    public static <T> List<T> importExcel(String filePath, Integer titleRows, Integer headerRows, Class<T> pojoClass) throws IOException {
        if (StringUtils.isBlank(filePath)) {
            return null;
        }
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        params.setNeedSave(true);
        params.setSaveUrl("/excel/");
        try {
            return ExcelImportUtil.importExcel(new File(filePath), pojoClass, params);
        } catch (NoSuchElementException e) {
            throw new IOException("模板不能为空");
        } catch (Exception e) {
            throw new IOException(e.getMessage());
        }
    }


    /**
     * excel 导入
     *
     * @param file       excel文件
     * @param titleRows  表格内数据标题行
     * @param headerRows 表头行
     * @param pojoClass  pojo类型
     * @param <T>
     * @return
     */
    public static <T> List<T> importExcel(MultipartFile file, Integer titleRows, Integer headerRows, Class<T> pojoClass) throws IOException {
        return importExcel(file, titleRows, headerRows, false, pojoClass);
    }

    /**
     * excel 导入
     *
     * @param file       上传的文件
     * @param titleRows  表格内数据标题行
     * @param headerRows 表头行
     * @param needVerfiy 是否检验excel内容
     * @param pojoClass  pojo类型
     * @param <T>
     * @return
     */
    public static <T> List<T> importExcel(MultipartFile file, Integer titleRows, Integer headerRows, boolean needVerfiy, Class<T> pojoClass) throws IOException {
        if (file == null) {
            return null;
        }
        try {
            return importExcel(file.getInputStream(), titleRows, headerRows, needVerfiy, pojoClass);
        } catch (Exception e) {
            throw new IOException(e.getMessage());
        }
    }

    /**
     * excel 导入
     *
     * @param inputStream 文件输入流
     * @param titleRows   表格内数据标题行
     * @param headerRows  表头行
     * @param needVerfiy  是否检验excel内容
     * @param pojoClass   pojo类型
     * @param <T>
     * @return
     */
    public static <T> List<T> importExcel(InputStream inputStream, Integer titleRows, Integer headerRows, boolean needVerfiy, Class<T> pojoClass) throws IOException {
        if (inputStream == null) {
            return null;
        }
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        params.setSaveUrl("/excel/");
        params.setNeedSave(true);
        // params.setNeedVerfiy(needVerfiy);
        try {
            return ExcelImportUtil.importExcel(inputStream, pojoClass, params);
        } catch (NoSuchElementException e) {
            throw new IOException("excel文件不能为空");
        } catch (Exception e) {
            throw new IOException(e.getMessage());
        }
    }

    /**
     * excel 导入
     *
     * @param inputStream 文件输入流
     * @param titleRows   表格内数据标题行
     * @param headerRows  表头行
     * @param needVerfiy  是否检验excel内容
     * @param pojoClass   pojo类型
     * @param <T>
     * @return
     */
    public static <T> List<T> importExcel(InputStream inputStream, Integer startSheetIndex, Integer titleRows, Integer headerRows, boolean needVerfiy, Class<T> pojoClass) throws IOException {
        if (inputStream == null) {
            return null;
        }
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        params.setSaveUrl("/excel/");
        params.setNeedSave(true);
        params.setStartSheetIndex(startSheetIndex);
        // params.setNeedVerfiy(needVerfiy);
        try {
            return ExcelImportUtil.importExcel(inputStream, pojoClass, params);
        } catch (NoSuchElementException e) {
            throw new IOException("excel文件不能为空");
        } catch (Exception e) {
            throw new IOException(e.getMessage());
        }
    }

    /**
     * 导入时有验证的操作
     *
     * @param inputStream
     * @param titleRows           表格内数据标题行
     * @param headerRows          表头行
     * @param iExcelVerifyHandler
     * @param pojoClass
     * @param <T>
     * @return
     * @throws IOException
     */
    public static <T> ExcelImportResult<T> importExcel(InputStream inputStream,
                                                       Integer titleRows,
                                                       Integer headerRows,
                                                       IExcelVerifyHandler iExcelVerifyHandler,
                                                       Class<T> pojoClass) throws Exception {
        if (inputStream == null) {
            return null;
        }
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        params.setStartRows(0);
        //params.setNeedVerfiy(true);
        params.setVerifyHandler(iExcelVerifyHandler);
        return ExcelImportUtil.importExcelMore(inputStream, pojoClass, params);
    }

    /**
     * 读取Excel文件
     *
     * @param inputStream
     * @param titleRows
     * @param headerRows
     * @param startRows           跳过第几行开始读取
     * @param iExcelVerifyHandler
     * @param pojoClass
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> ExcelImportResult<T> importExcel(InputStream inputStream,
                                                       Integer titleRows,
                                                       Integer headerRows,
                                                       Integer startRows,
                                                       IExcelVerifyHandler iExcelVerifyHandler,
                                                       Class<T> pojoClass) throws Exception {
        if (inputStream == null) {
            return null;
        }
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        params.setStartRows(startRows);
        //params.setNeedVerfiy(true);
        params.setVerifyHandler(iExcelVerifyHandler);
        return ExcelImportUtil.importExcelMore(inputStream, pojoClass, params);
    }

    public static boolean suffixCheck(String fileName) {
        if (fileName == null || "".equals(fileName)) {
            return false;
        }
        // 从最后一个点之后截取字符串
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        String[] suffixWhiteList = {"XLSX", "xlsx", "XLS", "xls"};
        // 白名单匹配
        boolean anyMatch = Arrays.stream(suffixWhiteList).anyMatch(x -> x.equalsIgnoreCase(suffix));
        return anyMatch;
    }

    public static <T> Workbook exportTradeOrderDetailFast(String sheetName, Class<T> clazz, List<T> dataList, boolean addOrderExportHeader) {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        CellStyle contentStyle = createCellStyleByColor(workbook, (short) -1);
        SXSSFSheet sheet = workbook.createSheet(sheetName);

        int dataRowNum = 0;
        if (addOrderExportHeader) {
            //添加订单导出需要的表头
            Row headerRow = sheet.createRow(dataRowNum);
            addTradeOrderDetailExportHeader(headerRow, sheet, workbook);
            dataRowNum++;
        }
        Field[] fields = clazz.getDeclaredFields();
        setData(dataList, contentStyle, sheet, fields, dataRowNum);
        return workbook;
    }

    /**
     * Excel 类型枚举
     */
    enum ExcelTypeEnum {
        XLS("xls"), XLSX("xlsx");
        private String value;

        ExcelTypeEnum(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    public static String toUtf8String(String s) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            if (c >= 0 && c <= 255) {
                sb.append(c);
            } else {
                byte[] b;
                try {
                    b = Character.toString(c).getBytes("utf-8");
                } catch (Exception ex) {
                    System.out.println(ex);
                    b = new byte[0];
                }
                for (int j = 0; j < b.length; j++) {
                    int k = b[j];
                    if (k < 0) {
                        k += 256;
                    }

                    sb.append("%" + Integer.toHexString(k).toUpperCase());
                }
            }
        }
        return sb.toString();
    }

    /**
     * 简化后的导出方法，去掉不必要的校验，速度快很多
     */
    public static <T> Workbook exportSimpleExcel(String sheetName, Class<T> clazz, List<T> dataList) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        CellStyle contentStyle = createCellStyleByColor(workbook, (short) -1);
        XSSFSheet sheet = workbook.createSheet(sheetName);
        Field[] fields = clazz.getDeclaredFields();
        setData(dataList, contentStyle, sheet, fields, 0);
        return workbook;
    }

    /**
     * 使用SXSSFWorkbook导出，速度比XSSFWorkbook更快,且不会内存溢出
     */
    public static <T> Workbook exportSimpleExcelFast(String sheetName, Class<T> clazz, List<T> dataList, boolean addOrderExportHeader) {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        CellStyle contentStyle = createCellStyleByColor(workbook, (short) -1);
        SXSSFSheet sheet = workbook.createSheet(sheetName);

        int dataRowNum = 0;
        if (addOrderExportHeader) {
            //添加订单导出需要的表头
            Row headerRow = sheet.createRow(dataRowNum);
            addOrderExportHeader(headerRow, sheet, workbook);
            dataRowNum++;
        }
        Field[] fields = clazz.getDeclaredFields();
        setData(dataList, contentStyle, sheet, fields, dataRowNum);
        return workbook;
    }


    /**
     * 导出库存的Excel组装
     *
     * @param sheetName
     * @param clazz
     * @param dataList
     * @param addInventoryExportHeader
     * @param <T>
     * @return
     */
    public static <T> Workbook exportInventorySimpleExcelFast(String sheetName, Class<T> clazz,
                                                              List<T> dataList, boolean addInventoryExportHeader) {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        CellStyle contentStyle = createCellStyleByColor(workbook, (short) -1);
        SXSSFSheet sheet = workbook.createSheet(sheetName);

        int dataRowNum = 0;
        if (addInventoryExportHeader) {
            //添加订单导出需要的表头
            Row headerRow = sheet.createRow(dataRowNum);
            addInventoryExportHeader(headerRow, sheet, workbook);
            dataRowNum++;
        }
        Field[] fields = clazz.getDeclaredFields();
        setData(dataList, contentStyle, sheet, fields, dataRowNum);
        return workbook;
    }

    /**
     * 使用SXSSFWorkbook导出，速度比XSSFWorkbook更快,且不会内存溢出
     */
    public static <T> Workbook exportProductSimpleExcelFast(String sheetName, Class<T> clazz, List<T> dataList, boolean addProductExportHeader) {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        CellStyle contentStyle = createCellStyleByColor(workbook, (short) -1);
        SXSSFSheet sheet = workbook.createSheet(sheetName);

        int dataRowNum = 0;
        if (addProductExportHeader) {
            //添加订单导出需要的表头
            Row headerRow = sheet.createRow(dataRowNum);
            addProductExportHeader(headerRow, sheet, workbook);
            dataRowNum++;
        }
        Field[] fields = clazz.getDeclaredFields();
        setData(dataList, contentStyle, sheet, fields, dataRowNum);
        return workbook;
    }

    public static <T> Workbook exportProductFlowSimpleExcelFast(String sheetName, Class<T> clazz, List<T> dataList, int addProductExportHeader) {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        CellStyle contentStyle = createCellStyleByColor(workbook, (short) -1);
        SXSSFSheet sheet = workbook.createSheet(sheetName);

        int dataRowNum = 0;

        if (addProductExportHeader != 0) {
            //添加订单导出需要的表头
            Row headerRow = sheet.createRow(dataRowNum);
            addProductFlowExportHeader(headerRow, sheet, workbook, addProductExportHeader);
            dataRowNum++;
        }
        Field[] fields = clazz.getDeclaredFields();
        setData(dataList, contentStyle, sheet, fields, dataRowNum);
        return workbook;
    }

    /**
     * 使用SXSSFWorkbook导出，速度比XSSFWorkbook更快,且不会内存溢出
     */
    public static <T> Workbook exportAfterMarketProductExcelFast(String sheetName, Class<T> clazz, List<T> dataList, boolean addOrderExportHeader) {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        CellStyle contentStyle = createCellStyleByColor(workbook, (short) -1);
        SXSSFSheet sheet = workbook.createSheet(sheetName);

        int dataRowNum = 0;
        if (addOrderExportHeader) {
            //添加订单导出需要的表头
            Row headerRow = sheet.createRow(dataRowNum);
            addAfterMarketProductExportHeader(headerRow, sheet, workbook);
            dataRowNum++;
        }
        Field[] fields = clazz.getDeclaredFields();
        setData(dataList, contentStyle, sheet, fields, dataRowNum);
        return workbook;
    }

    public static <T> Workbook exportStandardServiceSimpleExcelFast(String sheetName, Class<T> clazz, List<T> dataList) {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        CellStyle contentStyle = createCellStyleByColor(workbook, (short) -1);
        SXSSFSheet sheet = workbook.createSheet(sheetName);

        int dataRowNum = 0;
        Field[] fields = clazz.getDeclaredFields();
        Class<? super T> superclass = clazz.getSuperclass();
        if (superclass != null) {
            Field[] declaredFields = superclass.getDeclaredFields();
            for (Field declaredField : declaredFields) {
                fields = (Field[]) ArrayUtils.add(fields, declaredField);
            }
        }
        setData(dataList, contentStyle, sheet, fields, dataRowNum);
        return workbook;
    }

    /**
     * 使用SXSSFWorkbook导出，速度比XSSFWorkbook更快,且不会内存溢出
     */
    public static <T> Workbook exportUnionSellOrderExcelFast(String sheetName, Class<T> clazz, List<T> dataList, boolean addOrderExportHeader) {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        CellStyle contentStyle = createCellStyleByColor(workbook, (short) -1);
        SXSSFSheet sheet = workbook.createSheet(sheetName);

        int dataRowNum = 0;
        if (addOrderExportHeader) {
            //添加订单导出需要的表头
            Row headerRow = sheet.createRow(dataRowNum);
            addUnionSellOrderExportHeader(headerRow, sheet, workbook);
            dataRowNum++;
        }
        Field[] fields = clazz.getDeclaredFields();
        setData(dataList, contentStyle, sheet, fields, dataRowNum);
        return workbook;
    }

    private static <T> void setData(List<T> dataList, CellStyle contentStyle, Sheet sheet, Field[] fields, Integer startRow) {
        List<Field> fieldList = Arrays.stream(fields).filter(field -> {
            //支持多种表头注解
            Excel annotation = field.getAnnotation(Excel.class);
            ExcelProperty annotation1 = field.getAnnotation(ExcelProperty.class);
            return annotation != null || annotation1 != null;
        }).collect(Collectors.toList());
        Row headerRow = sheet.createRow(startRow);
        List<Field> dataFiledList = new ArrayList<>();
        //将反射得到的表头，插入第一行
        for (int i = 0; i < fieldList.size(); i++) {
            Field field = fieldList.get(i);
            field.setAccessible(true);
            Excel annotation = field.getAnnotation(Excel.class);
            ExcelProperty annotation1 = field.getAnnotation(ExcelProperty.class);
            dataFiledList.add(field);
            String name = annotation == null ? annotation1.value()[0] : annotation.name();
            Cell cell = headerRow.createCell(i);
            cell.setCellStyle(contentStyle);
            cell.setCellValue(name);
        }
        //设置表格内容
        for (int i = 0; i < dataList.size(); i++) {
            //因为有表头，所以这里行数+1
            Row dataRow = sheet.createRow(i + startRow + 1);
            T dto = dataList.get(i);
            for (int j = 0; j < dataFiledList.size(); j++) {
                Cell cell = dataRow.createCell(j);
                Field dataFiled = dataFiledList.get(j);
                try {
                    Object o = dataFiled.get(dto);
                    setCellValue(cell, o);
                    cell.setCellStyle(contentStyle);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private static void setCellValue(Cell cell, Object o) {
        if (o == null) {
            return;
        }
        if (o instanceof String) {
            cell.setCellValue((String) o);
        } else if (o instanceof Double) {
            cell.setCellValue((Double) o);
        } else if (o instanceof Long) {
            cell.setCellValue((Long) o);
        } else if (o instanceof Integer) {
            cell.setCellValue((Integer) o);
        } else if (o instanceof BigDecimal) {
            cell.setCellValue(o + "");
        } else {
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "数据类型错误");
        }
    }

    private static CellStyle createCellStyleByColor(Workbook workbook, short colorIndex) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER); //水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直对齐
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        if (colorIndex != -1) {
            cellStyle.setFillForegroundColor(colorIndex);//背景颜色
        }
        cellStyle.setWrapText(true);// 设置自动换行
        cellStyle.setBorderBottom(BorderStyle.THIN); //下边框
        cellStyle.setBorderLeft(BorderStyle.THIN); //左边框
        cellStyle.setBorderRight(BorderStyle.THIN); //右边框
        cellStyle.setBorderTop(BorderStyle.THIN); //上边框

       /* XSSFFont headerFont = (XSSFFont) workbook.createFont(); // 创建字体样式
        headerFont.setBold(true); //字体加粗
        headerFont.setFontName("黑体"); // 设置字体类型
        headerFont.setFontHeightInPoints((short) 12); // 设置字体大小
        cellStyle.setFont(headerFont); // 为标题样式设置字体样式*/
        return cellStyle;
    }


    /**
     * OS订单导出的表头信息，当更改或增减字段后，需要对应调整名称和对应列数字
     */
    public static void addOrderExportHeader(Row headerRow, Sheet sheet, Workbook workbook) {
        CellStyle headerStyle1 = createCellStyleByColor(workbook, IndexedColors.CORAL.getIndex());
        CellStyle headerStyle2 = createCellStyleByColor(workbook, IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
        String[] headerNames = new String[]{
                "客户经理信息", "", "", "",
                "客户信息", "", "", "", "",
                "订单计收信息", "", "",
                "订单基本信息", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
                "商品信息（SPU）", "", "",
                "商品信息（SKU）", "", "", "", "",
                "商品信息（原子）", "", "", "", "", "", "",
                "合作伙伴信息", "",
                "标准服务信息"};
        for (int i = 0; i < headerNames.length; i++) {
            Cell cell = headerRow.createCell(i);
            if ((i >= 0 && i <= 3) || (i >= 9 && i <= 11) || (i >= 34 && i <= 36) || (i >= 42 && i <= 48) || (i == 51)) {
                cell.setCellStyle(headerStyle1);
            } else {
                cell.setCellStyle(headerStyle2);
            }
            cell.setCellValue(headerNames[i]);
        }

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 3));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 4, 8));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 9, 11));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 12, 33));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 34, 35));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 36, 41));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 42, 48));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 49, 50));
    }

    public static void addTradeOrderDetailExportHeader(Row headerRow, Sheet sheet, Workbook workbook) {
        CellStyle headerStyle1 = createCellStyleByColor(workbook, IndexedColors.CORAL.getIndex());
        CellStyle headerStyle2 = createCellStyleByColor(workbook, IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
        String[] headerNames = new String[]{
                "客户信息", "",
                "订单计收信息",
                "订单基本信息", "", "", "", "", "", "", "", "", "", "",
                "商品信息（SPU）", "", "",
                "商品信息（SKU）", "", "",
                "商品信息（原子）", "", "", "",
                "移动省专公司信息", "", "",
                "合作伙伴信息", ""};
        for (int i = 0; i < headerNames.length; i++) {
            Cell cell = headerRow.createCell(i);
            if ((i >= 0 && i <= 1) || (i >= 3 && i <= 13) || (i >= 17 && i <= 19) || (i >= 24 && i <= 26)) {
                cell.setCellStyle(headerStyle1);
            } else {
                cell.setCellStyle(headerStyle2);
            }
            cell.setCellValue(headerNames[i]);
        }

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 1));
//        sheet.addMergedRegion(new CellRangeAddress(0, 0, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 3, 13));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 14, 16));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 17, 19));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 20, 23));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 24, 26));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 27, 28));
    }

    public static void addInventoryExportHeader(Row headerRow, Sheet sheet, Workbook workbook) {
        CellStyle headerStyle1 = createCellStyleByColor(workbook, IndexedColors.CORAL.getIndex());
        CellStyle headerStyle2 = createCellStyleByColor(workbook, IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
        String[] headerNames = new String[]{
                "商品信息（SPU）", "", "", "", "",
                "商品信息（SKU）", "", "", "", "", "",
                "商品信息（原子）", "", "", "", "", "", "", "", "", "",
                "所属合作伙伴", "",
                "库存信息", "", "", "", ""};
        for (int i = 0; i < headerNames.length; i++) {
            Cell cell = headerRow.createCell(i);
            if ((i >= 0 && i <= 4) || (i >= 11 && i <= 20) || (i >= 23 && i <= 27)) {
                cell.setCellStyle(headerStyle1);
            } else {
                cell.setCellStyle(headerStyle2);
            }
            cell.setCellValue(headerNames[i]);
        }

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 4));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 5, 10));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 11, 20));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 21, 22));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 23, 27));
    }


    /**
     * OS商品导出的表头信息，当更改或增减字段后，需要对应调整名称和对应列数字
     */
    public static void addProductExportHeader(Row headerRow, Sheet sheet, Workbook workbook) {
        CellStyle headerStyle1 = createCellStyleByColor(workbook, IndexedColors.CORAL.getIndex());
        CellStyle headerStyle2 = createCellStyleByColor(workbook, IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
        String[] headerNames = new String[]{
                "商品信息（SPU）", "", "", "","","","", "","","","", "","","",
                "商品信息（SKU）", "", "", "", "","","","","","","",
                "商品信息（原子）", "", "","","","","","","","","","","","",
                "所属合作伙伴", "",
                "所属标准服务", "", "", "", "", "", ""};
        for (int i = 0; i < headerNames.length; i++) {
            Cell cell = headerRow.createCell(i);
            if ((i >= 0 && i <= 13) || (i >= 25 && i <= 38) || (i >= 41 && i <= 47)) {
                cell.setCellStyle(headerStyle1);
            } else {
                cell.setCellStyle(headerStyle2);
            }
            cell.setCellValue(headerNames[i]);
        }

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 13));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 14, 24));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 25, 38));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 39, 40));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 41, 47));
    }

    /**
     * OS流程产品导出的表头信息，当更改或增减字段后，需要对应调整名称和对应列数字
     */
    public static void addProductFlowExportHeader(Row headerRow, Sheet sheet, Workbook workbook, Integer type) {
        CellStyle headerStyle1 = createCellStyleByColor(workbook, IndexedColors.CORAL.getIndex());
        CellStyle headerStyle2 = createCellStyleByColor(workbook, IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
        switch (type) {
            case 1:
            case 2:
                String[] headerNames = new String[]{
                        "商品SPU信息", "", "", "", "", "", "", "", "", "", "", "",
                        "商品规格SKU信息", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
                        "原子商品信息", "", "", "", "", "", "", "", "", "",
                        "商城上架信息", "", "", "",

                };
                for (int i = 0; i < headerNames.length; i++) {
                    Cell cell = headerRow.createCell(i);
                    if ((i >= 0 && i <= 11) || (i >= 32 && i <= 41)) {
                        cell.setCellStyle(headerStyle1);
                    } else {
                        cell.setCellStyle(headerStyle2);
                    }
                    cell.setCellValue(headerNames[i]);
                }

                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 11));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 12, 31));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 32, 41));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 42, 45));

                break;
            case 3:
                String[] headerNames3 = new String[]{
                        "商品SPU信息", "", "", "", "", "", "", "", "", "", "", "",
                        "商品规格SKU信息", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
                        "原子商品信息", "", "", "", "", "", "", "", "", "",
                        "商城上架信息", "", "", "",

                };
                for (int i = 0; i < headerNames3.length; i++) {
                    Cell cell = headerRow.createCell(i);
                    if ((i >= 0 && i <= 11) || (i >= 32 && i <= 41)) {
                        cell.setCellStyle(headerStyle1);
                    } else {
                        cell.setCellStyle(headerStyle2);
                    }
                    cell.setCellValue(headerNames3[i]);
                }

                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 11));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 12, 31));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 32, 41));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 42, 45));
                break;
            case 4:
                String[] headerNames4 = new String[]{
                        "商品SPU信息", "", "", "", "", "", "", "", "", "", "",
                        "商品规格SKU信息", "", "", "", "", "", "", "", "",
                        "（合同履约类）硬件原子商品信息", "", "", "", "", "", "", "", "",
                        "软件功能费原子商品信息", "", "", "", "", "", "", "", "", "", "", "", "",
                        "标准服务产品配置", "", "", "",
                        "合作伙伴信息", "", "",
                        "商城上架信息", "", "", "",
                };
                for (int i = 0; i < headerNames4.length; i++) {
                    Cell cell = headerRow.createCell(i);
                    if ((i >= 0 && i <= 10) || (i >= 20 && i <= 28) || (i >= 42 && i <= 45) || (i >= 49 && i <= 52)) {
                        cell.setCellStyle(headerStyle1);
                    } else {
                        cell.setCellStyle(headerStyle2);
                    }
                    cell.setCellValue(headerNames4[i]);
                }

                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 10));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 11, 19));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 20, 28));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 29, 41));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 42, 45));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 46, 48));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 49, 52));
                break;
            case 5:
                String[] headerNames5 = new String[]{
                        "商品SPU信息", "", "", "", "", "", "", "", "", "", "", "",
                        "商品规格SKU信息", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
                        "原子商品信息", "", "", "", "", "", "", "", "", "",
                        "各环节接口人信息", "", "", "", "", "", "", "", "",
                        "商品套餐详细信息", "", "", "", "", "", "", "", "", "", "", "", "", "",
                        "商城上架信息", "", "", "",

                };
                for (int i = 0; i < headerNames5.length; i++) {
                    Cell cell = headerRow.createCell(i);
                    if ((i >= 0 && i <= 10) || (i >= 28 && i <= 37) || (i >= 47 && i <= 60)) {
                        cell.setCellStyle(headerStyle1);
                    } else {
                        cell.setCellStyle(headerStyle2);
                    }
                    cell.setCellValue(headerNames5[i]);
                }

                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 10));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 11, 27));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 28, 37));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 38, 46));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 47, 60));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 61, 64));
                break;
            case 6:
                String[] headerNames6 = new String[]{
                        "商品SPU信息", "", "", "", "", "", "", "", "", "", "", "",
                        "商品规格SKU信息", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
                        "原子商品信息", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
                        "商城上架信息", "", "", "",

                };
                for (int i = 0; i < headerNames6.length; i++) {
                    Cell cell = headerRow.createCell(i);
                    if ((i >= 0 && i <= 11) || (i >= 29 && i <= 46)) {
                        cell.setCellStyle(headerStyle1);
                    } else {
                        cell.setCellStyle(headerStyle2);
                    }
                    cell.setCellValue(headerNames6[i]);
                }

                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 11));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 12, 28));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 29, 46));
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 47, 50));

                break;
            default:
                break;
        }

    }


    /**
     * 售后服务商品导出的表头信息，当更改或增减字段后，需要对应调整名称和对应列数字
     */
    public static void addAfterMarketProductExportHeader(Row headerRow, Sheet sheet, Workbook workbook) {
        CellStyle headerStyle1 = createCellStyleByColor(workbook, IndexedColors.CORAL.getIndex());
        CellStyle headerStyle2 = createCellStyleByColor(workbook, IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
        String[] headerNames = new String[]{
                "售后服务商品信息", "", "", "", "", "", "", "", "", "",
                "关联商品编码", "", "", "",
                "所属标准服务", "", "", "", "",
                "所属合作伙伴", "",
                "所属装维管理员", "",
                "所属省侧装维平台"
        };
        for (int i = 0; i < headerNames.length; i++) {
            Cell cell = headerRow.createCell(i);
            if ((i >= 0 && i <= 9) || (i >= 14 && i <= 18) ||  (i >= 21 && i <= 22)) {
                cell.setCellStyle(headerStyle1);
            } else {
                cell.setCellStyle(headerStyle2);
            }
            cell.setCellValue(headerNames[i]);
        }

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 9));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 10, 13));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 14, 18));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 19, 20));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 21, 22));
    }

    /**
     * OS订单导出的表头信息，当更改或增减字段后，需要对应调整名称和对应列数字
     */
    public static void addUnionSellOrderExportHeader(Row headerRow, Sheet sheet, Workbook workbook) {
        CellStyle headerStyle1 = createCellStyleByColor(workbook, IndexedColors.CORAL.getIndex());
        CellStyle headerStyle2 = createCellStyleByColor(workbook, IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
        CellStyle headerStyle3 = createCellStyleByColor(workbook, IndexedColors.PALE_BLUE.getIndex());
        String[] headerNames = new String[]{
                "订单计收信息", "",
                "订单基本信息", "", "", "", "", "", "",
                "商品信息（SPU）", "", "",
                "商品信息（SKU）", "", "", "",
                "商品信息（原子）", "", "", "", "", "", "",
                "合作伙伴信息", "", "",
                "结算信息", "", ""
        };
        for (int i = 0; i < headerNames.length; i++) {
            Cell cell = headerRow.createCell(i);
            if ((i >= 0 && i <= 1) || (i >= 9 && i <= 11) || (i >= 16 && i <= 22)) {
                cell.setCellStyle(headerStyle1);
            } else if (i >= 26 && i <= 28) {
                cell.setCellStyle(headerStyle3);
            } else {
                cell.setCellStyle(headerStyle2);
            }
            cell.setCellValue(headerNames[i]);
        }

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 1));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 2, 8));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 9, 11));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 12, 15));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 16, 22));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 23, 25));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 26, 28));
    }

    /**
     * 导出加密的Excel流
     *
     * @param workbook
     * @param password
     * @throws Exception
     */
    public static InputStream exportEncryptExcelStream(Workbook workbook, String password) throws Exception {
        if (password != null && !"".equals(password)) {

            // 创建一个字节数组输出流
            ByteArrayOutputStream workbookOutput = new ByteArrayOutputStream();
            workbook.write(workbookOutput);
            workbookOutput.flush();

            // 创建一个字节数组输入流
            ByteArrayInputStream workbookInput = new ByteArrayInputStream(workbookOutput.toByteArray());

            // 加密
            EncryptionInfo info = new EncryptionInfo(EncryptionMode.agile);
            Encryptor enc = info.getEncryptor();
            enc.confirmPassword(password);

            // 创建一个POIFS 文件系统
            POIFSFileSystem poifsFileSystem = new POIFSFileSystem();
            OPCPackage opc = OPCPackage.open(workbookInput);
            OutputStream outputStream = enc.getDataStream(poifsFileSystem);
            opc.save(outputStream);
            outputStream.close();
            opc.close();
            workbookOutput = new ByteArrayOutputStream();
            poifsFileSystem.writeFilesystem(workbookOutput);
            workbookOutput.flush();
            return new ByteArrayInputStream(workbookOutput.toByteArray());
        } else {
            throw new IOException("密码不能为空");
        }

    }

    /**
     * 去除数据的空格、回车、换行符、制表符
     *
     * @param str
     * @return
     */
    public static String replaceBlank(String str) {
        String dest = "";
        if (StringUtils.isNotEmpty(str)) {
            //空格\t、回车\n、换行符\r、制表符\t
            dest = str.replace("\t", "")
                    .replace("\n", "")
                    .replace("\r", "");
        }
        return dest;
    }

}
