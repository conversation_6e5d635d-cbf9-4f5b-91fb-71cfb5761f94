<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.RoleManageMapperExt">
    <resultMap id="roleManageItem" type="com.chinamobile.iot.sc.pojo.vo.RoleManageListItemVO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="roleId" jdbcType="VARCHAR" property="roleId"/>
        <result column="roleName" jdbcType="VARCHAR" property="roleName"/>
        <result column="system" jdbcType="VARCHAR" property="system"/>
        <result column="roleType" jdbcType="VARCHAR" property="roleType"/>
        <collection property="manageRoles" column="roleId"
                    ofType="com.chinamobile.iot.sc.pojo.vo.RoleManageListItemVO" select="getRoleManageInner">
            <result column="roleId" jdbcType="VARCHAR" property="roleId"/>
            <result column="roleName" jdbcType="VARCHAR" property="roleName"/>
            <result column="system" jdbcType="VARCHAR" property="system"/>
            <result column="roleType" jdbcType="VARCHAR" property="roleType"/>
        </collection>
    </resultMap>

    <select id="getRoleManageInner" parameterType="String" resultType="com.chinamobile.iot.sc.pojo.vo.RoleManageListItemVO">
        SELECT ri.id        roleId,
               ri.name      roleName,
               ri.system    system,
               ri.role_type roleType
        FROM role_manage rmi
                 LEFT JOIN role_info ri ON rmi.can_create_user_role_id = ri.id
        WHERE rmi.role_id = #{id}
    </select>

    <select id="getRoleManageByRoleId" resultMap="roleManageItem">
        SELECT
        r.id roleId,
        r.name roleName,
        r.system system,
        r.role_type roleType
        FROM role_info r
        where 1=1
        <if test="roleId != null and roleId != ''">
            and r.id = #{roleId}
        </if>

        <if test="roleName != null and roleName != ''">
            and r.name like concat('%', #{roleName},'%')
        </if>

    </select>

    <select id="selectDpName" resultType="com.chinamobile.iot.sc.pojo.dto.RoleDataParentIdDTO">
        SELECT parentDp.`name`,childDp.childName,childDp.parent_id parentId
        FROM(SELECT `name` as childName,
                    parent_id
             FROM data_permission dp WHERE id = #{id}) childDp
                JOIN data_permission parentDp on childDp.parent_id = parentDp.id
    </select>

</mapper>