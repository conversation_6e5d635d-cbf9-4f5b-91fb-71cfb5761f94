<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.AuthMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.Auth">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="auth_code" jdbcType="VARCHAR" property="authCode" />
    <result column="auth_name" jdbcType="VARCHAR" property="authName" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="group_code" jdbcType="VARCHAR" property="groupCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    id, auth_code, auth_name, parent_id, group_code
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.AuthExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from auth
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from auth
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from auth
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.AuthExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from auth
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.Auth">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into auth (id, auth_code, auth_name, 
      parent_id, group_code)
    values (#{id,jdbcType=VARCHAR}, #{authCode,jdbcType=VARCHAR}, #{authName,jdbcType=VARCHAR}, 
      #{parentId,jdbcType=VARCHAR}, #{groupCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.Auth">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into auth
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="authCode != null">
        auth_code,
      </if>
      <if test="authName != null">
        auth_name,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="groupCode != null">
        group_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="authCode != null">
        #{authCode,jdbcType=VARCHAR},
      </if>
      <if test="authName != null">
        #{authName,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="groupCode != null">
        #{groupCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.AuthExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from auth
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    update auth
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.authCode != null">
        auth_code = #{record.authCode,jdbcType=VARCHAR},
      </if>
      <if test="record.authName != null">
        auth_name = #{record.authName,jdbcType=VARCHAR},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=VARCHAR},
      </if>
      <if test="record.groupCode != null">
        group_code = #{record.groupCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    update auth
    set id = #{record.id,jdbcType=VARCHAR},
      auth_code = #{record.authCode,jdbcType=VARCHAR},
      auth_name = #{record.authName,jdbcType=VARCHAR},
      parent_id = #{record.parentId,jdbcType=VARCHAR},
      group_code = #{record.groupCode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.Auth">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    update auth
    <set>
      <if test="authCode != null">
        auth_code = #{authCode,jdbcType=VARCHAR},
      </if>
      <if test="authName != null">
        auth_name = #{authName,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="groupCode != null">
        group_code = #{groupCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.Auth">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    update auth
    set auth_code = #{authCode,jdbcType=VARCHAR},
      auth_name = #{authName,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=VARCHAR},
      group_code = #{groupCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into auth
    (id, auth_code, auth_name, parent_id, group_code)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.authCode,jdbcType=VARCHAR}, #{item.authName,jdbcType=VARCHAR}, 
        #{item.parentId,jdbcType=VARCHAR}, #{item.groupCode,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 01 16:20:55 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into auth (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'auth_code'.toString() == column.value">
          #{item.authCode,jdbcType=VARCHAR}
        </if>
        <if test="'auth_name'.toString() == column.value">
          #{item.authName,jdbcType=VARCHAR}
        </if>
        <if test="'parent_id'.toString() == column.value">
          #{item.parentId,jdbcType=VARCHAR}
        </if>
        <if test="'group_code'.toString() == column.value">
          #{item.groupCode,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>