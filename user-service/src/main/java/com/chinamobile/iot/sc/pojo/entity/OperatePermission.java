package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 操作权限表
 *
 * <AUTHOR>
public class OperatePermission implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    private String id;

    /**
     * 操作权限名称
     *
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    private String name;

    /**
     * 操作权限对应的前端模块或功能code
     *
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    private String authCode;

    /**
     * 接口路径
     *
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    private String uri;

    /**
     * 接口方法
     *
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    private String method;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..operate_permission.id
     *
     * @return the value of supply_chain..operate_permission.id
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public OperatePermission withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..operate_permission.id
     *
     * @param id the value for supply_chain..operate_permission.id
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..operate_permission.name
     *
     * @return the value of supply_chain..operate_permission.name
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public OperatePermission withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..operate_permission.name
     *
     * @param name the value for supply_chain..operate_permission.name
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..operate_permission.auth_code
     *
     * @return the value of supply_chain..operate_permission.auth_code
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public String getAuthCode() {
        return authCode;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public OperatePermission withAuthCode(String authCode) {
        this.setAuthCode(authCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..operate_permission.auth_code
     *
     * @param authCode the value for supply_chain..operate_permission.auth_code
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    /**
     * This method returns the value of the database column supply_chain..operate_permission.uri
     *
     * @return the value of supply_chain..operate_permission.uri
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public String getUri() {
        return uri;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public OperatePermission withUri(String uri) {
        this.setUri(uri);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..operate_permission.uri
     *
     * @param uri the value for supply_chain..operate_permission.uri
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public void setUri(String uri) {
        this.uri = uri;
    }

    /**
     * This method returns the value of the database column supply_chain..operate_permission.method
     *
     * @return the value of supply_chain..operate_permission.method
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public String getMethod() {
        return method;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public OperatePermission withMethod(String method) {
        this.setMethod(method);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..operate_permission.method
     *
     * @param method the value for supply_chain..operate_permission.method
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public void setMethod(String method) {
        this.method = method;
    }

    /**
     * This method returns the value of the database column supply_chain..operate_permission.create_time
     *
     * @return the value of supply_chain..operate_permission.create_time
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public OperatePermission withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..operate_permission.create_time
     *
     * @param createTime the value for supply_chain..operate_permission.create_time
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..operate_permission.update_time
     *
     * @return the value of supply_chain..operate_permission.update_time
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public OperatePermission withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..operate_permission.update_time
     *
     * @param updateTime the value for supply_chain..operate_permission.update_time
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", authCode=").append(authCode);
        sb.append(", uri=").append(uri);
        sb.append(", method=").append(method);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OperatePermission other = (OperatePermission) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getAuthCode() == null ? other.getAuthCode() == null : this.getAuthCode().equals(other.getAuthCode()))
            && (this.getUri() == null ? other.getUri() == null : this.getUri().equals(other.getUri()))
            && (this.getMethod() == null ? other.getMethod() == null : this.getMethod().equals(other.getMethod()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getAuthCode() == null) ? 0 : getAuthCode().hashCode());
        result = prime * result + ((getUri() == null) ? 0 : getUri().hashCode());
        result = prime * result + ((getMethod() == null) ? 0 : getMethod().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 16:50:22 CST 2023
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        authCode("auth_code", "authCode", "VARCHAR", false),
        uri("uri", "uri", "VARCHAR", false),
        method("method", "method", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Apr 18 16:50:22 CST 2023
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}