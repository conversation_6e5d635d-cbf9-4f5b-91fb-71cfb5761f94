package com.chinamobile.iot.sc.config;


import com.chinamobile.iot.sc.utils.GeneralUtils;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @version: V1.0
 * @author: zhangjin
 * @description:
 * @data: 2021/6/16 15:09
 **/
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "slide-code-config")
public class SlideCodeConfig {

    private String path;

    private String originImagePath = "/captcha/origin";

    private String templateImagePath = "/captcha/template";



    public String getOriginImagePath(){
        if(StringUtils.isBlank(path)){
            try {
                this.path = GeneralUtils.getJarPath();
            }catch (Exception e){

            }
        }
        return this.path + this.originImagePath;
    }


    public String getTemplateImagePath(){
        if(StringUtils.isBlank(path)){
            try {
                this.path = GeneralUtils.getJarPath();
            }catch (Exception e){

            }
        }
        return this.path + this.templateImagePath;
    }


}
