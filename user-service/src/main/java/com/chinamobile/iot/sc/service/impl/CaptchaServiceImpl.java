package com.chinamobile.iot.sc.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseUtils;
import com.chinamobile.iot.sc.common.utils.RegexUtil;
import com.chinamobile.iot.sc.config.SlideCodeConfig;
import com.chinamobile.iot.sc.config.SmsConfig;
import com.chinamobile.iot.sc.constant.StatusConstant;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.enums.UserUnifiedStatusEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.LoginOperateEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.utils.GeneralUtils;
import com.chinamobile.iot.sc.utils.IOTEncodeUtils;
import com.chinamobile.iot.sc.utils.ImageVerificationUtil;
import com.chinamobile.iot.sc.vo.ImageVerificationVo;
import com.chinamobile.iot.sc.vo.RedisSmsValidCode;
import com.chinamobile.iot.sc.vo.response.Data4ValidCode;
import com.google.code.kaptcha.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.constant.RedisLockConstant.*;

@Service
@Slf4j
public class CaptchaServiceImpl {
    //验证码有效时间
    private int expireTimeInSeconds = 5 * 60;
    @Value("${sms.editCodeTempId:106036}")
    private String editCodeTempId;
    @Resource
    private Producer captchaProducer;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private SlideCodeConfig slideCodeConfig;
    @Resource
    private SmsConfig smsConfig;
    @Autowired
    private SmsFeignClient smsFeignClient;

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserScreenMapper userScreenMapper;

    @Resource
    private UserInstallMapper userInstallMapper;

    @Resource
    private UserPartnerMapper userPartnerMapper;

    @Resource
    private UserProvinceBusinessMapper userProvinceBusinessMapper;

    @Value("${supply.des.key}")
    private String encryptKey;
    @Resource
    private LogService logService;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 获取图片验证码
     * @param request
     * @param response
     * @return
     */
    public BaseAnswer<Data4ValidCode> getKaptchaImage(HttpServletRequest request, HttpServletResponse response){
        BaseAnswer<Data4ValidCode> answer = new BaseAnswer<>();
        HttpSession session = request.getSession();
        response.setDateHeader("Expires", 0);
        // Set standard HTTP/1.1 no-cache headers.
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
        response.setHeader("Access-Control-Expose-Headers", "Session-D");

        // Set IE extended HTTP/1.1 no-cache headers (use addHeader).
        response.addHeader("Cache-Control", "post-check=0, pre-check=0");
        response.setHeader("Session-D", session.getId());

        // Set standard HTTP/1.0 no-cache header.
        response.setHeader("Pragma", "no-cache");
        // return a jpeg
        response.setContentType("image/jpeg");
        // create the text for the image
        String capText = captchaProducer.createText();
        String sessionId = session.getId();
        redisTemplate.opsForValue().set(BaseUtils.getCaptchaSessionIdKey(sessionId)
                , capText,expireTimeInSeconds < 1 ? 60 : expireTimeInSeconds, TimeUnit.SECONDS);
        //保存到缓存
        log.debug(session.getId()+"----------------->"+capText);
        BufferedImage image = captchaProducer.createImage(capText);
        //创建编码对象
        Base64.Encoder base64 = Base64.getEncoder();
        // 创建字符流
        ByteArrayOutputStream bs = new ByteArrayOutputStream();
        Data4ValidCode code = new Data4ValidCode();
        code.setSessionId(session.getId());
        // 写入字符流
        try {
            ImageIO.write(image, "jpg", bs);
            // 转码成字符串
            String imgsrc = base64.encodeToString(bs.toByteArray());
            code.setValidCodeImage("data:image/jpeg;base64,"+imgsrc);
        } catch (Exception e) {
            log.error("验证码获取失败--->>>",e);
            answer.setStatus(BaseErrorConstant.SERVER_INTERNAL_ERROR);
        }
        answer.setData(code);
        return answer;
    }

    /**
     * 验证
     * @param validCode
     * @param sessionId
     */
    public void valid(String validCode,String sessionId,String account){
        String captchaSessionIdKey = BaseUtils.getCaptchaSessionIdKey(sessionId);
        String code = (String)redisTemplate.opsForValue().get(captchaSessionIdKey);
        log.info("获取保存redis的图形验证码：{}",code);
        if(code == null){
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, BaseErrorConstant.VAILD_CODE_EXPIRED.getMessage(),account);
            throw new BusinessException(BaseErrorConstant.VAILD_CODE_EXPIRED);
        }
        //需要删除,防止一直使用该验证码
        redisTemplate.delete(captchaSessionIdKey);
        if(!validCode.equals(code)){
            //验证码错误
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, BaseErrorConstant.VAILD_CODE_VAILD_FAIL.getMessage(),account);
            throw new BusinessException(BaseErrorConstant.VAILD_CODE_VAILD_FAIL);
        }
    }

    /**
     * 用于os登录验证图形验证码
     * @param validCode
     * @param sessionId
     */
    public void validOs(String validCode,String sessionId,String account){
        String captchaSessionIdKey = BaseUtils.getCaptchaSessionIdKey(sessionId);
        String code = (String)redisTemplate.opsForValue().get(captchaSessionIdKey);
        if(code == null){
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, BaseErrorConstant.VAILD_CODE_EXPIRED.getMessage(),account);

            throw new BusinessException(BaseErrorConstant.VAILD_CODE_EXPIRED);
        }
        //需要删除,防止一直使用该验证码,os登录开始不用删图形验证码  在获取短信的时候在验证删除
      //  redisTemplate.delete(captchaSessionIdKey);
        if(!validCode.equals(code)){
            //验证码错误
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, BaseErrorConstant.VAILD_CODE_VAILD_FAIL.getMessage(),account);
            throw new BusinessException(BaseErrorConstant.VAILD_CODE_VAILD_FAIL);
        }
    }

    /**
     * 大屏手机号登录验证码验证
     * @param validCode
     * @param sessionId
     * @param account
     */
    public void validScreen(String validCode,String sessionId,String account){
        String captchaSessionIdKey = BaseUtils.getCaptchaSessionIdKey(sessionId);
        String code = (String)redisTemplate.opsForValue().get(captchaSessionIdKey);
        log.info("获取保存redis的图形验证码：{}",code);
        if(code == null){
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, BaseErrorConstant.VAILD_CODE_EXPIRED.getMessage(),account);
            throw new BusinessException(BaseErrorConstant.VAILD_CODE_EXPIRED);
        }
        //需要删除,防止一直使用该验证码
        redisTemplate.delete(captchaSessionIdKey);
        if(!validCode.equals(code)){
            //验证码错误
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.KANBAN_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, BaseErrorConstant.VAILD_CODE_VAILD_FAIL.getMessage(),account);
            throw new BusinessException(BaseErrorConstant.VAILD_CODE_VAILD_FAIL);
        }
    }


    /**
     * 获取滑动验证码
     * @param request
     * @param response
     * @param macro
     * @return
     */
    public BaseAnswer<ImageVerificationVo> getSlideImage(HttpServletRequest request, HttpServletResponse response, int macro) throws IOException {
        HttpSession session = request.getSession();
        if(macro == -1) {
            //状态码错误
            response.setStatus(400);
            return null;
        }
        response.setDateHeader("Expires", 0);

        // Set standard HTTP/1.1 no-cache headers.
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
        response.setHeader("Access-Control-Expose-Headers", "Session-D");

        // Set IE extended HTTP/1.1 no-cache headers (use addHeader).
        response.addHeader("Cache-Control", "post-check=0, pre-check=0");
        String sessionId = session.getId();
        response.setHeader("Session-D", sessionId);

        // Set standard HTTP/1.0 no-cache header.
        response.setHeader("Pragma", "no-cache");

        // return a jpeg
        response.setContentType("image/jpeg");
        ImageVerificationVo imageVerificationVo = null;
        try {
            File verifyImageImport = new File(slideCodeConfig.getOriginImagePath());
            File[] verifyImages = verifyImageImport.listFiles();

            Random random = new Random(System.currentTimeMillis());
            //  随机取得原图文件夹中一张图片
            File originImageFile = verifyImages[random.nextInt(verifyImages.length)];

            //  获取模板图片文件
            File templateImageFile = new File(slideCodeConfig.getTemplateImagePath() + "/template.png");

            //  获取描边图片文件
            File borderImageFile = new File(slideCodeConfig.getTemplateImagePath() + "/border.png");
            //  获取描边图片类型
            String borderImageFileType = "png";

            //  获取原图文件类型
            String originImageFileType = originImageFile.getName().substring(originImageFile.getName().lastIndexOf(".") + 1);
            //  获取模板图文件类型
            String templateImageFileType = "png";

            //  读取原图
            BufferedImage verificationImage = ImageIO.read(originImageFile);
            //  读取模板图
            BufferedImage readTemplateImage = ImageIO.read(templateImageFile);

            //  读取描边图片
            BufferedImage borderImage = ImageIO.read(borderImageFile);


            //  获取原图感兴趣区域坐标
            imageVerificationVo = ImageVerificationUtil.generateCutoutCoordinates(verificationImage, readTemplateImage);

            int y  = imageVerificationVo.getY();
            //  在分布式应用中，可将session改为redis存储
            imageVerificationVo.setMacro(macro);

            redisTemplate.opsForValue().set(BaseUtils.getCaptchaSessionIdKey(sessionId), JSONObject.toJSONString(imageVerificationVo)
                    ,expireTimeInSeconds < 1 ? 60 : expireTimeInSeconds, TimeUnit.SECONDS);
            log.info("slide_code,x:{},y:{},sessionId:{}",imageVerificationVo.getX(),imageVerificationVo.getY(),sessionId);
            //  根据原图生成遮罩图和切块图
            imageVerificationVo = ImageVerificationUtil.pictureTemplateCutout(originImageFile, originImageFileType
                    , templateImageFile, templateImageFileType, imageVerificationVo.getX(), imageVerificationVo.getY());

            //   剪切图描边
            imageVerificationVo = ImageVerificationUtil.cutoutImageEdge(imageVerificationVo, borderImage, borderImageFileType);
            imageVerificationVo.setY(y);
            imageVerificationVo.setOriginImage(null);
            imageVerificationVo.setSessionId(sessionId);
        }  catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new IOException("IO异常");
        }
        return new BaseAnswer().setData(imageVerificationVo);
    }


    /**
     * 验证滑动验证码
     * @param x
     * @param y
     * @param session
     */
    public void validSlideCode(Integer x, Integer y, String session){
        String captchaSessionIdKey = BaseUtils.getCaptchaSessionIdKey(session);
        String jsonStr = (String)redisTemplate.opsForValue().get(captchaSessionIdKey);
        if(StringUtils.isBlank(jsonStr)){
            throw new BusinessException(BaseErrorConstant.VAILD_CODE_EXPIRED);
        }
        ImageVerificationVo imageVerificationVo = JSONObject.parseObject(jsonStr,ImageVerificationVo.class);
        int threshold = 10;
        //检验验证码以及对应操作  x轴偏移大于误差允许值或者y轴偏移了。人为操作不可能可以移动y轴
        if((Math.abs(x - imageVerificationVo.getX()) > threshold) || !y.equals(imageVerificationVo.getY())) {
            throw new BusinessException(BaseErrorConstant.VAILD_CODE_VAILD_FAIL);
        }
        //需要删除,防止一直使用该验证码
        redisTemplate.delete(captchaSessionIdKey);
    }

    /**
     * 获取Os商城短信验证码
     * @param phone
     * @return
     */
    public BaseAnswer<Void> getSmsValidCode(String phone,HttpServletRequest request,String account){

        return redisUtil.smartLock(SMS_LOCK + phone, () -> {
            //验证图形,删除redis图形验证码
            String session = request.getParameter("sessionId");
            String validCode = request.getParameter("validCode");
            log.info("获取的request参数：session：{}，validCode：{}",session,validCode);
            valid(validCode,session,account);

            //校验手机是否注册
            if (!RegexUtil.regexPhone(phone)) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_ERROR.getMessage(),account);

                throw new BusinessException(StatusConstant.PHONE_ERROR);
            }
            //手机号是否被注册
            //手机号加密之后再进行匹配判断
            String encryptPhone = IOTEncodeUtils.encryptIOTMessage(phone,encryptKey);
            List<User> users = userMapper.selectByExample(new UserExample().createCriteria().andPhoneEqualTo(encryptPhone)
                            .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example()).stream()
                    .filter(item -> item.getUnifiedStatus() == null || item.getUnifiedStatus().equals(UserUnifiedStatusEnum.VALID.code))
                    .collect(Collectors.toList());
            List<UserPartner> partners = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andPhoneEqualTo(encryptPhone)
                    .andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example());
            List<UserProvinceBusiness> userProvinceBusinesses = userProvinceBusinessMapper.selectByExample(new UserProvinceBusinessExample().createCriteria()
                    .andPhoneEqualTo(encryptPhone).andIsLogoffEqualTo(false).example());
            if (CollectionUtils.isEmpty(users) && CollectionUtils.isEmpty(partners) && CollectionUtils.isEmpty(userProvinceBusinesses)) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_NOT_REGISTER.getMessage(),account);

                throw new BusinessException(StatusConstant.PHONE_NOT_REGISTER);
            }

            BaseAnswer<Void> answer  = new BaseAnswer<>();
            String redisKey = BaseUtils.getSMSValidKey(phone);
            //查看一分钟之内有没有发送短信
            String jsonStr = (String)redisTemplate.opsForValue().get(redisKey);
            if(StringUtils.isNotBlank(jsonStr)){
                RedisSmsValidCode redisSmsValidCode = JSONObject.parseObject(jsonStr, RedisSmsValidCode.class);
                if((System.currentTimeMillis() - redisSmsValidCode.getTime() < 60000)){
                    //超过1分钟
                    logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, "验证码请求过于频繁，请稍后再试",account);

                    throw new BusinessException(BaseErrorConstant.SYSTEM_BUSY, "验证码请求过于频繁，请稍后再试");
                }
            }
            Integer code = sendSmsValidCode(phone, smsConfig.getValidCodeTempId());
            log.info("sms_code,phone:{},code:{}",phone,code);
            RedisSmsValidCode redisSmsValidCode = new RedisSmsValidCode(code,System.currentTimeMillis());
            redisTemplate.opsForValue().set(redisKey,JSONObject.toJSONString(redisSmsValidCode), expireTimeInSeconds,TimeUnit.SECONDS);
            return answer;
        });
    }


    /**
     * 获取大屏登录时的短信验证码
     * @param phone
     * @return
     */
    public BaseAnswer<Void> getSmsValidCodeScreen(String phone, HttpServletRequest request, String account){
        return redisUtil.smartLock(SMS_SCREEN_LOCK + phone, () -> {
            //验证图形,删除redis图形验证码
            String session = request.getParameter("sessionId");
            String validCode = request.getParameter("validCode");
            log.info("获取的request参数：session：{}，validCode：{}, account: {}",session,validCode,account);
            validScreen(validCode,session,account);

            //校验手机是否注册
            if (!RegexUtil.regexPhone(phone)) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code,StatusConstant.PHONE_ERROR.getMessage(),phone);

                throw new BusinessException(StatusConstant.PHONE_ERROR);
            }
            //手机号是否被注册
            //手机号加密之后再进行匹配判断
            String encryptPhone = IOTEncodeUtils.encryptIOTMessage(phone,encryptKey);
            List<UserScreen> users = userScreenMapper.selectByExample(new UserScreenExample().createCriteria().andPhoneEqualTo(encryptPhone).andIsCancelEqualTo(false).andIsLogoffEqualTo(false).example());
            if (CollectionUtils.isEmpty(users)) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code,StatusConstant.PHONE_NOT_REGISTER.getMessage(),phone);

                throw new BusinessException(StatusConstant.PHONE_NOT_REGISTER);
            }

            BaseAnswer<Void> answer  = new BaseAnswer<>();
            String redisKey = BaseUtils.getSMSValidKey(phone);
            //查看一分钟之内有没有发送短信
            String jsonStr = (String)redisTemplate.opsForValue().get(redisKey);
            if(StringUtils.isNotBlank(jsonStr)){
                RedisSmsValidCode redisSmsValidCode = JSONObject.parseObject(jsonStr, RedisSmsValidCode.class);
                if((System.currentTimeMillis() - redisSmsValidCode.getTime() < 60000)){
                    //超过1分钟
                    logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code,"验证码请求过于频繁，请稍后再试",phone);

                    throw new BusinessException(BaseErrorConstant.SYSTEM_BUSY, "验证码请求过于频繁，请稍后再试");
                }
            }
            Integer code = sendSmsValidCode(phone, smsConfig.getScreenCodeTempId());
            log.info("sms_code,phone:{},code:{}",phone,code);
            RedisSmsValidCode redisSmsValidCode = new RedisSmsValidCode(code,System.currentTimeMillis());
            redisTemplate.opsForValue().set(redisKey,JSONObject.toJSONString(redisSmsValidCode), expireTimeInSeconds,TimeUnit.SECONDS);
            return answer;
        });
    }

    /**
     * 获取装维用户登录验证码
     * @param phone
     * @return
     */
    public BaseAnswer<Void> getSmsValidCodeInstall(String phone){

        return redisUtil.smartLock(SMS_INSTALL_LOCK + phone, () -> {
            //校验手机是否注册
            if (!RegexUtil.regexPhone(phone)) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code,StatusConstant.PHONE_ERROR.getMessage(),phone);

                throw new BusinessException(StatusConstant.PHONE_ERROR);
            }
            //手机号是否被注册
            //手机号加密之后再进行匹配判断
            String encryptPhone = IOTEncodeUtils.encryptIOTMessage(phone,encryptKey);
            List<UserPartner> userInstalls = userPartnerMapper.selectByExample(new UserPartnerExample().createCriteria().andPhoneEqualTo(encryptPhone).andIsCancelEqualTo(false).example());
            if (CollectionUtils.isEmpty(userInstalls)) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code,StatusConstant.PHONE_NOT_REGISTER.getMessage(),phone);

                throw new BusinessException(StatusConstant.PHONE_NOT_REGISTER);
            }

            BaseAnswer<Void> answer  = new BaseAnswer<>();
            String redisKey = BaseUtils.getSMSValidKey(phone);
            //查看一分钟之内有没有发送短信
            String jsonStr = (String)redisTemplate.opsForValue().get(redisKey);
            if(StringUtils.isNotBlank(jsonStr)){
                RedisSmsValidCode redisSmsValidCode = JSONObject.parseObject(jsonStr, RedisSmsValidCode.class);
                if((System.currentTimeMillis() - redisSmsValidCode.getTime() < 60000)){
                    //超过1分钟
                    logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code,"验证码请求过于频繁，请稍后再试",phone);

                    throw new BusinessException(BaseErrorConstant.SYSTEM_BUSY, "验证码请求过于频繁，请稍后再试");
                }
            }
            Integer code = sendSmsValidCode(phone, smsConfig.getValidCodeTempId());
            log.info("sms_code,phone:{},code:{}",phone,code);
            RedisSmsValidCode redisSmsValidCode = new RedisSmsValidCode(code,System.currentTimeMillis());
            redisTemplate.opsForValue().set(redisKey,JSONObject.toJSONString(redisSmsValidCode), expireTimeInSeconds,TimeUnit.SECONDS);
            return answer;
        });
    }



    /**
     * 获取短信验证码
     * @param phone
     * @return
     */
    public BaseAnswer<Void> getEditSmsCode(String phone){
        return redisUtil.smartLock(SMS_EDIT_LOCK + phone, () -> {
            //校验手机号
            if (!RegexUtil.regexPhone(phone)) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code,StatusConstant.PHONE_ERROR.getMessage(),phone);

                throw new BusinessException(StatusConstant.PHONE_ERROR);
            }
            BaseAnswer<Void> answer  = new BaseAnswer<>();
            String redisKey = BaseUtils.getEditSMSKey(phone);
            //查看一分钟之内有没有发送短信
            String jsonStr = (String)redisTemplate.opsForValue().get(redisKey);
            if(StringUtils.isNotBlank(jsonStr)){
                RedisSmsValidCode redisSmsValidCode = JSONObject.parseObject(jsonStr, RedisSmsValidCode.class);
                if((System.currentTimeMillis() - redisSmsValidCode.getTime() < 60000)){
                    //超过1分钟
                    logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, "验证码请求过于频繁，请稍后再试",phone);

                    throw new BusinessException(BaseErrorConstant.SYSTEM_BUSY, "验证码请求过于频繁，请稍后再试");
                }
            }
            Integer code = sendSmsValidCode(phone, editCodeTempId);
            log.info("sms_code,phone:{},code:{}",phone,code);
            RedisSmsValidCode redisSmsValidCode = new RedisSmsValidCode(code,System.currentTimeMillis());
            redisTemplate.opsForValue().set(redisKey,JSONObject.toJSONString(redisSmsValidCode), expireTimeInSeconds,TimeUnit.SECONDS);
            return answer;
        });
    }

    /**
     * 验证短信验证码
     * @param phone
     * @param code
     */
    public void validSmsCode(String account,String phone,Integer code){
        String redisKey = BaseUtils.getSMSValidKey(phone);
        String numErrorsKey = BaseUtils.getNumErrorsKey(phone);
        String jsonStr = (String)redisTemplate.opsForValue().get(redisKey);
        Integer numErrors = (Integer)redisTemplate.opsForValue().get(numErrorsKey);
        if(StringUtils.isNotBlank(jsonStr)){
            RedisSmsValidCode redisSmsValidCode = JSONObject.parseObject(jsonStr, RedisSmsValidCode.class);
            long passTime = (System.currentTimeMillis() - redisSmsValidCode.getTime())/1000;
            if (numErrors !=null){
                if (numErrors == 3){
                    redisTemplate.delete(redisKey);
                    redisTemplate.delete(numErrorsKey);
                    logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code,  "输入验证码错误3次，验证码失效请重新获取验证码",account);

                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "输入验证码错误3次，验证码失效请重新获取验证码");
                }
            }
            if(!code.equals(redisSmsValidCode.getCode())){
                //如果输入错误验证码，记录一次错误记录，错了3次就删除验证码至失效，需重新获取
                if (numErrors == null){
                    redisTemplate.opsForValue().set(numErrorsKey,1,expireTimeInSeconds-passTime,TimeUnit.SECONDS);
                }else if(numErrors ==1){
                    redisTemplate.opsForValue().set(numErrorsKey,2,expireTimeInSeconds-passTime,TimeUnit.SECONDS);
                }else if(numErrors ==2){
                    redisTemplate.opsForValue().set(numErrorsKey,3,expireTimeInSeconds-passTime,TimeUnit.SECONDS);
                }
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, "验证码错误",account);

                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "验证码错误");
            }
        }else{
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, "验证码已过期",account);

            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"验证码已过期");
        }
    }

    /**
     * 验证修改短信验证码
     * @param phone
     * @param code
     */
    public void validEditCode(String phone,Integer code){
        String redisKey = BaseUtils.getEditSMSKey(phone);
        String jsonStr = (String)redisTemplate.opsForValue().get(redisKey);
        if(StringUtils.isNotBlank(jsonStr)){
            RedisSmsValidCode redisSmsValidCode = JSONObject.parseObject(jsonStr, RedisSmsValidCode.class);
            if(!code.equals(redisSmsValidCode.getCode())){
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, "验证码错误",phone);
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "验证码错误");
            }
        }else{
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, "验证码已过期",phone);

            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"验证码已过期");
        }
    }
    /**
     * 删除保存在redis的短信验证码相关信息
     * @param phone
     */
    public void deleteSmsCodeInRedis(String phone){
        String redisKey = BaseUtils.getSMSValidKey(phone);
        String numErrorsKey = BaseUtils.getNumErrorsKey(phone);
        redisTemplate.delete(redisKey);
        Integer numErrors = (Integer)redisTemplate.opsForValue().get(numErrorsKey);
        if (numErrors !=null){
            redisTemplate.delete(numErrorsKey);
        }
    }
    /**
     * 删除保存在redis的修改短信验证码相关信息
     * @param phone
     */
    public void deleteEditSmsCodeInRedis(String phone){
        String redisKey = BaseUtils.getEditSMSKey(phone);
        redisTemplate.delete(redisKey);
    }

    /**
     * 删除图形验证码相关信息
     * @param sessionId
     */
    public void deleteValidCodeInRedis(String sessionId){
        String captchaSessionIdKey = BaseUtils.getCaptchaSessionIdKey(sessionId);
        redisTemplate.delete(captchaSessionIdKey);
    }

    /**
     * 验证短信验证码
     * @param phone
     * @param code
     * @param type
     * @return
     */
    public BaseAnswer<String> validSmsCode(String phone,Integer code,String type){
        BaseAnswer<String> answer = new BaseAnswer<>();
        //validSmsCode(phone,code);
        //验证通过后，对于不同的type，生成不同的key，用于进行下一步
        String redisKey = BaseUtils.getSmsCodeValidNextKey(phone, type);
        //生成的key依旧保存获取该次短信验证码的信息
        String key = BaseUtils.uuid();
        redisTemplate.opsForValue().set(redisKey,key, expireTimeInSeconds,TimeUnit.SECONDS);
        answer.setData(key);
        return answer;
    }

    /**
     * 验证获取短信验证码后的下一步
     * @param phone
     * @param type
     * @param key
     */
    public void validSmsCodeValidNextStep(String phone,String type,String key){
        String redisKey = BaseUtils.getSmsCodeValidNextKey(phone, type);
        String redisValue = (String)redisTemplate.opsForValue().get(redisKey);
        if(StringUtils.isBlank(redisValue)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "该操作已无效，请返回上一步重新操作");
        }
        if(!key.equals(redisValue)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "该操作已无效，请返回上一步重新操作");
        }
        //验证通过后先不删除，在整个请求成功后，再删除
        //redisTemplate.delete(redisKey);
    }

    /**
     * 删除保存在redis的验证下一步的相关信息
     * @param phone
     * @param type
     */
    public void deleteSmsCodeValidNextStepInRedis(String phone,String type){
        String redisKey = BaseUtils.getSmsCodeValidNextKey(phone, type);
        redisTemplate.delete(redisKey);
    }

    /**
     *@Description: 发送短信验证码
     *@param phone: 手机号
     *@return: java.lang.Integer
     *@Author: zyj
     *@date: 2021/11/1 9:52
     */
    private Integer sendSmsValidCode(String phone, String tempId){
        Integer code = (int)((Math.random()*9+1)*100000);
        Msg4Request request = new Msg4Request();
        List<String> mobiles = new ArrayList<>();
        mobiles.add(phone);
        Map<String,String> message = new HashMap<>();
        message.put("code",code.toString());

        request.setMobiles(mobiles);
        request.setMessage(message);
        request.setTemplateId(tempId);
        BaseAnswer<Void> messageAnswer = smsFeignClient.asySendMessage(request);
        GeneralUtils.dealResult(messageAnswer);
        return code;
    }


}
