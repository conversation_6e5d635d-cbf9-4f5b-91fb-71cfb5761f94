package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 主从账号关系表
 *
 * <AUTHOR>
public class PrimaryDownRelation implements Serializable {
    /**
     * 主键id
     *
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    private String primaryDownId;

    /**
     * 主账号用户id
     *
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    private String primaryUserId;

    /**
     * 从账号用户id
     *
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    private String downUserId;

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..primary_down_relation.primary_down_id
     *
     * @return the value of supply_chain..primary_down_relation.primary_down_id
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public String getPrimaryDownId() {
        return primaryDownId;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public PrimaryDownRelation withPrimaryDownId(String primaryDownId) {
        this.setPrimaryDownId(primaryDownId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..primary_down_relation.primary_down_id
     *
     * @param primaryDownId the value for supply_chain..primary_down_relation.primary_down_id
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public void setPrimaryDownId(String primaryDownId) {
        this.primaryDownId = primaryDownId;
    }

    /**
     * This method returns the value of the database column supply_chain..primary_down_relation.primary_user_id
     *
     * @return the value of supply_chain..primary_down_relation.primary_user_id
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public String getPrimaryUserId() {
        return primaryUserId;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public PrimaryDownRelation withPrimaryUserId(String primaryUserId) {
        this.setPrimaryUserId(primaryUserId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..primary_down_relation.primary_user_id
     *
     * @param primaryUserId the value for supply_chain..primary_down_relation.primary_user_id
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public void setPrimaryUserId(String primaryUserId) {
        this.primaryUserId = primaryUserId;
    }

    /**
     * This method returns the value of the database column supply_chain..primary_down_relation.down_user_id
     *
     * @return the value of supply_chain..primary_down_relation.down_user_id
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public String getDownUserId() {
        return downUserId;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public PrimaryDownRelation withDownUserId(String downUserId) {
        this.setDownUserId(downUserId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..primary_down_relation.down_user_id
     *
     * @param downUserId the value for supply_chain..primary_down_relation.down_user_id
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public void setDownUserId(String downUserId) {
        this.downUserId = downUserId;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", primaryDownId=").append(primaryDownId);
        sb.append(", primaryUserId=").append(primaryUserId);
        sb.append(", downUserId=").append(downUserId);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        PrimaryDownRelation other = (PrimaryDownRelation) that;
        return (this.getPrimaryDownId() == null ? other.getPrimaryDownId() == null : this.getPrimaryDownId().equals(other.getPrimaryDownId()))
            && (this.getPrimaryUserId() == null ? other.getPrimaryUserId() == null : this.getPrimaryUserId().equals(other.getPrimaryUserId()))
            && (this.getDownUserId() == null ? other.getDownUserId() == null : this.getDownUserId().equals(other.getDownUserId()));
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getPrimaryDownId() == null) ? 0 : getPrimaryDownId().hashCode());
        result = prime * result + ((getPrimaryUserId() == null) ? 0 : getPrimaryUserId().hashCode());
        result = prime * result + ((getDownUserId() == null) ? 0 : getDownUserId().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Fri May 13 14:27:49 CST 2022
     */
    public enum Column {
        primaryDownId("primary_down_id", "primaryDownId", "VARCHAR", false),
        primaryUserId("primary_user_id", "primaryUserId", "VARCHAR", false),
        downUserId("down_user_id", "downUserId", "VARCHAR", false);

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Fri May 13 14:27:49 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}