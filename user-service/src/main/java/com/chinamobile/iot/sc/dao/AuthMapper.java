package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.Auth;
import com.chinamobile.iot.sc.pojo.entity.AuthExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AuthMapper {
    /**
     *
     * @mbg.generated Thu Jun 01 16:20:55 CST 2023
     */
    long countByExample(AuthExample example);

    /**
     *
     * @mbg.generated Thu Jun 01 16:20:55 CST 2023
     */
    int deleteByExample(AuthExample example);

    /**
     *
     * @mbg.generated Thu Jun 01 16:20:55 CST 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Jun 01 16:20:55 CST 2023
     */
    int insert(Auth record);

    /**
     *
     * @mbg.generated Thu Jun 01 16:20:55 CST 2023
     */
    int insertSelective(Auth record);

    /**
     *
     * @mbg.generated Thu Jun 01 16:20:55 CST 2023
     */
    List<Auth> selectByExample(AuthExample example);

    /**
     *
     * @mbg.generated Thu Jun 01 16:20:55 CST 2023
     */
    Auth selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Jun 01 16:20:55 CST 2023
     */
    int updateByExampleSelective(@Param("record") Auth record, @Param("example") AuthExample example);

    /**
     *
     * @mbg.generated Thu Jun 01 16:20:55 CST 2023
     */
    int updateByExample(@Param("record") Auth record, @Param("example") AuthExample example);

    /**
     *
     * @mbg.generated Thu Jun 01 16:20:55 CST 2023
     */
    int updateByPrimaryKeySelective(Auth record);

    /**
     *
     * @mbg.generated Thu Jun 01 16:20:55 CST 2023
     */
    int updateByPrimaryKey(Auth record);

    /**
     *
     * @mbg.generated Thu Jun 01 16:20:55 CST 2023
     */
    int batchInsert(@Param("list") List<Auth> list);

    /**
     *
     * @mbg.generated Thu Jun 01 16:20:55 CST 2023
     */
    int batchInsertSelective(@Param("list") List<Auth> list, @Param("selective") Auth.Column ... selective);
}