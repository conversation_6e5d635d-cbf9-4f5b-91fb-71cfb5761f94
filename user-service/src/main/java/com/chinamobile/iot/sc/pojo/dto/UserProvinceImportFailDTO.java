package com.chinamobile.iot.sc.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON>
 * @date : 2024/11/1 9:50
 * @description:
 **/
@Data
public class UserProvinceImportFailDTO {

    /**
     * 角色名称
     */
    @ExcelProperty(value = "角色名称",index = 0)
    private String roleName;

    /**
     * 账号类型
     */
    @ExcelProperty(value = "账号类型",index = 1)
    private String userTypeName;



    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号",index = 2)
    private String phone;

    /**
     * 联系人姓名
     */
    @ExcelProperty(value = "姓名",index = 3)
    private String name;

    /**
     * 联系人邮箱
     */
    @ExcelProperty(value = "邮箱",index = 4)
    private String email;

    /**
     * 所属单位
     */
    @ExcelProperty(value = "所属单位",index = 5)
    private String company;

    /**
     * 省域
     */
    @ExcelProperty(value = "省域",index = 6)
    private String province;

    @ExcelProperty(value="失败原因",index = 7)
    private String failedReason;
}
