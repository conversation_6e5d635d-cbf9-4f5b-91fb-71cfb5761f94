package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/12 11:41
 * @description 角色列表VO
 */
@Data
public class RoleInfoListItemVO {

    /**
     * 主键
     */
    private String id;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 所属系统 os-IoT应用商城运营支撑系统，screen-商城大屏
     */
    private String system;

    /**
     * 角色类型，admin-超级管理员，common-普通角色
     */
    private String roleType;

    /**
     * 管理中心名称
     */
    private String administrationCenter;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否可编辑
     */
    private Boolean editable;

    /**
     * 该角色用户数量
     */
    private Integer userNum;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
