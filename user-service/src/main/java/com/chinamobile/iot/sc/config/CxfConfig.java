package com.chinamobile.iot.sc.config;

import com.chinamobile.iot.sc.service.WebServiceTo4AService;
import org.apache.cxf.Bus;
import org.apache.cxf.jaxws.EndpointImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import javax.xml.ws.Endpoint;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/7
 * @description webservice的配置类
 */
@Configuration
public class CxfConfig {

    @Resource
    private Bus bus;

    @Resource
    private WebServiceTo4AService webServiceTo4AService;

    /**
     * 发布服务
     *
     * @return
     */
    @Bean
    public Endpoint webServiceTo4AEndpoint() {
        //这里指定的端口不能跟应用的端口冲突, 单独指定
        String path = "/webServiceTo4AService";

        EndpointImpl userEndpoint = new EndpointImpl(bus, webServiceTo4AService);
        userEndpoint.publish(path);
        return userEndpoint;
    }
}
