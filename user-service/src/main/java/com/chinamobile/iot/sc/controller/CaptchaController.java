package com.chinamobile.iot.sc.controller;


import com.chinamobile.iot.sc.annotation.CodeValidMark;
import com.chinamobile.iot.sc.annotation.LogAspectIgnore;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.service.impl.CaptchaServiceImpl;
import com.chinamobile.iot.sc.vo.response.Data4ValidCode;
import com.chinamobile.iot.sc.vo.ImageVerificationVo;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RequestMapping("/base")
@RestController
public class CaptchaController {

    @Resource
    private CaptchaServiceImpl captchaService;

    /**
     * 获取图片验证码
     * @param request
     * @param response
     * @return
     */
    @GetMapping("/captcha/img")
    @LogAspectIgnore(ignoreResult = true)
    public BaseAnswer<Data4ValidCode> getKaptchaImage(HttpServletRequest request, HttpServletResponse response) {
        return captchaService.getKaptchaImage(request,response);
    }

    /**
     *@Description: 校验图片验证码
     *@param validCode: 图片验证码
     *@param sessionId: 回话id
     *@return: BaseAnswer
     *@Author: zyj
     */
    @PostMapping("/captcha/image/code/valid")
    @LogAspectIgnore(ignoreResult = true)
    public BaseAnswer validImageCode(String validCode, String sessionId){
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        String  account = request.getParameter("userName")==null?request.getParameter("phone") :request.getParameter("userName") ;
        captchaService.valid(validCode, sessionId,account);
        return new BaseAnswer();
    }

    /**
     *@Description: 校验图片验证码(用于os登录验证图形验证)
     *@param validCode: 图片验证码
     *@param sessionId: 回话id
     *@return: BaseAnswer
     *@Author: zyj
     */
    @PostMapping("/captcha/image/code/validOs")
    @LogAspectIgnore(ignoreResult = true)
    public BaseAnswer validImageCodeOs(String validCode, String sessionId){
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        String  account = request.getParameter("userName")==null?request.getParameter("phone") :request.getParameter("userName") ;
        captchaService.validOs(validCode, sessionId,account);
        return new BaseAnswer();
    }

    /**
     * 获取滑动验证码
     * @param request
     * @param response
     * @param macro
     * @return
     * @throws IOException
     */
    @GetMapping("/captcha/slide")
    @LogAspectIgnore(ignoreResult = true)
    public BaseAnswer<ImageVerificationVo> getSlideImage(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "macro",required = false,defaultValue = "0")int macro) throws IOException {
        return captchaService.getSlideImage(request,response,macro);
    }

    /**
     * 获取短信验证码(OS系统)
     * @param phone
     * @return
     */
    @GetMapping("/captcha/sms")
    public BaseAnswer<Void> getSmsValidCode(String phone,HttpServletRequest request){
        String  account = request.getParameter("userName")==null?request.getParameter("phone") :request.getParameter("userName") ;
        return captchaService.getSmsValidCode(phone,request,account);
    }

    /**
     * 获取短信验证码(大屏系统)
     * @param phone
     * @return
     */
    @GetMapping("/captcha/smsScreen")
    public BaseAnswer<Void> getSmsValidCodeScreen(String phone, HttpServletRequest request){
        String  account = request.getParameter("userName")==null?request.getParameter("phone") :request.getParameter("userName") ;
        return captchaService.getSmsValidCodeScreen(phone, request, account);
    }



    /**
     * 获取短信验证码(装维\H5系统)
     * @param phone
     * @return
     */
    @GetMapping("/captcha/smsInstall")
    public BaseAnswer<Void> getSmsValidCodeInstall(String phone){
        return captchaService.getSmsValidCodeInstall(phone);
    }

    /**
     * 获取修改短信验证码
     * @param phone
     * @return
     */
    @GetMapping("/captcha/editSms")
    public BaseAnswer<Void> getEditSmsCode(String phone){
        return captchaService.getEditSmsCode(phone);
    }

}
