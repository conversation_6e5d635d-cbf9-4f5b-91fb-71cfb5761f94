package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.constant.StatusConstant;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.AuthRO;
import com.chinamobile.iot.sc.mode.OperatePermissionRO;
import com.chinamobile.iot.sc.mode.RoleOperatePermissionRO;
import com.chinamobile.iot.sc.pojo.dto.DataPermissionDTO;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.service.DataPermissionService;
import com.chinamobile.iot.sc.service.OnlineConfigService;

import com.chinamobile.iot.sc.service.RoleInfoService;
import com.chinamobile.iot.sc.service.RoleOperatePermissionService;
import com.chinamobile.iot.sc.utils.IOTEncodeUtils;
import com.chinamobile.iot.sc.vo.request.*;
import com.chinamobile.iot.sc.vo.response.RoleInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;


import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * 上线配置
 */
@Service
@Slf4j
public class OnlineConfigServiceImpl implements OnlineConfigService {

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private OperatePermissionMapper operatePermissionMapper;

    @Resource
    private DataPermissionMapper dataPermissionMapper;

    @Resource
    private RoleDataPermissionMapper roleDataPermissionMapper;

    @Resource
    private RoleOperatePermissionMapper roleOperatePermissionMapper;

    @Resource
    private AuthMapper authMapper;

    @Resource
    private OnlineConfigService onlineConfigService;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private RoleInfoMapper roleInfoMapper;

    @Resource
    private RoleAuthMapper roleAuthMapper;

    @Resource
    private RoleOperatePermissionService roleOperatePermissionService;

    @Resource
    private DataPermissionService dataPermissionService;

    @Resource
    private UserPartnerMapper userPartnerMapper;

    @Resource
    private UserInstallMapper userInstallMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private RoleInfoService roleInfoService;

    @Resource
    private RoleManageMapper roleManageMapper;

    private AtomicInteger atomicInteger = new AtomicInteger(0);

    @Value("${supply.des.key}")
    private String encryptKey;

    @Resource
    private UserScreenMapper userScreenMapper;

    /**
     * 装载权限配置
     */
    @Override
    public BaseAnswer loadPermissionConfig() {
        //加载auth
        loadAuth();

        //加载操作权限
        loadOperatePermissions();

        //加载数据权限
        //loadDataPermissions();

        //加载角色操作权限
        loadRoleOperatePermissions();

//        dataPermissionService.initDataPermissions();

        //加载角色数据权限
        loadRoleDataPermissions();

//        roleInfoService.migrateOldRoles();

        return new BaseAnswer<>();
    }

    public void deleteKeysByPrefix(String prefix) {
        Set<String> keys = redisTemplate.keys(prefix + "*");
        List<String> batchDeleteKeys = new ArrayList<>(keys);
        if (!batchDeleteKeys.isEmpty()) {
            redisTemplate.delete(batchDeleteKeys);
        }
    }
    private void loadOperatePermissions() {
        deleteKeysByPrefix(Constant.REDIS_KEY_OPERATE_PERMISSION);
        List<OperatePermissionRO> operatePermissions = operatePermissionMapper.selectByExample(new OperatePermissionExample())
                .stream().map(item -> {
                    OperatePermissionRO ro = new OperatePermissionRO();
                    BeanUtils.copyProperties(item, ro);
                    return ro;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(operatePermissions)) {
            Map<String, List<OperatePermissionRO>> operatePermissionListMap = new LinkedHashMap<>();
            operatePermissions.stream().collect(Collectors.groupingBy(item -> item.getUri() + item.getMethod().toLowerCase()))
                    .forEach((key, value) -> operatePermissionListMap.put(Constant.REDIS_KEY_OPERATE_PERMISSION + key, value));

            redisTemplate.opsForValue().multiSet(operatePermissionListMap);
        }
    }

    private void loadDataPermissions() {
//        DataPermissionExample dataPermissionExample = new DataPermissionExample();
//        List<DataPermission> dataPermissions = dataPermissionMapper.selectByExample(dataPermissionExample);
//        if (CollectionUtils.isNotEmpty(dataPermissions)) {
//            Map<String, List<DataPermission>> dataPermissionMap = new LinkedHashMap<>();
//            dataPermissions.stream().collect(Collectors.groupingBy(DataPermission::getTableName))
//                    .forEach((key, value) -> dataPermissionMap.put(Constant.REDIS_KEY_DATA_PERMISSION + key, value));
//
//            redisTemplate.opsForValue().multiSet(dataPermissionMap);
//        }
    }

    private void loadRoleOperatePermissions() {
        deleteKeysByPrefix(Constant.REDIS_KEY_ROLE_OPERATE_PERMISSION);
        RoleOperatePermissionExample roleOperatePermissionExample = new RoleOperatePermissionExample();
        List<RoleOperatePermissionRO> roleOperatePermissions = roleOperatePermissionMapper.selectByExample(roleOperatePermissionExample)
                .stream().map(item -> {
                    RoleOperatePermissionRO ro = new RoleOperatePermissionRO();
                    BeanUtils.copyProperties(item, ro);
                    return ro;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(roleOperatePermissions)) {
            Map<String, List<RoleOperatePermissionRO>> roleOperatePermissionMap = new LinkedHashMap<>();
            roleOperatePermissions.stream().collect(Collectors.groupingBy(RoleOperatePermissionRO::getRoleId))
                    .forEach((key, value) -> roleOperatePermissionMap.put(Constant.REDIS_KEY_ROLE_OPERATE_PERMISSION + key, value));

            redisTemplate.opsForValue().multiSet(roleOperatePermissionMap);
        }
    }

    @Override
    public void loadRoleDataPermissions() {
        deleteKeysByPrefix(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE);
        List<RoleInfoVO> roleInfoVOS = roleInfoService.listAllRoles();
        if (CollectionUtils.isNotEmpty(roleInfoVOS)) {
            Map<String, List<String>> roleDataPermissionMap = new HashMap<>();
            for (RoleInfoVO roleInfoVO : roleInfoVOS) {
                String roleId = roleInfoVO.getId();
                List<DataPermissionDTO> dataPermissionDTOS = dataPermissionService.listByRoleId(roleId);
                List<String> codes = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(dataPermissionDTOS)) {
                    codes = dataPermissionDTOS.stream().map(DataPermissionDTO::getAuthCode).collect(Collectors.toList());
                }
                roleDataPermissionMap.put(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + roleId, codes);
            }
            redisTemplate.opsForValue().multiSet(roleDataPermissionMap);
        }
    }

    private void loadAuth() {
        deleteKeysByPrefix(Constant.REDIS_KEY_AUTH_ID);
        deleteKeysByPrefix(Constant.REDIS_KEY_AUTH_CODE);
        List<AuthRO> auths = authMapper.selectByExample(new AuthExample()).stream().map(
                item -> {
                    AuthRO ro = new AuthRO();
                    BeanUtils.copyProperties(item, ro);
                    return ro;
                }
        ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(auths)) {
            Map<String, AuthRO> authIdMap = new LinkedHashMap<>();
            Map<String, AuthRO> authCodeMap = new LinkedHashMap<>();
            auths.forEach(auth -> {
                authIdMap.put(Constant.REDIS_KEY_AUTH_ID + auth.getId(), auth);
                authCodeMap.put(Constant.REDIS_KEY_AUTH_CODE + auth.getAuthCode(), auth);
            });

            redisTemplate.opsForValue().multiSet(authIdMap);
            redisTemplate.opsForValue().multiSet(authCodeMap);
        }
    }

    @Override
    public BaseAnswer addOperatePermission(OperatePermissionParam param) {
        if (StringUtils.isNotEmpty(param.getAuthCode())
                && !StringUtils.equals(param.getAuthCode(), BaseConstant.PERMISSION_SUPER_ADMIN_ONLY)) {
            AuthExample authExample = new AuthExample();
            AuthExample.Criteria authCriteria = authExample.createCriteria();
            authCriteria.andAuthCodeEqualTo(param.getAuthCode());
            List<Auth> auths = authMapper.selectByExample(authExample);
            if (CollectionUtils.isEmpty(auths)) {
                throw new BusinessException(StatusConstant.AUTH_NOT_EXIST, "authCode（" + param.getAuthCode() + "）不存在");
            }
        }

        OperatePermissionExample example = new OperatePermissionExample();
        OperatePermissionExample.Criteria criteria = example.createCriteria();
        criteria.andUriEqualTo(param.getUri()).andAuthCodeEqualTo(param.getAuthCode()).andMethodEqualTo(param.getMethod());
        List<OperatePermission> operatePermissions = operatePermissionMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(operatePermissions)) {
//            throw new BusinessException(StatusConstant.OPERATE_PERMISSION_CODE_EXITED,
//                    "操作权限接口路径（" + param.getUri() + "）已经配置了功能" + param.getAuthCode());
            //已配置直接跳过
            return BaseAnswer.success("成功");
        }

        OperatePermission operatePermission = new OperatePermission();
        BeanUtils.copyProperties(param, operatePermission);
        operatePermission.setId(BaseServiceUtils.getId());
        Date now = new Date();
        operatePermission.setCreateTime(now);
        operatePermission.setUpdateTime(now);
        operatePermissionMapper.insertSelective(operatePermission);

        return BaseAnswer.success("成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer batchInsertOperatePermission(List<OperatePermissionParam> params) {
        if (CollectionUtils.isNotEmpty(params)) {
            params.forEach(param -> onlineConfigService.addOperatePermission(param));
        }
        return BaseAnswer.success("成功");
    }

    @Override
    public BaseAnswer batchInsertAuth(List<Request4AddAuth> params) {
        if (CollectionUtils.isEmpty(params)) {
            return BaseAnswer.success("成功");
        }
        List<String> parentCodes = params.stream().map(Request4AddAuth::getParentCode).distinct().collect(Collectors.toList());
        parentCodes.removeIf(StringUtils::isEmpty);
        AuthExample authExample = new AuthExample();
        AuthExample.Criteria authCriteria = authExample.createCriteria();
        Map<String, Auth> parentMap = new LinkedHashMap<>();
        if (CollectionUtils.isNotEmpty(parentCodes)) {
            authCriteria.andAuthCodeIn(parentCodes);
            List<Auth> parentAuths = authMapper.selectByExample(authExample);

            if (CollectionUtils.isEmpty(parentAuths)) {
                throw new BusinessException(StatusConstant.AUTH_NOT_EXIST, "父编码" + parentCodes + "不存在");
            }

            if (parentCodes.size() != parentAuths.size()) {
                throw new BusinessException(StatusConstant.AUTH_NOT_EXIST, "父编码"
                        + CollectionUtils.subtract(parentCodes, parentAuths.stream().map(Auth::getAuthCode).collect(Collectors.toList())) + "不存在");
            }
            parentAuths.forEach(auth -> parentMap.put(auth.getAuthCode(), auth));
        }

        authExample = new AuthExample();
        authCriteria = authExample.createCriteria();
        List<String> authCodes = params.stream().map(Request4AddAuth::getAuthCode).collect(Collectors.toList());
        authCriteria.andAuthCodeIn(authCodes);
        List<Auth> auths = authMapper.selectByExample(authExample);
        if (CollectionUtils.isNotEmpty(auths)) {
//            throw new BusinessException(StatusConstant.AUTH_NOT_EXIST, "code"
//                    + auths.stream().map(Auth::getAuthCode).collect(Collectors.toList()) + "已存在");
            //过滤掉已经加入
            List<String> existCodes = auths.stream().map(Auth::getAuthCode).collect(Collectors.toList());
            params = params.stream().filter(item -> !existCodes.contains(item.getAuthCode())).collect(Collectors.toList());
        }
        Integer maxId = authMapper.selectByExample(new AuthExample().createCriteria().andParentIdEqualTo("-1").example())
                .stream().map(auth -> Integer.valueOf(auth.getId())).max(Comparator.comparing(x -> x)).get();
        atomicInteger.set(maxId);
        List<Auth> authList = params.stream().map(param -> {
            Auth auth = new Auth();
            BeanUtils.copyProperties(param, auth);
            if (StringUtils.isNotBlank(param.getParentCode())) {
                auth.setId(BaseServiceUtils.getId());
                auth.setParentId(parentMap.get(param.getParentCode()).getId());
            } else {
                //针对顶级auth的创建，为了保证顺序，在将已有的auth最大值取出来+1
                auth.setId(Integer.toString(atomicInteger.addAndGet(1)));
                auth.setParentId("-1");
            }
            return auth;
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(authList)) {
            authMapper.batchInsert(authList);
        }
        return BaseAnswer.success("成功");
    }

    @Override
    public BaseAnswer insertAuth(Request4AddAuth param) {

        AuthExample authExample = new AuthExample();
        AuthExample.Criteria authCriteria = authExample.createCriteria();
        Map<String, Auth> parentMap = new LinkedHashMap<>();
        Auth parentAuth = null;
        if (StringUtils.isNotBlank(param.getParentCode())) {
            authCriteria.andAuthCodeEqualTo(param.getParentCode());
            List<Auth> parentAuths = authMapper.selectByExample(authExample);

            if (CollectionUtils.isEmpty(parentAuths)) {
                throw new BusinessException(StatusConstant.AUTH_NOT_EXIST, "父编码" + param.getParentCode() + "不存在");
            }

            parentAuth = parentAuths.get(0);
        }

        authExample = new AuthExample();
        authCriteria = authExample.createCriteria();

        authCriteria.andAuthCodeEqualTo(param.getAuthCode());
        List<Auth> auths = authMapper.selectByExample(authExample);
        if (CollectionUtils.isEmpty(auths)) {
            Auth auth = new Auth();
            BeanUtils.copyProperties(param, auth);
            if (StringUtils.isNotBlank(param.getParentCode())) {
                auth.setId(BaseServiceUtils.getId());
                auth.setParentId(parentAuth.getId());
            } else {
                //针对顶级auth的创建，为了保证顺序，在将已有的auth最大值取出来+1
                Integer maxId = authMapper.selectByExample(new AuthExample().createCriteria().andParentIdEqualTo("-1").example())
                        .stream().map(item -> Integer.valueOf(item.getId())).max(Comparator.comparing(x -> x)).get();
                auth.setId(Integer.toString(++maxId));
                auth.setParentId("-1");
            }
            authMapper.insert(auth);
        }

        return BaseAnswer.success("成功");
    }

    @Override
    public BaseAnswer initDataPermissions() {
        DataPermission permission = dataPermissionMapper.selectByPrimaryKey("101");
        if (ObjectUtils.isEmpty(permission)) {
            dataPermissionService.initDataPermissions();
            ;
        }
        return BaseAnswer.success("成功");
    }

    @Override
    public BaseAnswer deleteUnusedAuth() {
        List<String> ids = Arrays.asList("1000", "1001", "1002",
                "1003", "1004", "1005", "1006", "1010", "1011", "1012", "1100", "1101",
                "131", "132", "133", "134", "135", "136", "137", "138",
                "1102", "1110", "1111", "1112", "1200", "1201", "1210", "1211", "1212",
                "1300", "1500", "1501", "1502", "160", "161", "163", "164");
        authMapper.deleteByExample(new AuthExample().createCriteria().andIdIn(ids).example());
        roleAuthMapper.deleteByExample(new RoleAuthExample().createCriteria().andAuthIdIn(ids).example());
        return BaseAnswer.success("成功");
    }

    @Override
    public BaseAnswer batchInsertRoleAuth(List<RoleAuthBatchParam> params, Boolean onlyAdd) {
        if (CollectionUtils.isEmpty(params)) {
            return BaseAnswer.success("成功");
        }
        List<RoleAuth> roleAuths = new ArrayList<>();
        List<RoleOperatePermission> roleOperatePermissions = new ArrayList<>();
        List<String> roleIds = new ArrayList<>();
        List<RoleManage> roleManages = new ArrayList<>();
        Date now = new Date();
        params.forEach(roleAuthBatchParam -> {
            roleIds.add(roleAuthBatchParam.getRoleId());
            roleAuths.addAll(authMapper.selectByExample(new AuthExample().createCriteria().andAuthCodeIn(roleAuthBatchParam.getAuthCodes()).example())
                    .stream().map(auth -> {
                        RoleAuth roleAuth = new RoleAuth();
                        roleAuth.setRoleId(roleAuthBatchParam.getRoleId());
                        roleAuth.setAuthId(auth.getId());
                        return roleAuth;
                    }).filter(roleAuth -> {
                        List<RoleAuth> roleAuthList = roleAuthMapper.selectByExample(new RoleAuthExample().createCriteria()
                                .andRoleIdEqualTo(roleAuth.getRoleId()).andAuthIdEqualTo(roleAuth.getAuthId())
                                .example());
                        return CollectionUtils.isEmpty(roleAuthList);
                    }).collect(Collectors.toList()));
            roleOperatePermissions.addAll(operatePermissionMapper.selectByExample(new OperatePermissionExample().createCriteria()
                            .andAuthCodeIn(roleAuthBatchParam.getAuthCodes()).example())
                    .stream().map(operatePermission -> {
                        RoleOperatePermission roleOperatePermission = new RoleOperatePermission();
                        roleOperatePermission.setId(BaseServiceUtils.getId());
                        roleOperatePermission.setRoleId(roleAuthBatchParam.getRoleId());
                        roleOperatePermission.setOperatePermissionId(operatePermission.getId());
                        roleOperatePermission.setCreateTime(now);
                        roleOperatePermission.setUpdateTime(now);
                        return roleOperatePermission;
                    }).filter(item -> {
                        List<RoleOperatePermission> roleManageList = roleOperatePermissionMapper.selectByExample(
                                new RoleOperatePermissionExample().createCriteria()
                                        .andRoleIdEqualTo(item.getRoleId()).andOperatePermissionIdEqualTo(item.getOperatePermissionId())
                                        .example());
                        return CollectionUtils.isEmpty(roleManageList);
                    })
                    .collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(roleAuthBatchParam.getManageRoleTypes())) {
                roleManages.addAll(roleInfoMapper.selectByExample(new RoleInfoExample().createCriteria()
                        .andRoleTypeIn(roleAuthBatchParam.getManageRoleTypes()).example()).stream().map(item -> {
                    RoleManage roleManage = new RoleManage();
                    roleManage.setId(BaseServiceUtils.getId());
                    roleManage.setRoleId(roleAuthBatchParam.getRoleId());
                    roleManage.setCanCreateUserRoleId(item.getId());
                    roleManage.setCreateTime(now);
                    roleManage.setUpdateTime(now);
                    return roleManage;
                }).filter(item -> {
                    List<RoleManage> roleManageList = roleManageMapper.selectByExample(new RoleManageExample().createCriteria()
                            .andRoleIdEqualTo(item.getRoleId()).andCanCreateUserRoleIdEqualTo(item.getCanCreateUserRoleId())
                            .example());
                    return CollectionUtils.isEmpty(roleManageList);
                }).collect(Collectors.toList()));
            }
        });

        if (!onlyAdd) {
            roleAuthMapper.deleteByExample(new RoleAuthExample().createCriteria().andRoleIdIn(roleIds).example());
            roleOperatePermissionMapper.deleteByExample(new RoleOperatePermissionExample().createCriteria().andRoleIdIn(roleIds).example());
        }
//        roleManageMapper.deleteByExample(new RoleManageExample().createCriteria().andRoleIdIn(roleIds).example());
        if (CollectionUtils.isNotEmpty(roleAuths)) {
            roleAuthMapper.batchInsert(roleAuths);
        }
        if (CollectionUtils.isNotEmpty(roleOperatePermissions)) {
            roleOperatePermissionMapper.batchInsert(roleOperatePermissions);
        }
        if (CollectionUtils.isNotEmpty(roleManages)) {
            roleManageMapper.batchInsert(roleManages);
        }
        return BaseAnswer.success("成功");
    }

    @Override
    public BaseAnswer migratePartnerAndInstaller() {
        //查询用户信息里面的所有合作伙伴
        List<User> partners = userMapper.selectByExample(new UserExample().createCriteria()
                .andIsPrimaryIsNotNull().example());
        List<UserPartner> existPartner = userPartnerMapper.selectByExample(new UserPartnerExample());
        List<String> existPartnerUserIds = existPartner.stream().map(UserPartner::getUserId).collect(Collectors.toList());
        partners = partners.stream().filter(item -> !existPartnerUserIds.contains(item.getUserId()))
                .collect(Collectors.toList());
        List<UserPartner> userPartners = partners.stream().map(item -> {
            UserPartner partner = new UserPartner();
            BeanUtils.copyProperties(item, partner);
            return partner;
        }).collect(Collectors.toList());
        List<UserInstall> userInstalls = userInstallMapper.selectByExample(new UserInstallExample());
        userInstalls = userInstalls.stream().filter(item -> !existPartnerUserIds.contains(item.getId()))
                .collect(Collectors.toList());
        RoleInfo installRole = roleInfoMapper.selectByExample(new RoleInfoExample().createCriteria()
                .andRoleTypeEqualTo("partnerInstall").example()).get(0);
        List<UserPartner> install2Partners = userInstalls.stream().map(item -> {
            UserPartner partner = new UserPartner();
            partner.withUserId(item.getId())
                    .withEmail(item.getInstallEmail())
                    .withPhone(item.getInstallPhone())
                    .withRoleId(installRole.getId())
                    .withName(item.getInstallName())
                    .withIsSend(true)
                    .withPartnerName(item.getInstallPartnerName())
                    .withCreateTime(item.getCreateTime())
                    .withUpdateTime(item.getUpdateTime())
                    .withIsCancel(item.getIsCancel());
            User user = userMapper.selectByPrimaryKey(item.getCreateUserId());
            UserPartner up = userPartnerMapper.selectByPrimaryKey(item.getCreateUserId());
            if (user != null) {
                partner.setCreator(user.getName());
            } else if (up != null) {
                partner.setCreator(up.getName());
            }

            return partner;
        }).collect(Collectors.toList());
        userPartners.addAll(install2Partners);
        //删掉user库中的主从伙伴
        userMapper.deleteByExample(new UserExample().createCriteria().andIsPrimaryIsNotNull().example());
        if (CollectionUtils.isNotEmpty(userPartners)) {
            userPartnerMapper.batchInsert(userPartners);
        }
        return BaseAnswer.success("成功");
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer permissionOnline(Request4OnlineData onlineData) {
        //插入已有parent的auth
        onlineConfigService.batchInsertAuth(onlineData.getParentExistAuth());

        //插入前面新增的auth的子auth
        onlineConfigService.batchInsertAuth(onlineData.getNewParentAuth());

        //删除以后不使用的auth
        onlineConfigService.deleteUnusedAuth();


        //插入跟auth相关的操作权限
        onlineConfigService.batchInsertOperatePermission(onlineData.getOperatePermissionParams());

        //迁移角色
        onlineConfigService.initDataPermissions();

        //迁移绑定数据权限
        roleInfoService.migrateOldRoles();

        //迁移角色绑定的权限
        onlineConfigService.batchInsertRoleAuth(onlineData.getRoleAuth(), false);

        //迁移合作伙伴以及售后安装师傅
        onlineConfigService.migratePartnerAndInstaller();

        //将权限都加载到redis里面
        onlineConfigService.loadPermissionConfig();


        return BaseAnswer.success("成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer newPermissionOnline(Request4OnlineData onlineData) {
        if (CollectionUtils.isNotEmpty(onlineData.getAuths())) {
            onlineData.getAuths().forEach(auth -> onlineConfigService.insertAuth(auth));
        }
//        //插入已有parent的auth
//        onlineConfigService.batchInsertAuth(onlineData.getParentExistAuth());
//
//        //插入前面新增的auth的子auth
//        onlineConfigService.batchInsertAuth(onlineData.getNewParentAuth());

        //插入跟auth相关的操作权限
        onlineConfigService.batchInsertOperatePermission(onlineData.getOperatePermissionParams());

        //迁移角色绑定的权限
        onlineConfigService.batchInsertRoleAuth(onlineData.getRoleAuth(), true);

        //更新操作权限之后，需要考虑使用以前的authCode给与新接口的情况，对于这种权限，需要添加进已经持有相关authCode的角色
        if (CollectionUtils.isNotEmpty(onlineData.getOperatePermissionParams())) {
            checkRoelAuthPermission(onlineData.getOperatePermissionParams().stream().map(OperatePermissionParam::getAuthCode)
                    .distinct().collect(Collectors.toList()));
        }
        //将权限都加载到redis里面
        onlineConfigService.loadPermissionConfig();


        return BaseAnswer.success("成功");
    }

    private void checkRoelAuthPermission(List<String> authCodes) {
        if (CollectionUtils.isEmpty(authCodes)) {
            return;
        }
        Date now = new Date();
        final List<RoleOperatePermission> newRoleOperatePermission = new ArrayList<>();
        authCodes.forEach(code -> {
            List<OperatePermission> operatePermissions = operatePermissionMapper.selectByExample(new OperatePermissionExample().createCriteria().andAuthCodeEqualTo(code).example());
            if (CollectionUtils.isNotEmpty(operatePermissions)) {
                List<RoleOperatePermission> roleOperatePermissions = roleOperatePermissionMapper.selectByExample(new RoleOperatePermissionExample()
                        .createCriteria().andOperatePermissionIdIn(operatePermissions.stream().map(OperatePermission::getId)
                                .collect(Collectors.toList())).example());
                roleOperatePermissions.stream().map(RoleOperatePermission::getRoleId).distinct().forEach(roleId -> {
                    List<RoleOperatePermission> theRoleOperatePermissions = roleOperatePermissionMapper.selectByExample(new RoleOperatePermissionExample()
                            .createCriteria().andRoleIdEqualTo(roleId).example());
                    newRoleOperatePermission.addAll(CollectionUtils.subtract(operatePermissions.stream().map(OperatePermission::getId).collect(Collectors.toList()),
                            theRoleOperatePermissions.stream().map(RoleOperatePermission::getOperatePermissionId)
                                    .collect(Collectors.toList())).stream().map(permissionId -> {
                        RoleOperatePermission roleOperatePermission = new RoleOperatePermission();
                        roleOperatePermission.setId(BaseServiceUtils.getId());
                        roleOperatePermission.setRoleId(roleId);
                        roleOperatePermission.setOperatePermissionId(permissionId);
                        roleOperatePermission.setCreateTime(now);
                        roleOperatePermission.setUpdateTime(now);
                        return roleOperatePermission;
                    }).collect(Collectors.toList()));
                });
            }
        });

        if(CollectionUtils.isNotEmpty(newRoleOperatePermission)) {
            roleOperatePermissionMapper.batchInsert(newRoleOperatePermission);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseAnswer encryptPhone(Request4EncryptPhone param) {
        if (param.getEncryptAll() != null && param.getEncryptAll()) {
            //加密数据库所有手机号

            //加密user表中所有手机号
            List<User> users = userMapper.selectByExample(new UserExample());
            users.forEach(user -> {
                //小于11位，手机号需要加密
                if (StringUtils.length(user.getPhone()) <= 11) {
                    user.setPhone(IOTEncodeUtils.encryptIOTMessage(user.getPhone(), encryptKey));
                }
                if (CollectionUtils.isNotEmpty(param.getAccounts())) {
                    String phone = IOTEncodeUtils.decryptIOTMessage(user.getPhone(), encryptKey);
                    Optional<ExistUserAddAccount> existUserAddAccount = param.getAccounts().stream()
                            .filter(item -> StringUtils.equals(item.getPhone(), phone)).findAny();
                    existUserAddAccount.ifPresent(userAddAccount -> {
                        user.setAccount(userAddAccount.getAccount());
                    });
                }
                userMapper.updateByPrimaryKeySelective(user);
            });

            //加密user_partner表中所有手机号
            List<UserPartner> userPartners = userPartnerMapper.selectByExample(new UserPartnerExample());
            userPartners.forEach(user -> {
                //小于11位，手机号需要加密
                if (StringUtils.length(user.getPhone()) <= 11) {
                    user.setPhone(IOTEncodeUtils.encryptIOTMessage(user.getPhone(), encryptKey));
                }
                userPartnerMapper.updateByPrimaryKeySelective(user);
            });

            //加密user_screen表中所有手机号
            List<UserScreen> userScreens = userScreenMapper.selectByExample(new UserScreenExample());
            userScreens.forEach(user -> {
                //小于11位，手机号需要加密
                if (StringUtils.length(user.getPhone()) <= 11) {
                    user.setPhone(IOTEncodeUtils.encryptIOTMessage(user.getPhone(), encryptKey));
                }
                userScreenMapper.updateByPrimaryKeySelective(user);
            });

        } else if (CollectionUtils.isNotEmpty(param.getAccounts())) {
            //只需要处理user表中account字段
            param.getAccounts().forEach(item -> item.setPhone(IOTEncodeUtils.encryptIOTMessage(item.getPhone(), encryptKey)));
            List<User> users = userMapper.selectByExample(new UserExample().createCriteria().andPhoneIn(
                    param.getAccounts().stream().map(ExistUserAddAccount::getPhone).collect(Collectors.toList())).example());
            users.forEach(user -> {
                Optional<ExistUserAddAccount> existUserAddAccount = param.getAccounts().stream()
                        .filter(item -> StringUtils.equals(item.getPhone(), user.getPhone())).findAny();
                existUserAddAccount.ifPresent(userAddAccount -> {
                    user.setAccount(userAddAccount.getAccount());
                    userMapper.updateByPrimaryKeySelective(user);
                });
            });
        }
        return BaseAnswer.success("成功");
    }
}
