package com.chinamobile.iot.sc.vo.response;

import lombok.Data;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/9/13 14:20
 * @description: 产品运营管理新增返回VO
 **/
@Data
public class UserPipeAddVO {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 名称
     */
    private String name;

    /**
     * 用户账号
     */
    private String account;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 所属单位
     */
    private String company;

    /**
     * 部门
     */
    private String departmentName;

    /**
     * 久工号
     */
    private String oldJobNumber;

    /**
     * 新工号
     */
    private String newJobNumber;


    /**
     * 角色类型
     */
    private String roleType;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 是否超管
     */
    private Boolean isAdmin;
}
