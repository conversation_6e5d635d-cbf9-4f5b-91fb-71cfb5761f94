package com.chinamobile.iot.sc.constant;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON>
 * @date : 2025/6/18 14:15
 * @description:
 **/
public class UserImportShelfExcelHeaderConstant {


    public static List<String> importUserAdminHeader = null;

    public static List<String> importUserPartnerLordHeader = null;

    public static List<String> importUserPartnerHeader = null;

    public static List<String> importUserPartnerInstallHeader = null;


    static {

        String importUserAdminHeaderStr = "系统,角色名称,账号类型,用户账号,省域";
        String importUserPartnerLordHeaderStr = "系统,合作伙伴名称,角色名称,账号类型,姓名,手机号,邮箱,是否为外部人员,省域/地市域,社会统一信用代码";
        String importUserPartnerHeaderStr = "角色名称,账号类型,姓名,手机号,邮箱,是否为外部人员,省域/地市域";
        String importUserPartnerInstallHeaderStr = "角色名称,账号类型,联系人姓名,联系人手机号,联系人邮箱,是否为外部人员,省域/地市域";
        importUserAdminHeader = Arrays.asList(importUserAdminHeaderStr.split(","));
        importUserPartnerLordHeader = Arrays.asList(importUserPartnerLordHeaderStr.split(","));
        importUserPartnerHeader = Arrays.asList(importUserPartnerHeaderStr.split(","));
        importUserPartnerInstallHeader = Arrays.asList(importUserPartnerInstallHeaderStr.split(","));
    }
}
