package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.ContractProvinceInfo;
import com.chinamobile.iot.sc.pojo.entity.ContractProvinceInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ContractProvinceInfoMapper {
    /**
     *
     * @mbg.generated Mon Sep 25 10:37:54 CST 2023
     */
    long countByExample(ContractProvinceInfoExample example);

    /**
     *
     * @mbg.generated Mon Sep 25 10:37:54 CST 2023
     */
    int deleteByExample(ContractProvinceInfoExample example);

    /**
     *
     * @mbg.generated Mon Sep 25 10:37:54 CST 2023
     */
    int deleteByPrimaryKey(String mallCode);

    /**
     *
     * @mbg.generated Mon Sep 25 10:37:54 CST 2023
     */
    int insert(ContractProvinceInfo record);

    /**
     *
     * @mbg.generated Mon Sep 25 10:37:54 CST 2023
     */
    int insertSelective(ContractProvinceInfo record);

    /**
     *
     * @mbg.generated Mon Sep 25 10:37:54 CST 2023
     */
    List<ContractProvinceInfo> selectByExample(ContractProvinceInfoExample example);

    /**
     *
     * @mbg.generated Mon Sep 25 10:37:54 CST 2023
     */
    ContractProvinceInfo selectByPrimaryKey(String mallCode);

    /**
     *
     * @mbg.generated Mon Sep 25 10:37:54 CST 2023
     */
    int updateByExampleSelective(@Param("record") ContractProvinceInfo record, @Param("example") ContractProvinceInfoExample example);

    /**
     *
     * @mbg.generated Mon Sep 25 10:37:54 CST 2023
     */
    int updateByExample(@Param("record") ContractProvinceInfo record, @Param("example") ContractProvinceInfoExample example);

    /**
     *
     * @mbg.generated Mon Sep 25 10:37:54 CST 2023
     */
    int updateByPrimaryKeySelective(ContractProvinceInfo record);

    /**
     *
     * @mbg.generated Mon Sep 25 10:37:54 CST 2023
     */
    int updateByPrimaryKey(ContractProvinceInfo record);

    /**
     *
     * @mbg.generated Mon Sep 25 10:37:54 CST 2023
     */
    int batchInsert(@Param("list") List<ContractProvinceInfo> list);

    /**
     *
     * @mbg.generated Mon Sep 25 10:37:54 CST 2023
     */
    int batchInsertSelective(@Param("list") List<ContractProvinceInfo> list, @Param("selective") ContractProvinceInfo.Column ... selective);
}