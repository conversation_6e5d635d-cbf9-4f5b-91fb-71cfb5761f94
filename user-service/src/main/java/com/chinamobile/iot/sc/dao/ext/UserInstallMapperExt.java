package com.chinamobile.iot.sc.dao.ext;

import com.chinamobile.iot.sc.pojo.entity.UserInstall;
import com.chinamobile.iot.sc.vo.response.DataUserInstallVO;
import com.chinamobile.iot.sc.vo.response.UserInstallSendOrdersVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR> x<PERSON>maoh<PERSON>
 * @date : 2022/12/14 15:42
 * @description: 装维用户mapper拓展类
 **/
@Mapper
public interface UserInstallMapperExt {

    /**
     * 分页查询装维用户信息
     * @param primaryUserId
     * @param installName
     * @param installPhone
     * @param installEmail
     * @param installPartnerName
     * @param pageIndex
     * @param num
     * @return
     */
    List<DataUserInstallVO> pageUserInstall(String primaryUserId,String installName, String installPhone, String installEmail, String installPartnerName, Integer pageIndex, Integer num);

    /**
     * 统计分页查询装维用户数量
     * @param primaryUserId
     * @param installName
     * @param installPhone
     * @param installEmail
     * @param installPartnerName
     * @return
     */
    Long pageCountUserInstall(String primaryUserId,String installName, String installPhone, String installEmail, String installPartnerName);

    /**
     * 用于派单查询主合作用户下的装维人员信息
     * @param partnerName
     * @param queryInfo
     * @return
     */
    List<UserInstallSendOrdersVO> queryUserInstallToPrimaryUser(String partnerName, String queryInfo);
}
