package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 角色-权限关系表
 *
 * <AUTHOR>
public class RoleAuth implements Serializable {
    /**
     * 角色id
     *
     *
     * @mbg.generated Tue Apr 18 15:49:03 CST 2023
     */
    private String roleId;

    /**
     * 权限id
     *
     *
     * @mbg.generated Tue Apr 18 15:49:03 CST 2023
     */
    private String authId;

    /**
     *
     * @mbg.generated Tue Apr 18 15:49:03 CST 2023
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..role_auth.role_id
     *
     * @return the value of supply_chain..role_auth.role_id
     *
     * @mbg.generated Tue Apr 18 15:49:03 CST 2023
     */
    public String getRoleId() {
        return roleId;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 15:49:03 CST 2023
     */
    public RoleAuth withRoleId(String roleId) {
        this.setRoleId(roleId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..role_auth.role_id
     *
     * @param roleId the value for supply_chain..role_auth.role_id
     *
     * @mbg.generated Tue Apr 18 15:49:03 CST 2023
     */
    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    /**
     * This method returns the value of the database column supply_chain..role_auth.auth_id
     *
     * @return the value of supply_chain..role_auth.auth_id
     *
     * @mbg.generated Tue Apr 18 15:49:03 CST 2023
     */
    public String getAuthId() {
        return authId;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 15:49:03 CST 2023
     */
    public RoleAuth withAuthId(String authId) {
        this.setAuthId(authId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..role_auth.auth_id
     *
     * @param authId the value for supply_chain..role_auth.auth_id
     *
     * @mbg.generated Tue Apr 18 15:49:03 CST 2023
     */
    public void setAuthId(String authId) {
        this.authId = authId;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 15:49:03 CST 2023
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", roleId=").append(roleId);
        sb.append(", authId=").append(authId);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Apr 18 15:49:03 CST 2023
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RoleAuth other = (RoleAuth) that;
        return (this.getRoleId() == null ? other.getRoleId() == null : this.getRoleId().equals(other.getRoleId()))
            && (this.getAuthId() == null ? other.getAuthId() == null : this.getAuthId().equals(other.getAuthId()));
    }

    /**
     *
     * @mbg.generated Tue Apr 18 15:49:03 CST 2023
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getRoleId() == null) ? 0 : getRoleId().hashCode());
        result = prime * result + ((getAuthId() == null) ? 0 : getAuthId().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Apr 18 15:49:03 CST 2023
     */
    public enum Column {
        roleId("role_id", "roleId", "VARCHAR", false),
        authId("auth_id", "authId", "VARCHAR", false);

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Apr 18 15:49:03 CST 2023
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}