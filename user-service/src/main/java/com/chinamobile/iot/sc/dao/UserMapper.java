package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.User;
import com.chinamobile.iot.sc.pojo.entity.UserExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserMapper {
    /**
     *
<<<<<<< HEAD
     * @mbg.generated Thu Aug 10 10:27:08 CST 2023
=======
     * @mbg.generated Mon Sep 11 15:41:34 CST 2023
>>>>>>> develop-account-logoff
     */
    long countByExample(UserExample example);

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Thu Aug 10 10:27:08 CST 2023
=======
     * @mbg.generated Mon Sep 11 15:41:34 CST 2023
>>>>>>> develop-account-logoff
     */
    int deleteByExample(UserExample example);

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Thu Aug 10 10:27:08 CST 2023
=======
     * @mbg.generated Mon Sep 11 15:41:34 CST 2023
>>>>>>> develop-account-logoff
     */
    int deleteByPrimaryKey(String userId);

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Thu Aug 10 10:27:08 CST 2023
=======
     * @mbg.generated Mon Sep 11 15:41:34 CST 2023
>>>>>>> develop-account-logoff
     */
    int insert(User record);

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Thu Aug 10 10:27:08 CST 2023
=======
     * @mbg.generated Mon Sep 11 15:41:34 CST 2023
>>>>>>> develop-account-logoff
     */
    int insertSelective(User record);

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Thu Aug 10 10:27:08 CST 2023
=======
     * @mbg.generated Mon Sep 11 15:41:34 CST 2023
>>>>>>> develop-account-logoff
     */
    List<User> selectByExample(UserExample example);

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Thu Aug 10 10:27:08 CST 2023
=======
     * @mbg.generated Mon Sep 11 15:41:34 CST 2023
>>>>>>> develop-account-logoff
     */
    User selectByPrimaryKey(String userId);

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Thu Aug 10 10:27:08 CST 2023
=======
     * @mbg.generated Mon Sep 11 15:41:34 CST 2023
>>>>>>> develop-account-logoff
     */
    int updateByExampleSelective(@Param("record") User record, @Param("example") UserExample example);

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Thu Aug 10 10:27:08 CST 2023
=======
     * @mbg.generated Mon Sep 11 15:41:34 CST 2023
>>>>>>> develop-account-logoff
     */
    int updateByExample(@Param("record") User record, @Param("example") UserExample example);

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Thu Aug 10 10:27:08 CST 2023
=======
     * @mbg.generated Mon Sep 11 15:41:34 CST 2023
>>>>>>> develop-account-logoff
     */
    int updateByPrimaryKeySelective(User record);

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Thu Aug 10 10:27:08 CST 2023
=======
     * @mbg.generated Mon Sep 11 15:41:34 CST 2023
>>>>>>> develop-account-logoff
     */
    int updateByPrimaryKey(User record);

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Thu Aug 10 10:27:08 CST 2023
=======
     * @mbg.generated Mon Sep 11 15:41:34 CST 2023
>>>>>>> develop-account-logoff
     */
    int batchInsert(@Param("list") List<User> list);

    /**
     *
<<<<<<< HEAD
     * @mbg.generated Thu Aug 10 10:27:08 CST 2023
=======
     * @mbg.generated Mon Sep 11 15:41:34 CST 2023
>>>>>>> develop-account-logoff
     */
    int batchInsertSelective(@Param("list") List<User> list, @Param("selective") User.Column ... selective);
}