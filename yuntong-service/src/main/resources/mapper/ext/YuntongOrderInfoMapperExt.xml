<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.yuntong.dao.ext.YuntongOrderInfoMapperExt">
    <select id="orderInfoList" parameterType="com.chinamobile.yuntong.pojo.param.OrderInfoListParam"
            resultType="com.chinamobile.yuntong.pojo.mapper.OrderInfoListDO">
        SELECT
        yoi.order_id orderId,
        yoi.valorem_total valoremTotal,
        yoi.business_date businessDate,
        yoi.bill_no_time billNoTime,
        CASE yoi.settle_status
            WHEN 0 THEN '待计收'
            WHEN 1 THEN '计收成功'
            WHEN 2 THEN '计收失败'
        END settleStatus,
        CASE yair.status
            WHEN -1 THEN '失败'
            WHEN 0 THEN '待发票'
            WHEN 1 THEN '开票中'
            WHEN 2 THEN '开票成功'
        END invoiceStatus,
        yair.id recId
        FROM
        yuntong_order_info yoi
        left join yuntong_apply_invoice_record yair on yair.order_id = yoi.order_id
        where  1=1
        <if test="param.orderId != null and param.orderId != ''">
            and yoi.order_id = #{param.orderId}
        </if>
        <if test="param.settleStatus != null and param.settleStatus != ''">
            and yoi.settle_status = #{param.settleStatus}
        </if>
        <if test="param.invoiceStatus != null and param.invoiceStatus != ''">
            and yair.status = #{param.invoiceStatus}
        </if>
        <if test="param.startTime != null and param.startTime != ''">
            and yoi.bill_no_time <![CDATA[ >= ]]> #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and yoi.bill_no_time  <![CDATA[ <= ]]> #{param.endTime}
        </if>

        order by yoi.create_time DESC
    </select>

    <select id="orderInfoListExport" parameterType="com.chinamobile.yuntong.pojo.param.OrderInfoListExportParam"
            resultType="com.chinamobile.yuntong.pojo.dto.OrderInfoListExcelDTO">
        SELECT
        yoi.order_id orderId,
        yom.product_name productName,
        yom.product_number productNumber,
        yom.amount amount,
        yoi.valorem_total valoremTotal,
        yoi.bill_no_time billNoTime,
        yom.taxprice taxprice,
        yoi.business_date businessDate,
        yoi.bill_no_time billNoTime,
        CASE yoi.settle_status
            WHEN '0' THEN '待计收'
            WHEN '1' THEN '计收成功'
            WHEN '2' THEN '计收失败'
        END settleStatus,
        CASE yair.status
            WHEN -1 THEN '失败'
            WHEN 0 THEN '待发票'
            WHEN 1 THEN '开票中'
            WHEN 2 THEN '开票成功'
        END invoiceStatus
        FROM
        yuntong_order_info yoi
        left join yuntong_order_material yom on yom.order_number = yoi.order_id
        left join yuntong_apply_invoice_record yair on yair.order_id = yoi.order_id
        where  1=1
        <if test="param.settleStatus != null and param.settleStatus != ''">
            and yoi.settleStatus = #{param.settleStatus}
        </if>
        <if test="param.startTime != null and param.startTime != ''">
            and yoi.bill_no_time <![CDATA[ >= ]]> #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and yoi.bill_no_time  <![CDATA[ <= ]]> #{param.endTime}
        </if>
        <if test="param.businessStartTime  != null and param.businessStartTime  != ''">
            and yoi.business_date <![CDATA[ >= ]]> #{param.businessStartTime }
        </if>
        <if test="param.businessEndTime != null and param.businessEndTime != ''">
            and yoi.business_date  <![CDATA[ <= ]]> #{param.businessEndTime}
        </if>

        order by yoi.create_time DESC
    </select>
</mapper>