package com.chinamobile.yuntong.response.revenue;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 应收系统电子发票开具结果反馈结果
 **/
@Data
public class GetInvoiceResponse {

    private String successFlag;

    private String errorMessage;

    private Integer totalRecord;

    private Integer totalPage;

    private Integer pageSize;

    private Integer currentPage;

    private List<invoiceList> list;

    @Data
    public static class invoiceList{
        private String id;
        //单据编号
        private String documentNumber;
        //发票号码
        private String invoiceNumber;
        //发票代码
        private String invoiceCode;
        //价税合计
        private BigDecimal valoremTotal;
        /**
         * 开票状态-乐企：-1:未找到对应订单,0:未开票,1:开票成功,2:申请中,3:开票中,4:开票失败,5:开票已提交
         */

        private Integer invoiceStatus;

        private List<logInfo> logInfoList;

        private List<invoiceAddress> salesInvoiceAddressList;
    }

    @Data
    public static class logInfo{
        private Long id;
        //发票单据编号
        private String documentNumber;
        /**
         * 调用类型 1--发票保存、2--发票开具提交、3--电子开具、
         * 4--发票打印、5--发票清单打印、6--发票作废、7--检验异常、8--发票开具结果获取
         */
        private Integer callType;
        //结果标识
        private Integer resultFlag;
        //错误信息
        private String errorMsg;

        //创建时间
        private String createTime;

    }

    @Data
    public static class invoiceAddress{
        //发票地址id
        private Long id;
        //发票单据id
        private String salesInvoiceId;

        //发票url地址
        private String url;

        private String type;
    }

}
