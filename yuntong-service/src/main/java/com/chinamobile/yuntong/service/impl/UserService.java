package com.chinamobile.yuntong.service.impl;

import com.alibaba.excel.EasyExcel;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.yuntong.dao.UserYuntongMapper;
import com.chinamobile.yuntong.dao.YuntongOldUserMapper;
import com.chinamobile.yuntong.dao.YuntongUserAddressMapper;
import com.chinamobile.yuntong.excel.YuntongAppUserExcelDTO;
import com.chinamobile.yuntong.exception.StatusContant;
import com.chinamobile.yuntong.pojo.entity.*;
import com.chinamobile.yuntong.pojo.param.AddAddressParam;
import com.chinamobile.yuntong.pojo.param.UpdateAddressParam;
import com.chinamobile.yuntong.pojo.vo.YuntongUserAddressVO;
import com.chinamobile.yuntong.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserService implements IUserService {

    @Resource
    private YuntongUserAddressMapper yuntongUserAddressMapper;

    @Resource
    private UserYuntongMapper userYuntongMapper;

    @Resource
    private YuntongOldUserMapper yuntongOldUserMapper;

    @Override
    public List<YuntongUserAddressVO> getAddressList(String userId) {
        UserYuntong userYuntong = userYuntongMapper.selectByPrimaryKey(userId);
        if (userYuntong == null) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "用户不存在");
        }
        List<YuntongUserAddress> existAddresses = yuntongUserAddressMapper.selectByExample(
                new YuntongUserAddressExample().createCriteria()
                        .andUserIdEqualTo(userYuntong.getId())
                        .example()
                        .orderBy("is_default, create_time")
        );
        if (CollectionUtils.isNotEmpty(existAddresses)) {
            return existAddresses.stream().map(yuntongUserAddress -> {
                YuntongUserAddressVO yuntongUserAddressVO = new YuntongUserAddressVO();
                BeanUtils.copyProperties(yuntongUserAddress, yuntongUserAddressVO);
                return yuntongUserAddressVO;
            }).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAddress(AddAddressParam param, String userId) {
        UserYuntong userYuntong = userYuntongMapper.selectByPrimaryKey(userId);
        if (userYuntong == null) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "用户不存在");
        }
        // 查找用户已有地址列表，判断是否重复添加
        List<YuntongUserAddress> existAddresses = yuntongUserAddressMapper.selectByExample(
                new YuntongUserAddressExample().createCriteria()
                        .andUserIdEqualTo(userYuntong.getId())
                        .example()
        );
        boolean addressExist = checkAddressExist(param, existAddresses);
        if (addressExist) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "地址已存在");
        }

        // 如果是默认地址，则将其他地址设置为非默认
        if (param.getIsDefault()) {
            removeDefaultAddresses(existAddresses);
        }

        // 添加用户地址
        YuntongUserAddress yuntongUserAddress = new YuntongUserAddress();
        BeanUtils.copyProperties(param, yuntongUserAddress);
        yuntongUserAddress.setId(BaseServiceUtils.getId());
        yuntongUserAddress.setUserId(userYuntong.getId());
        yuntongUserAddress.setCreateTime(new Date());
        yuntongUserAddress.setUpdateTime(new Date());
        yuntongUserAddressMapper.insert(yuntongUserAddress);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAddress(UpdateAddressParam param, String userId) {
        UserYuntong userYuntong = userYuntongMapper.selectByPrimaryKey(userId);
        if (userYuntong == null) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "用户不存在");
        }
        YuntongUserAddress yuntongUserAddress = yuntongUserAddressMapper.selectByPrimaryKey(param.getId());
        if (yuntongUserAddress == null) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "地址不存在");
        }

        // 如果是默认地址，则将其他地址设置为非默认
        if (param.getIsDefault()) {
            List<YuntongUserAddress> otherAddresses = yuntongUserAddressMapper.selectByExample(
                    new YuntongUserAddressExample().createCriteria()
                            .andIdNotEqualTo(param.getId())
                            .andUserIdEqualTo(userYuntong.getId())
                            .example()
            );
            removeDefaultAddresses(otherAddresses);
        }

        // 更新用户地址
        YuntongUserAddress updateAddress = new YuntongUserAddress();
        BeanUtils.copyProperties(param, updateAddress);
        updateAddress.setUserId(userYuntong.getId());
        updateAddress.setUpdateTime(new Date());
        yuntongUserAddressMapper.updateByPrimaryKeySelective(updateAddress);
    }

    @Override
    public void deleteAddress(String id, String userId) {
        UserYuntong userYuntong = userYuntongMapper.selectByPrimaryKey(userId);
        if (userYuntong == null) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "用户不存在");
        }
        YuntongUserAddress yuntongUserAddress = yuntongUserAddressMapper.selectByPrimaryKey(id);
        if (yuntongUserAddress == null || !StringUtils.equals(userId, yuntongUserAddress.getUserId())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "地址不存在");
        }
        yuntongUserAddressMapper.deleteByPrimaryKey(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importYuntongAppUsers(MultipartFile excel) {
        log.info("开始导入云瞳用户数据，文件名: {}", excel.getOriginalFilename());

        try {
            // 读取Excel数据
            log.debug("开始解析Excel文件数据");
            List<YuntongAppUserExcelDTO> datas = EasyExcel.read(excel.getInputStream(), YuntongAppUserExcelDTO.class, null)
                    .sheet(0).headRowNumber(1).doReadSync();

            if (CollectionUtils.isEmpty(datas)) {
                log.warn("导入数据为空，文件名: {}", excel.getOriginalFilename());
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "导入数据为空");
            }

            log.info("成功解析Excel数据，共{}条记录", datas.size());

            // 清空表数据
            log.debug("开始清空云瞳用户表数据");
            yuntongOldUserMapper.deleteByExample(new YuntongOldUserExample());
            log.info("云瞳用户表数据已清空");

            // 分批处理参数
            int batchSize = 1000;
            int totalSize = datas.size();
            int fromIndex = 0;
            int toIndex = Math.min(batchSize, totalSize);
            int batchCount = 0;

            log.info("开始分批导入用户数据，总记录数: {}，每批处理: {}条", totalSize, batchSize);

            while (fromIndex < totalSize) {
                batchCount++;
                log.debug("正在处理第{}批数据，索引范围: {} - {}", batchCount, fromIndex, toIndex - 1);

                // 获取当前批次的数据
                List<YuntongAppUserExcelDTO> batchList = datas.subList(fromIndex, toIndex);

                // 转换为目标对象
                List<YuntongOldUser> users = batchList.stream().map(data -> {
                    YuntongOldUser user = new YuntongOldUser();
                    user.setId(BaseServiceUtils.getId());
                    user.setPhone(data.getPhone());
                    user.setCloudSubscriber("true".equalsIgnoreCase(data.getCloudSubscriber()));
                    user.setCreateTime(new Date());
                    user.setUpdateTime(new Date());
                    return user;
                }).collect(Collectors.toList());

                // 批量插入当前批次
                log.debug("开始插入第{}批数据，共{}条", batchCount, users.size());
                yuntongOldUserMapper.batchInsert(users);
                log.debug("第{}批数据插入完成", batchCount);

                // 更新索引
                fromIndex = toIndex;
                toIndex = Math.min(fromIndex + batchSize, totalSize);
            }

            log.info("云瞳用户数据导入完成，共处理{}批数据，总计{}条记录", batchCount, totalSize);

        } catch (BusinessException e) {
            log.error("导入云瞳用户数据业务异常: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("导入云瞳用户数据系统异常，文件名: {}", excel.getOriginalFilename(), e);
        }
    }

    /**
     * 检查用户地址是否已存在
     */
    private boolean checkAddressExist(AddAddressParam param, List<YuntongUserAddress> existAddresses) {
        if (CollectionUtils.isEmpty(existAddresses)) {
            return false;
        }
        boolean addressExist = false;
        for (YuntongUserAddress existAddress : existAddresses) {
            if (!StringUtils.equals(existAddress.getName(), param.getName())) {
                continue;
            }
            if (!StringUtils.equals(existAddress.getPhone(), param.getPhone())) {
                continue;
            }
            if (!StringUtils.equals(existAddress.getProvince(), param.getProvince())) {
                continue;
            }
            if (!StringUtils.equals(existAddress.getCity(), param.getCity())) {
                continue;
            }
            if (!StringUtils.equals(existAddress.getRegion(), param.getRegion())) {
                continue;
            }
            if (!StringUtils.equals(existAddress.getAddress(), param.getAddress())) {
                continue;
            }
            addressExist = true;
        }
        return addressExist;
    }

    private void removeDefaultAddresses(List<YuntongUserAddress> existAddresses) {
        if (CollectionUtils.isEmpty(existAddresses)) {
            return;
        }
        for (YuntongUserAddress existAddress : existAddresses) {
            if (existAddress.getIsDefault()) {
                existAddress.setIsDefault(false);
                yuntongUserAddressMapper.updateByPrimaryKey(existAddress);
            }
        }
    }
}
