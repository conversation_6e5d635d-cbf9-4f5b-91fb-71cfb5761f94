package com.chinamobile.yuntong.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.util.ExcelUtils;
import com.chinamobile.yuntong.config.YuntongActivityStatusEnum;
import com.chinamobile.yuntong.dao.*;
import com.chinamobile.yuntong.dao.ext.YuntongActivityMapperExt;
import com.chinamobile.yuntong.exception.StatusContant;
import com.chinamobile.yuntong.pojo.dto.ExportActivityAwardRecordExcelDTO;
import com.chinamobile.yuntong.pojo.dto.ImportLogisticsTrackingNumberErrorExcelDTO;
import com.chinamobile.yuntong.pojo.dto.ImportLogisticsTrackingNumberExcelDTO;
import com.chinamobile.yuntong.pojo.entity.*;
import com.chinamobile.yuntong.pojo.param.*;
import com.chinamobile.yuntong.pojo.vo.*;
import com.chinamobile.yuntong.service.IActivityService;
import com.chinamobile.yuntong.service.IStatisticsService;
import com.chinamobile.yuntong.util.RedisCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ActivityService implements IActivityService {

    @Resource
    private YuntongActivityMapper yuntongActivityMapper;

    @Resource
    private YuntongActivityAwardsMapper yuntongActivityAwardsMapper;

    @Resource
    private YuntongActivityAwardRecordMapper yuntongActivityAwardRecordMapper;

    @Resource
    private YuntongActivityParticipationRecordMapper yuntongActivityParticipationRecordMapper;

    @Resource
    private YuntongActivityMapperExt yuntongActivityMapperExt;

    @Resource
    private UserYuntongMapper userYuntongMapper;

    @Resource
    private YuntongUserAddressMapper yuntongUserAddressMapper;

    @Resource
    private YuntongOldUserMapper yuntongOldUserMapper;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private RedisCacheUtil redisCacheUtil;

    @Resource
    private IStatisticsService statisticsService;

    @Override
    public PageData<YuntongActivityVO> pageActivities(PageActivitiesParam param) {
        List<YuntongActivityVO> yuntongActivityVOS = yuntongActivityMapperExt.pageActivities(param);
        for (YuntongActivityVO yuntongActivityVO : yuntongActivityVOS) {
            BigInteger viewedCount = redisCacheUtil.get(Constant.REDIS_KEY_YUNTONG_ACTIVITY_VIEWED + yuntongActivityVO.getId());
            BigInteger raffledCount = redisCacheUtil.get(Constant.REDIS_KEY_YUNTONG_ACTIVITY_USER + yuntongActivityVO.getId());
            BigInteger userCount = redisCacheUtil.get(Constant.REDIS_KEY_YUNTONG_ACTIVITY_USER + yuntongActivityVO.getId());
            yuntongActivityVO.setViewedCount(viewedCount != null ? viewedCount.longValue() : 0);
            yuntongActivityVO.setRaffledCount(raffledCount != null ? raffledCount.longValue() : 0);
            yuntongActivityVO.setUserCount(userCount != null ? userCount.longValue() : 0);
        }
        Long count = yuntongActivityMapperExt.countActivities();
        PageData<YuntongActivityVO> pageData = new PageData<>();
        pageData.setPage(param.getPageNum());
        pageData.setData(yuntongActivityVOS);
        pageData.setCount(count != null ? count : 0);
        return pageData;
    }

    @Override
    public void updateActivity(UpdateActivityParam param) {
        YuntongActivity yuntongActivity = yuntongActivityMapper.selectByPrimaryKey(param.getId());
        if (null == yuntongActivity) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "活动不存在");
        }
        yuntongActivity.setCouponUrlWithCloud(param.getCouponUrlWithCloud());
        yuntongActivity.setCouponUrlWithoutCloud(param.getCouponUrlWithoutCloud());
        yuntongActivity.setCouponUrlNewUser(param.getCouponUrlNewUser());
        yuntongActivity.setUpdateTime(new Date());
        yuntongActivityMapper.updateByPrimaryKey(yuntongActivity);
    }

    @Override
    public PageData<YuntongActivityAwardRecordVO> pageAwardsRecord(PageAwardRecordParam param) {
        List<YuntongActivityAwardRecordVO> yuntongActivityAwardRecordVOS = yuntongActivityMapperExt.pageAwardsRecord(param);
        Long count = yuntongActivityMapperExt.countAwardsRecord(param);
        PageData<YuntongActivityAwardRecordVO> pageData = new PageData<>();
        pageData.setPage(param.getPageNum());
        pageData.setData(yuntongActivityAwardRecordVOS);
        pageData.setCount(count != null ? count : 0);
        return pageData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void configTrackingNumber(List<ConfigAwardLogisticsTrackingNumberParam> params) {
        for (ConfigAwardLogisticsTrackingNumberParam param : params) {
            List<YuntongActivityAwardRecord> records = yuntongActivityAwardRecordMapper.selectByExample(
                    new YuntongActivityAwardRecordExample().createCriteria()
                            .andAwardCodeEqualTo(param.getAwardCode())
                            .example()
            );
            if (CollectionUtils.isEmpty(records)) {
                throw new BusinessException(StatusContant.PARAM_ERROR, "中奖编码" + param.getAwardCode() + "不存在");
            }
            YuntongActivityAwardRecord record = records.get(0);
            if (StringUtils.isEmpty(record.getAddress())) {
                throw new BusinessException(StatusContant.PARAM_ERROR, "用户未领取奖品");
            }
            record.setLogisticsTrackingNumber(param.getLogisticsTrackingNumber());
            yuntongActivityAwardRecordMapper.updateByPrimaryKeySelective(record);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void configTrackingNumberBatch(MultipartFile file) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        try {
            if (!ExcelUtils.suffixCheck(file.getOriginalFilename())) {
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("文件格式错误，只能是xlsx,xls类型", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            if (file.isEmpty() || file.getSize() == 0) {
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("上传文件不能为空", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            List<ImportLogisticsTrackingNumberExcelDTO> dtos = EasyExcel.read(file.getInputStream(), ImportLogisticsTrackingNumberExcelDTO.class, null)
                    .sheet(0).headRowNumber(1).doReadSync();
            if (CollectionUtils.isEmpty(dtos)) {
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("上传文件内容为空", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }
            List<ImportLogisticsTrackingNumberErrorExcelDTO> errors = new ArrayList<>();
            for (ImportLogisticsTrackingNumberExcelDTO dto : dtos) {
                List<YuntongActivityAwardRecord> records = yuntongActivityAwardRecordMapper.selectByExample(
                        new YuntongActivityAwardRecordExample().createCriteria()
                                .andAwardCodeEqualTo(dto.getAwardCode())
                                .example()
                );
                if (CollectionUtils.isEmpty(records)) {
                    ImportLogisticsTrackingNumberErrorExcelDTO error = new ImportLogisticsTrackingNumberErrorExcelDTO();
                    BeanUtils.copyProperties(dto, error);
                    error.setReason("中奖编码" + dto.getAwardCode() + "不存在");
                    errors.add(error);
                    continue;
                }
                YuntongActivityAwardRecord record = records.get(0);
                if (StringUtils.isEmpty(record.getAddress())) {
                    ImportLogisticsTrackingNumberErrorExcelDTO error = new ImportLogisticsTrackingNumberErrorExcelDTO();
                    BeanUtils.copyProperties(dto, error);
                    error.setReason("用户未领取奖品");
                    errors.add(error);
                    continue;
                }
//                if (!StringUtils.isEmpty(record.getLogisticsTrackingNumber())) {
//                    ImportLogisticsTrackingNumberErrorExcelDTO error = new ImportLogisticsTrackingNumberErrorExcelDTO();
//                    BeanUtils.copyProperties(dto, error);
//                    error.setReason("该奖品已发货");
//                    continue;
//                }
                record.setLogisticsTrackingNumber(dto.getLogisticsTrackingNumber());
                yuntongActivityAwardRecordMapper.updateByPrimaryKeySelective(record);
            }
            if (!CollectionUtils.isEmpty(errors)) {
                ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                        .registerWriteHandler(getHorizontalCellStyleStrategy())
                        .registerWriteHandler(new ExcelWidthStyleStrategy())
                        .build();

                // 写入商品套餐信息
                WriteSheet writeSheet = EasyExcel.writerSheet(0, "Sheet1").head(ImportLogisticsTrackingNumberErrorExcelDTO.class).build();
                excelWriter.write(errors, writeSheet);
                String fileName = "批量发货-和目十周年（失败）.xlsx";
                response.setHeader("statecode", "11111");
                response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
                response.setHeader("Access-Control-Expose-Headers", "content-disposition");
                response.setContentType("application/octet-stream");
                response.setCharacterEncoding("utf-8");
                excelWriter.finish();
            } else {
                response.setHeader("statecode", "00000");
                response.setHeader("message", URLEncoder.encode("success", "UTF-8").replaceAll("\\+", "%20"));
            }
        } catch (Exception e) {
            log.error("数据格式异常", e);
            try {
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("数据格式异常", "UTF-8").replaceAll("\\+", "%20"));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    @Override
    public YuntongActivityAwardRecordDetailVO awardRecordDetail(String id) {
        YuntongActivityAwardRecordDetailVO detailVO = yuntongActivityMapperExt.awardRecordDetail(id);
        if (detailVO == null) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "中奖记录不存在");
        }
        return detailVO;
    }

    @Override
    public void exportAwardsRecord(String activityId) {
        List<ExportActivityAwardRecordExcelDTO> dtos = yuntongActivityMapperExt.exportAwardsRecord(activityId);
        if (CollectionUtils.isEmpty(dtos)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "无数据");
        }

        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        try {
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .registerWriteHandler(getHorizontalCellStyleStrategy())
                    .registerWriteHandler(new ExcelWidthStyleStrategy())
                    .build();

            // 写入商品套餐信息
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "中奖名单").head(ExportActivityAwardRecordExcelDTO.class).build();
            excelWriter.write(dtos, writeSheet);
            String fileName = "和目十周年中奖名单.xlsx";
            response.setHeader("statecode", "00000");
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setHeader("Access-Control-Expose-Headers", "content-disposition");
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            excelWriter.finish();
        } catch (IOException e) {
            log.error("和目十周年中奖名单导出失败！", e);
            throw new BusinessException(StatusContant.PARAM_ERROR, "导出失败");
        }
    }

    @Override
    public void offline(ActivityOfflineParam param) {
        YuntongActivity yuntongActivity = yuntongActivityMapper.selectByPrimaryKey(param.getId());
        if (null == yuntongActivity) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "活动不存在");
        }
        yuntongActivity.setOfflineStatus(param.getOffline());
        yuntongActivity.setUpdateTime(new Date());
        yuntongActivityMapper.updateByPrimaryKey(yuntongActivity);
    }

    @DS("query")
    @Override
    public YuntongActivityH5VO getH5ActivityById(String activityId, LoginIfo4Redis loginIfo4Redis) {
        String userId = loginIfo4Redis.getUserId();
        log.info("云瞳H5活动详情，activityId：{}，userId：{}", activityId, userId);
        // 统计页面查看次数
        statisticsService.h5ActivityDetail(activityId, loginIfo4Redis.getPhone());
        YuntongActivity activity = yuntongActivityMapper.selectByPrimaryKey(activityId);
        if (activity == null) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "活动不存在");
        }
        List<YuntongActivityAwards> awards = yuntongActivityAwardsMapper.selectByExample(
                new YuntongActivityAwardsExample().createCriteria()
                        .andActivityIdEqualTo(activityId)
                        .example()
        );
        if (CollectionUtils.isEmpty(awards)) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "活动奖项不存在");
        }
        YuntongActivityH5VO vo = new YuntongActivityH5VO();
        BeanUtils.copyProperties(activity, vo);
        vo.setAwards(awards.stream().map(item -> {
            YuntongActivityAwardsH5VO h5VO = new YuntongActivityAwardsH5VO();
            BeanUtils.copyProperties(item, h5VO);
            return h5VO;
        }).collect(Collectors.toList()));
        // 查询剩余抽奖次数
        vo.setRemainingRaffleEntries(getRemainingRaffleEntries(activityId, userId));
        // 设置活动状态
        if (!activity.getOfflineStatus() && activity.getStartTime().after(new Date())) {
            // 活动未开始
            vo.setStatus(YuntongActivityStatusEnum.NOT_START.code);
        } else if (!activity.getOfflineStatus() && activity.getStopTime().after(new Date())) {
            // 活动进行中
            vo.setStatus(YuntongActivityStatusEnum.IN_PROGRESS.code);
        } else {
            // 活动已结束
            vo.setStatus(YuntongActivityStatusEnum.STOPPED.code);
        }
        // 设置领券地址
        List<YuntongOldUser> oldUsers = yuntongOldUserMapper.selectByExample(
                new YuntongOldUserExample().createCriteria()
                        .andPhoneEqualTo(loginIfo4Redis.getPhone())
                        .example()
        );
        String couponUrl;
        if (CollectionUtils.isEmpty(oldUsers)) {
            couponUrl = activity.getCouponUrlNewUser();
        } else if (Boolean.TRUE.equals(oldUsers.get(0).getCloudSubscriber())) {
            couponUrl = activity.getCouponUrlWithCloud();
        } else {
            couponUrl = activity.getCouponUrlWithoutCloud();
        }
        vo.setCouponUrl(couponUrl);
        log.info("云瞳H5活动详情, activityId：{}，userId：{}，活动状态：{}，剩余抽奖次数：{}", activityId, userId, YuntongActivityStatusEnum.fromCode(vo.getStatus()).text, vo.getRemainingRaffleEntries());
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public String raffleByActivityId(RaffleParam param, LoginIfo4Redis loginIfo4Redis) {
        long start = System.currentTimeMillis();
        String activityId = param.getActivityId();
        String userId = loginIfo4Redis.getUserId();
        log.info("云瞳H5活动抽奖，activityId：{}，userId：{}", activityId, userId);
        YuntongActivity activity = yuntongActivityMapper.selectByPrimaryKey(activityId);
        if (activity == null) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "活动不存在");
        }
        // 测试用，临时注释
        int remainingRaffleEntries = getRemainingRaffleEntries(activityId, userId);
        if (remainingRaffleEntries <= 0) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "抽奖次数已用完，请明日再来");
        }
        UserYuntong userYuntong = userYuntongMapper.selectByPrimaryKey(userId);
        if (userYuntong == null) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "用户不存在");
        }
        if (ObjectUtils.isEmpty(userYuntong.getPhone())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "抱歉，暂无活动参与资格");
        }
        if (activity.getOfflineStatus() || activity.getStopTime().before(new Date())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "活动已结束，期待下次与您相遇");
        } else if (activity.getStartTime().after(new Date())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "活动尚未开始，敬请期待");
        }

        final List<YuntongActivityAwards> yuntongActivityAwards = yuntongActivityAwardsMapper.selectByExample(
                new YuntongActivityAwardsExample().createCriteria()
                        .andActivityIdEqualTo(activityId)
                        .example()
        );
        if (CollectionUtils.isEmpty(yuntongActivityAwards)) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "活动奖项不存在");
        }
        // 保存参与记录
        YuntongActivityParticipationRecord participationRecord = new YuntongActivityParticipationRecord();
        participationRecord.setId(BaseServiceUtils.getId());
        participationRecord.setActivityId(activityId);
        participationRecord.setUserId(userId);
        participationRecord.setTime(new Date());
        participationRecord.setCreateTime(new Date());
        participationRecord.setUpdateTime(new Date());
        yuntongActivityParticipationRecordMapper.insertSelective(participationRecord);
        // 统计抽奖次数
        statisticsService.h5ActivityRaffle(activityId, loginIfo4Redis.getPhone());
        return redisUtil.smartLock("SC:YUNTONG:ACTIVITY_LOCK:RAFFLE:" + activityId, () -> {
            // 所有奖品的中奖概率
            int totalProbability = yuntongActivityAwards.stream().mapToInt(YuntongActivityAwards::getProbability).sum();
            // 不中奖的概率
            int delta = Math.max(100000000 - totalProbability, 0);
            List<YuntongActivityAwards> awards = yuntongActivityAwards.stream()
                    // 过滤掉已达到上限的奖品
                    .filter(a -> !isAwardLimitReached(a))
                    // 将奖品按抽中概率从小到大排序
                    .sorted(Comparator.comparingInt(YuntongActivityAwards::getProbability))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(awards)) {
                throw new BusinessException(StatusContant.PARAM_ERROR, "奖品已抽完");
            }
            // 重新计算过滤后的所有奖品中奖概率
            totalProbability = awards.stream().mapToInt(YuntongActivityAwards::getProbability).sum();
            int randomNum = new Random().nextInt(totalProbability + delta);
            int currentProbability = 0;
            for (YuntongActivityAwards award : awards) {
                currentProbability += award.getProbability();
                if (randomNum <= currentProbability) {
                    // 记录中奖记录
                    YuntongActivityAwardRecord record = new YuntongActivityAwardRecord();
                    record.setId(BaseServiceUtils.getId());
                    record.setActivityId(activityId);
                    record.setUserId(userId);
                    record.setAwardId(award.getId());
                    record.setAwardCode("hmszn" + generateAwardCode());
                    record.setCreateTime(new Date());
                    record.setUpdateTime(new Date());
                    yuntongActivityAwardRecordMapper.insertSelective(record);
                    log.info("云瞳H5活动抽奖，activityId：{}，userId：{}，中奖编码：{}", activityId, userId, record.getAwardCode());
                    long end = System.currentTimeMillis();
                    log.info("云瞳H5活动抽奖，耗时：{}ms", end - start);
                    return award.getId();
                }
            }
            log.info("云瞳H5活动抽奖，activityId：{}，userId：{}，未中奖", activityId, userId);
            long end = System.currentTimeMillis();
            log.info("云瞳H5活动抽奖，耗时：{}ms", end - start);
            return null;
        });

    }

    @DS("query")
    @Override
    public PageData<YuntongUserAwardH5VO> getAwardRecordsByUser(PageUserAwardRecordParam param, String userId) {
        UserYuntong userYuntong = userYuntongMapper.selectByPrimaryKey(userId);
        if (userYuntong == null) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "用户不存在");
        }
        param.setUserId(userYuntong.getId());
        List<YuntongUserAwardH5VO> vos = yuntongActivityMapperExt.getAwardRecordsByUser(param);
        Long count = yuntongActivityMapperExt.countAwardRecordsByUser(param);
        PageData<YuntongUserAwardH5VO> pageData = new PageData<>();
        pageData.setPage(param.getPageNum());
        pageData.setData(vos);
        pageData.setCount(count != null ? count : 0);
        return pageData;
    }

    @DS("save")
    @Override
    public void bindAwardAddress(BindAwardAddressParam param) {
        YuntongUserAddress address = yuntongUserAddressMapper.selectByPrimaryKey(param.getAddressId());
        if (address == null) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "地址不存在");
        }
        YuntongActivityAwardRecord record = yuntongActivityAwardRecordMapper.selectByPrimaryKey(param.getAwardRecordId());
        if (record == null) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "中奖记录不存在");
        }
        if (!ObjectUtils.isEmpty(record.getAddress())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "该奖品已领取");
        }
        record.setAddressName(address.getName());
        record.setAddressPhone(address.getPhone());
        record.setProvince(address.getProvince());
        record.setCity(address.getCity());
        record.setRegion(address.getRegion());
        record.setAddress(address.getAddress());
        record.setUpdateTime(new Date());
        yuntongActivityAwardRecordMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 判断奖品是否达到上限
     */
    private boolean isAwardLimitReached(YuntongActivityAwards yuntongActivityAwards) {
        // 判断奖品是否抽完
        Integer max = yuntongActivityAwards.getMaxCount();
        max = max != null ? max : 0;
        long total = yuntongActivityAwardRecordMapper.countByExample(
                new YuntongActivityAwardRecordExample().createCriteria()
                        .andActivityIdEqualTo(yuntongActivityAwards.getActivityId())
                        .andAwardIdEqualTo(yuntongActivityAwards.getId())
                        .example()
        );
        if (total >= max) {
            return true;
        }
        
        // 判断当天抽奖次数是否达到上限
        Integer dailyCount = yuntongActivityAwards.getMaxCountDaily();
        dailyCount = dailyCount != null ? dailyCount : 0;
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(LocalTime.MAX);
        Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
        long todayCount = yuntongActivityAwardRecordMapper.countByExample(
                new YuntongActivityAwardRecordExample().createCriteria()
                        .andActivityIdEqualTo(yuntongActivityAwards.getActivityId())
                        .andAwardIdEqualTo(yuntongActivityAwards.getId())
                        .andCreateTimeBetween(startDate, endDate)
                        .example()
        );
        return todayCount >= dailyCount;
    }

    /**
     * 查询剩余抽奖次数
     */
    private int getRemainingRaffleEntries(String activityId, String userId) {
        // 查询当日参与记录，如果未参与则剩余抽奖次数为1，否则为0
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(LocalTime.MAX);

        // 测试用
        //LocalDateTime now = LocalDateTime.now();
        //startOfDay = now.minusMinutes(3);
        //endOfDay = now;

        Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
        List<YuntongActivityParticipationRecord> records = yuntongActivityParticipationRecordMapper.selectByExample(
                new YuntongActivityParticipationRecordExample().createCriteria()
                        .andActivityIdEqualTo(activityId)
                        .andUserIdEqualTo(userId)
                        .andTimeBetween(startDate, endDate)
                        .example()
        );
        return CollectionUtils.isEmpty(records) ? 1 : 0;
    }

    private HorizontalCellStyleStrategy getHorizontalCellStyleStrategy() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为白色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.index);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        //边框
        headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        headWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        headWriteCellStyle.setBorderRight(BorderStyle.THIN);
        headWriteCellStyle.setBorderTop(BorderStyle.THIN);
        //自动换行
        headWriteCellStyle.setWrapped(true);
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setBold(true);
        headWriteFont.setFontName("宋体");
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
        contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        // 背景绿色
        contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //边框
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        //自动换行
        contentWriteCellStyle.setWrapped(true);
        //文字
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 12);
        contentWriteFont.setFontName("宋体");
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    public static class ExcelWidthStyleStrategy extends AbstractColumnWidthStyleStrategy {

        @Override
        protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> list, Cell cell, Head head, Integer integer, Boolean aBoolean) {
            Sheet sheet = writeSheetHolder.getSheet();
            sheet.setColumnWidth(cell.getColumnIndex(),5000);
        }
    }

    /**
     * 生成唯一的 6 位数字
     */
    private String generateAwardCode() {
        // 获取当前时间戳的毫秒部分
        long nanoTime = System.nanoTime();
        String baseTime = String.valueOf(nanoTime);

        // 获取当前时间戳最后三位数字（确保精度为 6 位数字）
        int hash = baseTime.hashCode() % 900000; // 确保结果是 6 位数
        if (hash < 0) {
            hash += 900000; // 转为正数
        }

        // 在时间戳的基础上加入随机数，确保同一时间内生成唯一数
        int randomNumber = new Random().nextInt(100); // 生成 0 到 99 的随机数

        // 组合时间戳和随机数，确保唯一性
        int finalNumber = (hash + randomNumber) % 900000; // 确保 6 位数
        if (finalNumber < 0) {
            finalNumber += 900000; // 转为正数
        }

        return String.format("%06d", finalNumber);
    }

}
