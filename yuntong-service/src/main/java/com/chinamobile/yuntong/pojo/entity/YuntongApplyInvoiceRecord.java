package com.chinamobile.yuntong.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 同步IOT商城开发票申请表
 *
 * <AUTHOR>
public class YuntongApplyInvoiceRecord implements Serializable {
    /**
     * 申请开发票id
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private String id;

    /**
     * 业务订单流水号，销售业务的唯一标识，与 3.1.7 2C订单同步 orderId取值相同;
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private String orderId;

    /**
     * 请求发票开具操作时间，YYYYMMDD24HHMISS
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private String printDate;

    /**
     * 发票标记，0：专票；1：普票；个人客户只能开具普票，不能开具专票。固定填：1;
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private String frank;

    /**
     * 纳税人名称
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private String pName;

    /**
     * 纳税人识别号
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private String identifyNum;

    /**
     * 纳税人地址信息
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private String addressInfo;

    /**
     * 纳税人电话
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private String phoneNumber;

    /**
     * 纳税人开户行
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private String bankName;

    /**
     * 纳税人开户行账号
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private String bankId;

    /**
     * 订单金额，单位元
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private String orderPrice;

    /**
     * 申请状态，-1-失败，0-待发票，1-开票中，2-开票成功
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private Integer status;

    /**
     * 备注，失败原因等
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private String remark;

    /**
     * 开票申请记录，创建时间：yyyy-MM-dd HH:mm:ss
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private Date createTime;

    /**
     * 开票申请记录，更新时间：yyyy-MM-dd HH:mm:ss
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private Date updateTime;

    /**
     * 单位：厘仅一级管理目录=A13：软件服务且原子商品类型=A：自营软件服务时同步开票申请金额；
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private String voucherSum;

    /**
     * 申请应收系统开票单号
     *
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private String applyDocumentNumber;

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.id
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.id
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.id
     *
     * @param id the value for supply_chain..yuntong_apply_invoice_record.id
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.order_id
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.order_id
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.order_id
     *
     * @param orderId the value for supply_chain..yuntong_apply_invoice_record.order_id
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.print_date
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.print_date
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public String getPrintDate() {
        return printDate;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withPrintDate(String printDate) {
        this.setPrintDate(printDate);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.print_date
     *
     * @param printDate the value for supply_chain..yuntong_apply_invoice_record.print_date
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setPrintDate(String printDate) {
        this.printDate = printDate;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.frank
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.frank
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public String getFrank() {
        return frank;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withFrank(String frank) {
        this.setFrank(frank);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.frank
     *
     * @param frank the value for supply_chain..yuntong_apply_invoice_record.frank
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setFrank(String frank) {
        this.frank = frank;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.p_name
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.p_name
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public String getpName() {
        return pName;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withpName(String pName) {
        this.setpName(pName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.p_name
     *
     * @param pName the value for supply_chain..yuntong_apply_invoice_record.p_name
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setpName(String pName) {
        this.pName = pName;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.identify_num
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.identify_num
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public String getIdentifyNum() {
        return identifyNum;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withIdentifyNum(String identifyNum) {
        this.setIdentifyNum(identifyNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.identify_num
     *
     * @param identifyNum the value for supply_chain..yuntong_apply_invoice_record.identify_num
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setIdentifyNum(String identifyNum) {
        this.identifyNum = identifyNum;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.address_info
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.address_info
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public String getAddressInfo() {
        return addressInfo;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withAddressInfo(String addressInfo) {
        this.setAddressInfo(addressInfo);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.address_info
     *
     * @param addressInfo the value for supply_chain..yuntong_apply_invoice_record.address_info
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setAddressInfo(String addressInfo) {
        this.addressInfo = addressInfo;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.phone_number
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.phone_number
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public String getPhoneNumber() {
        return phoneNumber;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withPhoneNumber(String phoneNumber) {
        this.setPhoneNumber(phoneNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.phone_number
     *
     * @param phoneNumber the value for supply_chain..yuntong_apply_invoice_record.phone_number
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.bank_name
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.bank_name
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public String getBankName() {
        return bankName;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withBankName(String bankName) {
        this.setBankName(bankName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.bank_name
     *
     * @param bankName the value for supply_chain..yuntong_apply_invoice_record.bank_name
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.bank_iD
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.bank_iD
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public String getBankId() {
        return bankId;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withBankId(String bankId) {
        this.setBankId(bankId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.bank_iD
     *
     * @param bankId the value for supply_chain..yuntong_apply_invoice_record.bank_iD
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.order_price
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.order_price
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public String getOrderPrice() {
        return orderPrice;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withOrderPrice(String orderPrice) {
        this.setOrderPrice(orderPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.order_price
     *
     * @param orderPrice the value for supply_chain..yuntong_apply_invoice_record.order_price
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setOrderPrice(String orderPrice) {
        this.orderPrice = orderPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.status
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.status
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public Integer getStatus() {
        return status;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withStatus(Integer status) {
        this.setStatus(status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.status
     *
     * @param status the value for supply_chain..yuntong_apply_invoice_record.status
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.remark
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.remark
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public String getRemark() {
        return remark;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withRemark(String remark) {
        this.setRemark(remark);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.remark
     *
     * @param remark the value for supply_chain..yuntong_apply_invoice_record.remark
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.create_time
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.create_time
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.create_time
     *
     * @param createTime the value for supply_chain..yuntong_apply_invoice_record.create_time
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.update_time
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.update_time
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.update_time
     *
     * @param updateTime the value for supply_chain..yuntong_apply_invoice_record.update_time
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.voucher_sum
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.voucher_sum
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public String getVoucherSum() {
        return voucherSum;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withVoucherSum(String voucherSum) {
        this.setVoucherSum(voucherSum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.voucher_sum
     *
     * @param voucherSum the value for supply_chain..yuntong_apply_invoice_record.voucher_sum
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setVoucherSum(String voucherSum) {
        this.voucherSum = voucherSum;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_apply_invoice_record.apply_document_number
     *
     * @return the value of supply_chain..yuntong_apply_invoice_record.apply_document_number
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public String getApplyDocumentNumber() {
        return applyDocumentNumber;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public YuntongApplyInvoiceRecord withApplyDocumentNumber(String applyDocumentNumber) {
        this.setApplyDocumentNumber(applyDocumentNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_apply_invoice_record.apply_document_number
     *
     * @param applyDocumentNumber the value for supply_chain..yuntong_apply_invoice_record.apply_document_number
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public void setApplyDocumentNumber(String applyDocumentNumber) {
        this.applyDocumentNumber = applyDocumentNumber;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", printDate=").append(printDate);
        sb.append(", frank=").append(frank);
        sb.append(", pName=").append(pName);
        sb.append(", identifyNum=").append(identifyNum);
        sb.append(", addressInfo=").append(addressInfo);
        sb.append(", phoneNumber=").append(phoneNumber);
        sb.append(", bankName=").append(bankName);
        sb.append(", bankId=").append(bankId);
        sb.append(", orderPrice=").append(orderPrice);
        sb.append(", status=").append(status);
        sb.append(", remark=").append(remark);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", voucherSum=").append(voucherSum);
        sb.append(", applyDocumentNumber=").append(applyDocumentNumber);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        YuntongApplyInvoiceRecord other = (YuntongApplyInvoiceRecord) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getPrintDate() == null ? other.getPrintDate() == null : this.getPrintDate().equals(other.getPrintDate()))
            && (this.getFrank() == null ? other.getFrank() == null : this.getFrank().equals(other.getFrank()))
            && (this.getpName() == null ? other.getpName() == null : this.getpName().equals(other.getpName()))
            && (this.getIdentifyNum() == null ? other.getIdentifyNum() == null : this.getIdentifyNum().equals(other.getIdentifyNum()))
            && (this.getAddressInfo() == null ? other.getAddressInfo() == null : this.getAddressInfo().equals(other.getAddressInfo()))
            && (this.getPhoneNumber() == null ? other.getPhoneNumber() == null : this.getPhoneNumber().equals(other.getPhoneNumber()))
            && (this.getBankName() == null ? other.getBankName() == null : this.getBankName().equals(other.getBankName()))
            && (this.getBankId() == null ? other.getBankId() == null : this.getBankId().equals(other.getBankId()))
            && (this.getOrderPrice() == null ? other.getOrderPrice() == null : this.getOrderPrice().equals(other.getOrderPrice()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getVoucherSum() == null ? other.getVoucherSum() == null : this.getVoucherSum().equals(other.getVoucherSum()))
            && (this.getApplyDocumentNumber() == null ? other.getApplyDocumentNumber() == null : this.getApplyDocumentNumber().equals(other.getApplyDocumentNumber()));
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getPrintDate() == null) ? 0 : getPrintDate().hashCode());
        result = prime * result + ((getFrank() == null) ? 0 : getFrank().hashCode());
        result = prime * result + ((getpName() == null) ? 0 : getpName().hashCode());
        result = prime * result + ((getIdentifyNum() == null) ? 0 : getIdentifyNum().hashCode());
        result = prime * result + ((getAddressInfo() == null) ? 0 : getAddressInfo().hashCode());
        result = prime * result + ((getPhoneNumber() == null) ? 0 : getPhoneNumber().hashCode());
        result = prime * result + ((getBankName() == null) ? 0 : getBankName().hashCode());
        result = prime * result + ((getBankId() == null) ? 0 : getBankId().hashCode());
        result = prime * result + ((getOrderPrice() == null) ? 0 : getOrderPrice().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getVoucherSum() == null) ? 0 : getVoucherSum().hashCode());
        result = prime * result + ((getApplyDocumentNumber() == null) ? 0 : getApplyDocumentNumber().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 14:51:32 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        printDate("print_date", "printDate", "VARCHAR", false),
        frank("frank", "frank", "VARCHAR", false),
        pName("p_name", "pName", "VARCHAR", false),
        identifyNum("identify_num", "identifyNum", "VARCHAR", false),
        addressInfo("address_info", "addressInfo", "VARCHAR", false),
        phoneNumber("phone_number", "phoneNumber", "VARCHAR", false),
        bankName("bank_name", "bankName", "VARCHAR", false),
        bankId("bank_iD", "bankId", "VARCHAR", false),
        orderPrice("order_price", "orderPrice", "VARCHAR", false),
        status("status", "status", "INTEGER", false),
        remark("remark", "remark", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        voucherSum("voucher_sum", "voucherSum", "VARCHAR", false),
        applyDocumentNumber("apply_document_number", "applyDocumentNumber", "VARCHAR", false);

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Mar 10 14:51:32 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}