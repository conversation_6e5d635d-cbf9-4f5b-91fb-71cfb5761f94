package com.chinamobile.yuntong.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 和目活动中奖记录
 *
 * <AUTHOR>
public class YuntongActivityAwardRecord implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    private String id;

    /**
     * 活动id
     *
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    private String activityId;

    /**
     * 用户id
     *
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    private String userId;

    /**
     * 奖品id
     *
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    private String awardId;

    /**
     * 中奖编码
     *
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    private String awardCode;

    /**
     * 收货人姓名
     *
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    private String addressName;

    /**
     * 收货人电话
     *
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    private String addressPhone;

    /**
     * 省
     *
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    private String province;

    /**
     * 市
     *
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    private String city;

    /**
     * 区县
     *
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    private String region;

    /**
     * 详细地址
     *
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    private String address;

    /**
     * 物流单号
     *
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    private String logisticsTrackingNumber;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..yuntong_activity_award_record.id
     *
     * @return the value of supply_chain..yuntong_activity_award_record.id
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public YuntongActivityAwardRecord withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_activity_award_record.id
     *
     * @param id the value for supply_chain..yuntong_activity_award_record.id
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_activity_award_record.activity_id
     *
     * @return the value of supply_chain..yuntong_activity_award_record.activity_id
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public String getActivityId() {
        return activityId;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public YuntongActivityAwardRecord withActivityId(String activityId) {
        this.setActivityId(activityId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_activity_award_record.activity_id
     *
     * @param activityId the value for supply_chain..yuntong_activity_award_record.activity_id
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public void setActivityId(String activityId) {
        this.activityId = activityId == null ? null : activityId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_activity_award_record.user_id
     *
     * @return the value of supply_chain..yuntong_activity_award_record.user_id
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public YuntongActivityAwardRecord withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_activity_award_record.user_id
     *
     * @param userId the value for supply_chain..yuntong_activity_award_record.user_id
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_activity_award_record.award_id
     *
     * @return the value of supply_chain..yuntong_activity_award_record.award_id
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public String getAwardId() {
        return awardId;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public YuntongActivityAwardRecord withAwardId(String awardId) {
        this.setAwardId(awardId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_activity_award_record.award_id
     *
     * @param awardId the value for supply_chain..yuntong_activity_award_record.award_id
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public void setAwardId(String awardId) {
        this.awardId = awardId == null ? null : awardId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_activity_award_record.award_code
     *
     * @return the value of supply_chain..yuntong_activity_award_record.award_code
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public String getAwardCode() {
        return awardCode;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public YuntongActivityAwardRecord withAwardCode(String awardCode) {
        this.setAwardCode(awardCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_activity_award_record.award_code
     *
     * @param awardCode the value for supply_chain..yuntong_activity_award_record.award_code
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public void setAwardCode(String awardCode) {
        this.awardCode = awardCode == null ? null : awardCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_activity_award_record.address_name
     *
     * @return the value of supply_chain..yuntong_activity_award_record.address_name
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public String getAddressName() {
        return addressName;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public YuntongActivityAwardRecord withAddressName(String addressName) {
        this.setAddressName(addressName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_activity_award_record.address_name
     *
     * @param addressName the value for supply_chain..yuntong_activity_award_record.address_name
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public void setAddressName(String addressName) {
        this.addressName = addressName == null ? null : addressName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_activity_award_record.address_phone
     *
     * @return the value of supply_chain..yuntong_activity_award_record.address_phone
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public String getAddressPhone() {
        return addressPhone;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public YuntongActivityAwardRecord withAddressPhone(String addressPhone) {
        this.setAddressPhone(addressPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_activity_award_record.address_phone
     *
     * @param addressPhone the value for supply_chain..yuntong_activity_award_record.address_phone
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public void setAddressPhone(String addressPhone) {
        this.addressPhone = addressPhone == null ? null : addressPhone.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_activity_award_record.province
     *
     * @return the value of supply_chain..yuntong_activity_award_record.province
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public String getProvince() {
        return province;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public YuntongActivityAwardRecord withProvince(String province) {
        this.setProvince(province);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_activity_award_record.province
     *
     * @param province the value for supply_chain..yuntong_activity_award_record.province
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_activity_award_record.city
     *
     * @return the value of supply_chain..yuntong_activity_award_record.city
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public String getCity() {
        return city;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public YuntongActivityAwardRecord withCity(String city) {
        this.setCity(city);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_activity_award_record.city
     *
     * @param city the value for supply_chain..yuntong_activity_award_record.city
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_activity_award_record.region
     *
     * @return the value of supply_chain..yuntong_activity_award_record.region
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public String getRegion() {
        return region;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public YuntongActivityAwardRecord withRegion(String region) {
        this.setRegion(region);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_activity_award_record.region
     *
     * @param region the value for supply_chain..yuntong_activity_award_record.region
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public void setRegion(String region) {
        this.region = region == null ? null : region.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_activity_award_record.address
     *
     * @return the value of supply_chain..yuntong_activity_award_record.address
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public String getAddress() {
        return address;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public YuntongActivityAwardRecord withAddress(String address) {
        this.setAddress(address);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_activity_award_record.address
     *
     * @param address the value for supply_chain..yuntong_activity_award_record.address
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_activity_award_record.logistics_tracking_number
     *
     * @return the value of supply_chain..yuntong_activity_award_record.logistics_tracking_number
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public String getLogisticsTrackingNumber() {
        return logisticsTrackingNumber;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public YuntongActivityAwardRecord withLogisticsTrackingNumber(String logisticsTrackingNumber) {
        this.setLogisticsTrackingNumber(logisticsTrackingNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_activity_award_record.logistics_tracking_number
     *
     * @param logisticsTrackingNumber the value for supply_chain..yuntong_activity_award_record.logistics_tracking_number
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public void setLogisticsTrackingNumber(String logisticsTrackingNumber) {
        this.logisticsTrackingNumber = logisticsTrackingNumber == null ? null : logisticsTrackingNumber.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_activity_award_record.create_time
     *
     * @return the value of supply_chain..yuntong_activity_award_record.create_time
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public YuntongActivityAwardRecord withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_activity_award_record.create_time
     *
     * @param createTime the value for supply_chain..yuntong_activity_award_record.create_time
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_activity_award_record.update_time
     *
     * @return the value of supply_chain..yuntong_activity_award_record.update_time
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public YuntongActivityAwardRecord withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_activity_award_record.update_time
     *
     * @param updateTime the value for supply_chain..yuntong_activity_award_record.update_time
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", activityId=").append(activityId);
        sb.append(", userId=").append(userId);
        sb.append(", awardId=").append(awardId);
        sb.append(", awardCode=").append(awardCode);
        sb.append(", addressName=").append(addressName);
        sb.append(", addressPhone=").append(addressPhone);
        sb.append(", province=").append(province);
        sb.append(", city=").append(city);
        sb.append(", region=").append(region);
        sb.append(", address=").append(address);
        sb.append(", logisticsTrackingNumber=").append(logisticsTrackingNumber);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        YuntongActivityAwardRecord other = (YuntongActivityAwardRecord) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getActivityId() == null ? other.getActivityId() == null : this.getActivityId().equals(other.getActivityId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getAwardId() == null ? other.getAwardId() == null : this.getAwardId().equals(other.getAwardId()))
            && (this.getAwardCode() == null ? other.getAwardCode() == null : this.getAwardCode().equals(other.getAwardCode()))
            && (this.getAddressName() == null ? other.getAddressName() == null : this.getAddressName().equals(other.getAddressName()))
            && (this.getAddressPhone() == null ? other.getAddressPhone() == null : this.getAddressPhone().equals(other.getAddressPhone()))
            && (this.getProvince() == null ? other.getProvince() == null : this.getProvince().equals(other.getProvince()))
            && (this.getCity() == null ? other.getCity() == null : this.getCity().equals(other.getCity()))
            && (this.getRegion() == null ? other.getRegion() == null : this.getRegion().equals(other.getRegion()))
            && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
            && (this.getLogisticsTrackingNumber() == null ? other.getLogisticsTrackingNumber() == null : this.getLogisticsTrackingNumber().equals(other.getLogisticsTrackingNumber()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getActivityId() == null) ? 0 : getActivityId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getAwardId() == null) ? 0 : getAwardId().hashCode());
        result = prime * result + ((getAwardCode() == null) ? 0 : getAwardCode().hashCode());
        result = prime * result + ((getAddressName() == null) ? 0 : getAddressName().hashCode());
        result = prime * result + ((getAddressPhone() == null) ? 0 : getAddressPhone().hashCode());
        result = prime * result + ((getProvince() == null) ? 0 : getProvince().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getRegion() == null) ? 0 : getRegion().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getLogisticsTrackingNumber() == null) ? 0 : getLogisticsTrackingNumber().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        activityId("activity_id", "activityId", "VARCHAR", false),
        userId("user_id", "userId", "VARCHAR", false),
        awardId("award_id", "awardId", "VARCHAR", false),
        awardCode("award_code", "awardCode", "VARCHAR", false),
        addressName("address_name", "addressName", "VARCHAR", false),
        addressPhone("address_phone", "addressPhone", "VARCHAR", false),
        province("province", "province", "VARCHAR", false),
        city("city", "city", "VARCHAR", false),
        region("region", "region", "VARCHAR", false),
        address("address", "address", "VARCHAR", false),
        logisticsTrackingNumber("logistics_tracking_number", "logisticsTrackingNumber", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Jan 13 17:22:37 GMT+08:00 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}