package com.chinamobile.yuntong.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class OrderInfoListExcelDTO {

    /**
     * 订单id
     */
    @ExcelProperty(value = "订单编号", index = 0)
    private String orderId;

    /**
     * 物料编码
     *
     *
     * @mbg.generated Mon Feb 10 15:47:55 CST 2025
     */
    @ExcelProperty(value = "物料编码", index = 1)
    private String productNumber;

    /**
     * 物料名称
     *
     *
     * @mbg.generated Mon Feb 10 15:47:55 CST 2025
     */
    @ExcelProperty(value = "物料名称", index = 2)
    private String productName;


    /**
     * 计价数量
     *
     *
     * @mbg.generated Mon Feb 10 15:47:55 CST 2025
     */
    @ExcelProperty(value = "订购数量", index = 3)
    private Double amount;

    /**
     * 计价数量
     *
     *
     */
    @ExcelProperty(value = "单价", index = 4)
    private String taxprice;

    /**
     * 价税合计
     *
     *
     * @mbg.generated Mon Feb 10 15:47:55 CST 2025
     */
    @ExcelProperty(value = "总金额", index = 5)
    private String valoremTotal;



    /**
     * 业务日期
     *
     *
     * @mbg.generated Mon Feb 10 15:47:59 CST 2025
     */
    @ExcelProperty(value = "交易完成时间", index = 6)
    private String businessDate;

    /**
     * 计收状态，0-待计收，1-计收成功， 2-计收失败
     *
     *
     * @mbg.generated Mon Feb 10 15:47:59 CST 2025
     */
    @ExcelProperty(value = "计收状态", index = 7)
    private String settleStatus;

    /**
     * 同步给市场销售系统的时间
     *
     *
     * @mbg.generated Mon Feb 10 15:47:59 CST 2025
     */
    @ExcelProperty(value = "计收时间", index = 8)
    private String billNoTime;



    /**
     * 开票申请状态，-1-失败，0-待发票，1-开票中，2-开票成功
     *
     */
    @ExcelProperty(value = "开票申请状态", index = 9)
    private String invoiceStatus;
}
