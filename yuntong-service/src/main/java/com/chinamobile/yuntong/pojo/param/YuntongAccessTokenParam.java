package com.chinamobile.yuntong.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 云瞳token请求参数
 */
@Data
public class YuntongAccessTokenParam {

    @NotBlank(message = "手机号不能为空")
    private String phone;

    @NotBlank(message = "时间戳不能为空")
    private String timestamp;

    @NotBlank(message = "随机字符串不能为空")
    private String nonce;

    @NotBlank(message = "签名不能为空")
    private String signature;

}
