package com.chinamobile.yuntong.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class YuntongOrderInfoExample {
    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public YuntongOrderInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public YuntongOrderInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public YuntongOrderInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public static Criteria newAndCreateCriteria() {
        YuntongOrderInfoExample example = new YuntongOrderInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public YuntongOrderInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public YuntongOrderInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andClientNameIsNull() {
            addCriterion("client_name is null");
            return (Criteria) this;
        }

        public Criteria andClientNameIsNotNull() {
            addCriterion("client_name is not null");
            return (Criteria) this;
        }

        public Criteria andClientNameEqualTo(String value) {
            addCriterion("client_name =", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("client_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientNameNotEqualTo(String value) {
            addCriterion("client_name <>", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("client_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientNameGreaterThan(String value) {
            addCriterion("client_name >", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("client_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientNameGreaterThanOrEqualTo(String value) {
            addCriterion("client_name >=", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("client_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientNameLessThan(String value) {
            addCriterion("client_name <", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("client_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientNameLessThanOrEqualTo(String value) {
            addCriterion("client_name <=", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("client_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientNameLike(String value) {
            addCriterion("client_name like", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameNotLike(String value) {
            addCriterion("client_name not like", value, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameIn(List<String> values) {
            addCriterion("client_name in", values, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameNotIn(List<String> values) {
            addCriterion("client_name not in", values, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameBetween(String value1, String value2) {
            addCriterion("client_name between", value1, value2, "clientName");
            return (Criteria) this;
        }

        public Criteria andClientNameNotBetween(String value1, String value2) {
            addCriterion("client_name not between", value1, value2, "clientName");
            return (Criteria) this;
        }

        public Criteria andBusinessDateIsNull() {
            addCriterion("business_date is null");
            return (Criteria) this;
        }

        public Criteria andBusinessDateIsNotNull() {
            addCriterion("business_date is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessDateEqualTo(Date value) {
            addCriterion("business_date =", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("business_date = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessDateNotEqualTo(Date value) {
            addCriterion("business_date <>", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("business_date <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessDateGreaterThan(Date value) {
            addCriterion("business_date >", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("business_date > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessDateGreaterThanOrEqualTo(Date value) {
            addCriterion("business_date >=", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("business_date >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessDateLessThan(Date value) {
            addCriterion("business_date <", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("business_date < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessDateLessThanOrEqualTo(Date value) {
            addCriterion("business_date <=", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("business_date <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBusinessDateIn(List<Date> values) {
            addCriterion("business_date in", values, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateNotIn(List<Date> values) {
            addCriterion("business_date not in", values, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateBetween(Date value1, Date value2) {
            addCriterion("business_date between", value1, value2, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateNotBetween(Date value1, Date value2) {
            addCriterion("business_date not between", value1, value2, "businessDate");
            return (Criteria) this;
        }

        public Criteria andApproveIdIsNull() {
            addCriterion("approve_id is null");
            return (Criteria) this;
        }

        public Criteria andApproveIdIsNotNull() {
            addCriterion("approve_id is not null");
            return (Criteria) this;
        }

        public Criteria andApproveIdEqualTo(String value) {
            addCriterion("approve_id =", value, "approveId");
            return (Criteria) this;
        }

        public Criteria andApproveIdEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("approve_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApproveIdNotEqualTo(String value) {
            addCriterion("approve_id <>", value, "approveId");
            return (Criteria) this;
        }

        public Criteria andApproveIdNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("approve_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApproveIdGreaterThan(String value) {
            addCriterion("approve_id >", value, "approveId");
            return (Criteria) this;
        }

        public Criteria andApproveIdGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("approve_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApproveIdGreaterThanOrEqualTo(String value) {
            addCriterion("approve_id >=", value, "approveId");
            return (Criteria) this;
        }

        public Criteria andApproveIdGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("approve_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApproveIdLessThan(String value) {
            addCriterion("approve_id <", value, "approveId");
            return (Criteria) this;
        }

        public Criteria andApproveIdLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("approve_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApproveIdLessThanOrEqualTo(String value) {
            addCriterion("approve_id <=", value, "approveId");
            return (Criteria) this;
        }

        public Criteria andApproveIdLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("approve_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApproveIdLike(String value) {
            addCriterion("approve_id like", value, "approveId");
            return (Criteria) this;
        }

        public Criteria andApproveIdNotLike(String value) {
            addCriterion("approve_id not like", value, "approveId");
            return (Criteria) this;
        }

        public Criteria andApproveIdIn(List<String> values) {
            addCriterion("approve_id in", values, "approveId");
            return (Criteria) this;
        }

        public Criteria andApproveIdNotIn(List<String> values) {
            addCriterion("approve_id not in", values, "approveId");
            return (Criteria) this;
        }

        public Criteria andApproveIdBetween(String value1, String value2) {
            addCriterion("approve_id between", value1, value2, "approveId");
            return (Criteria) this;
        }

        public Criteria andApproveIdNotBetween(String value1, String value2) {
            addCriterion("approve_id not between", value1, value2, "approveId");
            return (Criteria) this;
        }

        public Criteria andValoremTotalIsNull() {
            addCriterion("valorem_total is null");
            return (Criteria) this;
        }

        public Criteria andValoremTotalIsNotNull() {
            addCriterion("valorem_total is not null");
            return (Criteria) this;
        }

        public Criteria andValoremTotalEqualTo(String value) {
            addCriterion("valorem_total =", value, "valoremTotal");
            return (Criteria) this;
        }

        public Criteria andValoremTotalEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("valorem_total = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValoremTotalNotEqualTo(String value) {
            addCriterion("valorem_total <>", value, "valoremTotal");
            return (Criteria) this;
        }

        public Criteria andValoremTotalNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("valorem_total <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValoremTotalGreaterThan(String value) {
            addCriterion("valorem_total >", value, "valoremTotal");
            return (Criteria) this;
        }

        public Criteria andValoremTotalGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("valorem_total > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValoremTotalGreaterThanOrEqualTo(String value) {
            addCriterion("valorem_total >=", value, "valoremTotal");
            return (Criteria) this;
        }

        public Criteria andValoremTotalGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("valorem_total >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValoremTotalLessThan(String value) {
            addCriterion("valorem_total <", value, "valoremTotal");
            return (Criteria) this;
        }

        public Criteria andValoremTotalLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("valorem_total < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValoremTotalLessThanOrEqualTo(String value) {
            addCriterion("valorem_total <=", value, "valoremTotal");
            return (Criteria) this;
        }

        public Criteria andValoremTotalLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("valorem_total <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValoremTotalLike(String value) {
            addCriterion("valorem_total like", value, "valoremTotal");
            return (Criteria) this;
        }

        public Criteria andValoremTotalNotLike(String value) {
            addCriterion("valorem_total not like", value, "valoremTotal");
            return (Criteria) this;
        }

        public Criteria andValoremTotalIn(List<String> values) {
            addCriterion("valorem_total in", values, "valoremTotal");
            return (Criteria) this;
        }

        public Criteria andValoremTotalNotIn(List<String> values) {
            addCriterion("valorem_total not in", values, "valoremTotal");
            return (Criteria) this;
        }

        public Criteria andValoremTotalBetween(String value1, String value2) {
            addCriterion("valorem_total between", value1, value2, "valoremTotal");
            return (Criteria) this;
        }

        public Criteria andValoremTotalNotBetween(String value1, String value2) {
            addCriterion("valorem_total not between", value1, value2, "valoremTotal");
            return (Criteria) this;
        }

        public Criteria andClientIsNull() {
            addCriterion("client is null");
            return (Criteria) this;
        }

        public Criteria andClientIsNotNull() {
            addCriterion("client is not null");
            return (Criteria) this;
        }

        public Criteria andClientEqualTo(String value) {
            addCriterion("client =", value, "client");
            return (Criteria) this;
        }

        public Criteria andClientEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("client = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientNotEqualTo(String value) {
            addCriterion("client <>", value, "client");
            return (Criteria) this;
        }

        public Criteria andClientNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("client <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientGreaterThan(String value) {
            addCriterion("client >", value, "client");
            return (Criteria) this;
        }

        public Criteria andClientGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("client > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientGreaterThanOrEqualTo(String value) {
            addCriterion("client >=", value, "client");
            return (Criteria) this;
        }

        public Criteria andClientGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("client >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientLessThan(String value) {
            addCriterion("client <", value, "client");
            return (Criteria) this;
        }

        public Criteria andClientLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("client < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientLessThanOrEqualTo(String value) {
            addCriterion("client <=", value, "client");
            return (Criteria) this;
        }

        public Criteria andClientLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("client <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientLike(String value) {
            addCriterion("client like", value, "client");
            return (Criteria) this;
        }

        public Criteria andClientNotLike(String value) {
            addCriterion("client not like", value, "client");
            return (Criteria) this;
        }

        public Criteria andClientIn(List<String> values) {
            addCriterion("client in", values, "client");
            return (Criteria) this;
        }

        public Criteria andClientNotIn(List<String> values) {
            addCriterion("client not in", values, "client");
            return (Criteria) this;
        }

        public Criteria andClientBetween(String value1, String value2) {
            addCriterion("client between", value1, value2, "client");
            return (Criteria) this;
        }

        public Criteria andClientNotBetween(String value1, String value2) {
            addCriterion("client not between", value1, value2, "client");
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdIsNull() {
            addCriterion("sale_org_id is null");
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdIsNotNull() {
            addCriterion("sale_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdEqualTo(String value) {
            addCriterion("sale_org_id =", value, "saleOrgId");
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_org_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdNotEqualTo(String value) {
            addCriterion("sale_org_id <>", value, "saleOrgId");
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_org_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdGreaterThan(String value) {
            addCriterion("sale_org_id >", value, "saleOrgId");
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_org_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdGreaterThanOrEqualTo(String value) {
            addCriterion("sale_org_id >=", value, "saleOrgId");
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_org_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdLessThan(String value) {
            addCriterion("sale_org_id <", value, "saleOrgId");
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_org_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdLessThanOrEqualTo(String value) {
            addCriterion("sale_org_id <=", value, "saleOrgId");
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_org_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdLike(String value) {
            addCriterion("sale_org_id like", value, "saleOrgId");
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdNotLike(String value) {
            addCriterion("sale_org_id not like", value, "saleOrgId");
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdIn(List<String> values) {
            addCriterion("sale_org_id in", values, "saleOrgId");
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdNotIn(List<String> values) {
            addCriterion("sale_org_id not in", values, "saleOrgId");
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdBetween(String value1, String value2) {
            addCriterion("sale_org_id between", value1, value2, "saleOrgId");
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdNotBetween(String value1, String value2) {
            addCriterion("sale_org_id not between", value1, value2, "saleOrgId");
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameIsNull() {
            addCriterion("sale_org_name is null");
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameIsNotNull() {
            addCriterion("sale_org_name is not null");
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameEqualTo(String value) {
            addCriterion("sale_org_name =", value, "saleOrgName");
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_org_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameNotEqualTo(String value) {
            addCriterion("sale_org_name <>", value, "saleOrgName");
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_org_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameGreaterThan(String value) {
            addCriterion("sale_org_name >", value, "saleOrgName");
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_org_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("sale_org_name >=", value, "saleOrgName");
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_org_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameLessThan(String value) {
            addCriterion("sale_org_name <", value, "saleOrgName");
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_org_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameLessThanOrEqualTo(String value) {
            addCriterion("sale_org_name <=", value, "saleOrgName");
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_org_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameLike(String value) {
            addCriterion("sale_org_name like", value, "saleOrgName");
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameNotLike(String value) {
            addCriterion("sale_org_name not like", value, "saleOrgName");
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameIn(List<String> values) {
            addCriterion("sale_org_name in", values, "saleOrgName");
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameNotIn(List<String> values) {
            addCriterion("sale_org_name not in", values, "saleOrgName");
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameBetween(String value1, String value2) {
            addCriterion("sale_org_name between", value1, value2, "saleOrgName");
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameNotBetween(String value1, String value2) {
            addCriterion("sale_org_name not between", value1, value2, "saleOrgName");
            return (Criteria) this;
        }

        public Criteria andCostCenterNameIsNull() {
            addCriterion("cost_center_name is null");
            return (Criteria) this;
        }

        public Criteria andCostCenterNameIsNotNull() {
            addCriterion("cost_center_name is not null");
            return (Criteria) this;
        }

        public Criteria andCostCenterNameEqualTo(String value) {
            addCriterion("cost_center_name =", value, "costCenterName");
            return (Criteria) this;
        }

        public Criteria andCostCenterNameEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("cost_center_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterNameNotEqualTo(String value) {
            addCriterion("cost_center_name <>", value, "costCenterName");
            return (Criteria) this;
        }

        public Criteria andCostCenterNameNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("cost_center_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterNameGreaterThan(String value) {
            addCriterion("cost_center_name >", value, "costCenterName");
            return (Criteria) this;
        }

        public Criteria andCostCenterNameGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("cost_center_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterNameGreaterThanOrEqualTo(String value) {
            addCriterion("cost_center_name >=", value, "costCenterName");
            return (Criteria) this;
        }

        public Criteria andCostCenterNameGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("cost_center_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterNameLessThan(String value) {
            addCriterion("cost_center_name <", value, "costCenterName");
            return (Criteria) this;
        }

        public Criteria andCostCenterNameLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("cost_center_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterNameLessThanOrEqualTo(String value) {
            addCriterion("cost_center_name <=", value, "costCenterName");
            return (Criteria) this;
        }

        public Criteria andCostCenterNameLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("cost_center_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterNameLike(String value) {
            addCriterion("cost_center_name like", value, "costCenterName");
            return (Criteria) this;
        }

        public Criteria andCostCenterNameNotLike(String value) {
            addCriterion("cost_center_name not like", value, "costCenterName");
            return (Criteria) this;
        }

        public Criteria andCostCenterNameIn(List<String> values) {
            addCriterion("cost_center_name in", values, "costCenterName");
            return (Criteria) this;
        }

        public Criteria andCostCenterNameNotIn(List<String> values) {
            addCriterion("cost_center_name not in", values, "costCenterName");
            return (Criteria) this;
        }

        public Criteria andCostCenterNameBetween(String value1, String value2) {
            addCriterion("cost_center_name between", value1, value2, "costCenterName");
            return (Criteria) this;
        }

        public Criteria andCostCenterNameNotBetween(String value1, String value2) {
            addCriterion("cost_center_name not between", value1, value2, "costCenterName");
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeIsNull() {
            addCriterion("cost_center_code is null");
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeIsNotNull() {
            addCriterion("cost_center_code is not null");
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeEqualTo(String value) {
            addCriterion("cost_center_code =", value, "costCenterCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("cost_center_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeNotEqualTo(String value) {
            addCriterion("cost_center_code <>", value, "costCenterCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("cost_center_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeGreaterThan(String value) {
            addCriterion("cost_center_code >", value, "costCenterCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("cost_center_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeGreaterThanOrEqualTo(String value) {
            addCriterion("cost_center_code >=", value, "costCenterCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("cost_center_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeLessThan(String value) {
            addCriterion("cost_center_code <", value, "costCenterCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("cost_center_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeLessThanOrEqualTo(String value) {
            addCriterion("cost_center_code <=", value, "costCenterCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("cost_center_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeLike(String value) {
            addCriterion("cost_center_code like", value, "costCenterCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeNotLike(String value) {
            addCriterion("cost_center_code not like", value, "costCenterCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeIn(List<String> values) {
            addCriterion("cost_center_code in", values, "costCenterCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeNotIn(List<String> values) {
            addCriterion("cost_center_code not in", values, "costCenterCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeBetween(String value1, String value2) {
            addCriterion("cost_center_code between", value1, value2, "costCenterCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeNotBetween(String value1, String value2) {
            addCriterion("cost_center_code not between", value1, value2, "costCenterCode");
            return (Criteria) this;
        }

        public Criteria andSalesmanIsNull() {
            addCriterion("salesman is null");
            return (Criteria) this;
        }

        public Criteria andSalesmanIsNotNull() {
            addCriterion("salesman is not null");
            return (Criteria) this;
        }

        public Criteria andSalesmanEqualTo(String value) {
            addCriterion("salesman =", value, "salesman");
            return (Criteria) this;
        }

        public Criteria andSalesmanEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("salesman = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanNotEqualTo(String value) {
            addCriterion("salesman <>", value, "salesman");
            return (Criteria) this;
        }

        public Criteria andSalesmanNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("salesman <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanGreaterThan(String value) {
            addCriterion("salesman >", value, "salesman");
            return (Criteria) this;
        }

        public Criteria andSalesmanGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("salesman > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanGreaterThanOrEqualTo(String value) {
            addCriterion("salesman >=", value, "salesman");
            return (Criteria) this;
        }

        public Criteria andSalesmanGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("salesman >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanLessThan(String value) {
            addCriterion("salesman <", value, "salesman");
            return (Criteria) this;
        }

        public Criteria andSalesmanLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("salesman < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanLessThanOrEqualTo(String value) {
            addCriterion("salesman <=", value, "salesman");
            return (Criteria) this;
        }

        public Criteria andSalesmanLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("salesman <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanLike(String value) {
            addCriterion("salesman like", value, "salesman");
            return (Criteria) this;
        }

        public Criteria andSalesmanNotLike(String value) {
            addCriterion("salesman not like", value, "salesman");
            return (Criteria) this;
        }

        public Criteria andSalesmanIn(List<String> values) {
            addCriterion("salesman in", values, "salesman");
            return (Criteria) this;
        }

        public Criteria andSalesmanNotIn(List<String> values) {
            addCriterion("salesman not in", values, "salesman");
            return (Criteria) this;
        }

        public Criteria andSalesmanBetween(String value1, String value2) {
            addCriterion("salesman between", value1, value2, "salesman");
            return (Criteria) this;
        }

        public Criteria andSalesmanNotBetween(String value1, String value2) {
            addCriterion("salesman not between", value1, value2, "salesman");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameIsNull() {
            addCriterion("salesman_name is null");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameIsNotNull() {
            addCriterion("salesman_name is not null");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameEqualTo(String value) {
            addCriterion("salesman_name =", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("salesman_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanNameNotEqualTo(String value) {
            addCriterion("salesman_name <>", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("salesman_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanNameGreaterThan(String value) {
            addCriterion("salesman_name >", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("salesman_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanNameGreaterThanOrEqualTo(String value) {
            addCriterion("salesman_name >=", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("salesman_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanNameLessThan(String value) {
            addCriterion("salesman_name <", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("salesman_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanNameLessThanOrEqualTo(String value) {
            addCriterion("salesman_name <=", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("salesman_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanNameLike(String value) {
            addCriterion("salesman_name like", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameNotLike(String value) {
            addCriterion("salesman_name not like", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameIn(List<String> values) {
            addCriterion("salesman_name in", values, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameNotIn(List<String> values) {
            addCriterion("salesman_name not in", values, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameBetween(String value1, String value2) {
            addCriterion("salesman_name between", value1, value2, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameNotBetween(String value1, String value2) {
            addCriterion("salesman_name not between", value1, value2, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andPayOrgIdIsNull() {
            addCriterion("pay_org_id is null");
            return (Criteria) this;
        }

        public Criteria andPayOrgIdIsNotNull() {
            addCriterion("pay_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andPayOrgIdEqualTo(String value) {
            addCriterion("pay_org_id =", value, "payOrgId");
            return (Criteria) this;
        }

        public Criteria andPayOrgIdEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_org_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayOrgIdNotEqualTo(String value) {
            addCriterion("pay_org_id <>", value, "payOrgId");
            return (Criteria) this;
        }

        public Criteria andPayOrgIdNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_org_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayOrgIdGreaterThan(String value) {
            addCriterion("pay_org_id >", value, "payOrgId");
            return (Criteria) this;
        }

        public Criteria andPayOrgIdGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_org_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayOrgIdGreaterThanOrEqualTo(String value) {
            addCriterion("pay_org_id >=", value, "payOrgId");
            return (Criteria) this;
        }

        public Criteria andPayOrgIdGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_org_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayOrgIdLessThan(String value) {
            addCriterion("pay_org_id <", value, "payOrgId");
            return (Criteria) this;
        }

        public Criteria andPayOrgIdLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_org_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayOrgIdLessThanOrEqualTo(String value) {
            addCriterion("pay_org_id <=", value, "payOrgId");
            return (Criteria) this;
        }

        public Criteria andPayOrgIdLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_org_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayOrgIdLike(String value) {
            addCriterion("pay_org_id like", value, "payOrgId");
            return (Criteria) this;
        }

        public Criteria andPayOrgIdNotLike(String value) {
            addCriterion("pay_org_id not like", value, "payOrgId");
            return (Criteria) this;
        }

        public Criteria andPayOrgIdIn(List<String> values) {
            addCriterion("pay_org_id in", values, "payOrgId");
            return (Criteria) this;
        }

        public Criteria andPayOrgIdNotIn(List<String> values) {
            addCriterion("pay_org_id not in", values, "payOrgId");
            return (Criteria) this;
        }

        public Criteria andPayOrgIdBetween(String value1, String value2) {
            addCriterion("pay_org_id between", value1, value2, "payOrgId");
            return (Criteria) this;
        }

        public Criteria andPayOrgIdNotBetween(String value1, String value2) {
            addCriterion("pay_org_id not between", value1, value2, "payOrgId");
            return (Criteria) this;
        }

        public Criteria andRedBlueIsNull() {
            addCriterion("red_blue is null");
            return (Criteria) this;
        }

        public Criteria andRedBlueIsNotNull() {
            addCriterion("red_blue is not null");
            return (Criteria) this;
        }

        public Criteria andRedBlueEqualTo(String value) {
            addCriterion("red_blue =", value, "redBlue");
            return (Criteria) this;
        }

        public Criteria andRedBlueEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("red_blue = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRedBlueNotEqualTo(String value) {
            addCriterion("red_blue <>", value, "redBlue");
            return (Criteria) this;
        }

        public Criteria andRedBlueNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("red_blue <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRedBlueGreaterThan(String value) {
            addCriterion("red_blue >", value, "redBlue");
            return (Criteria) this;
        }

        public Criteria andRedBlueGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("red_blue > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRedBlueGreaterThanOrEqualTo(String value) {
            addCriterion("red_blue >=", value, "redBlue");
            return (Criteria) this;
        }

        public Criteria andRedBlueGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("red_blue >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRedBlueLessThan(String value) {
            addCriterion("red_blue <", value, "redBlue");
            return (Criteria) this;
        }

        public Criteria andRedBlueLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("red_blue < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRedBlueLessThanOrEqualTo(String value) {
            addCriterion("red_blue <=", value, "redBlue");
            return (Criteria) this;
        }

        public Criteria andRedBlueLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("red_blue <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRedBlueLike(String value) {
            addCriterion("red_blue like", value, "redBlue");
            return (Criteria) this;
        }

        public Criteria andRedBlueNotLike(String value) {
            addCriterion("red_blue not like", value, "redBlue");
            return (Criteria) this;
        }

        public Criteria andRedBlueIn(List<String> values) {
            addCriterion("red_blue in", values, "redBlue");
            return (Criteria) this;
        }

        public Criteria andRedBlueNotIn(List<String> values) {
            addCriterion("red_blue not in", values, "redBlue");
            return (Criteria) this;
        }

        public Criteria andRedBlueBetween(String value1, String value2) {
            addCriterion("red_blue between", value1, value2, "redBlue");
            return (Criteria) this;
        }

        public Criteria andRedBlueNotBetween(String value1, String value2) {
            addCriterion("red_blue not between", value1, value2, "redBlue");
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonIsNull() {
            addCriterion("responsible_person is null");
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonIsNotNull() {
            addCriterion("responsible_person is not null");
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonEqualTo(String value) {
            addCriterion("responsible_person =", value, "responsiblePerson");
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("responsible_person = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonNotEqualTo(String value) {
            addCriterion("responsible_person <>", value, "responsiblePerson");
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("responsible_person <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonGreaterThan(String value) {
            addCriterion("responsible_person >", value, "responsiblePerson");
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("responsible_person > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonGreaterThanOrEqualTo(String value) {
            addCriterion("responsible_person >=", value, "responsiblePerson");
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("responsible_person >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonLessThan(String value) {
            addCriterion("responsible_person <", value, "responsiblePerson");
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("responsible_person < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonLessThanOrEqualTo(String value) {
            addCriterion("responsible_person <=", value, "responsiblePerson");
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("responsible_person <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonLike(String value) {
            addCriterion("responsible_person like", value, "responsiblePerson");
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonNotLike(String value) {
            addCriterion("responsible_person not like", value, "responsiblePerson");
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonIn(List<String> values) {
            addCriterion("responsible_person in", values, "responsiblePerson");
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonNotIn(List<String> values) {
            addCriterion("responsible_person not in", values, "responsiblePerson");
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonBetween(String value1, String value2) {
            addCriterion("responsible_person between", value1, value2, "responsiblePerson");
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonNotBetween(String value1, String value2) {
            addCriterion("responsible_person not between", value1, value2, "responsiblePerson");
            return (Criteria) this;
        }

        public Criteria andTaxAmountForIsNull() {
            addCriterion("tax_amount_for is null");
            return (Criteria) this;
        }

        public Criteria andTaxAmountForIsNotNull() {
            addCriterion("tax_amount_for is not null");
            return (Criteria) this;
        }

        public Criteria andTaxAmountForEqualTo(String value) {
            addCriterion("tax_amount_for =", value, "taxAmountFor");
            return (Criteria) this;
        }

        public Criteria andTaxAmountForEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("tax_amount_for = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxAmountForNotEqualTo(String value) {
            addCriterion("tax_amount_for <>", value, "taxAmountFor");
            return (Criteria) this;
        }

        public Criteria andTaxAmountForNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("tax_amount_for <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxAmountForGreaterThan(String value) {
            addCriterion("tax_amount_for >", value, "taxAmountFor");
            return (Criteria) this;
        }

        public Criteria andTaxAmountForGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("tax_amount_for > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxAmountForGreaterThanOrEqualTo(String value) {
            addCriterion("tax_amount_for >=", value, "taxAmountFor");
            return (Criteria) this;
        }

        public Criteria andTaxAmountForGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("tax_amount_for >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxAmountForLessThan(String value) {
            addCriterion("tax_amount_for <", value, "taxAmountFor");
            return (Criteria) this;
        }

        public Criteria andTaxAmountForLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("tax_amount_for < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxAmountForLessThanOrEqualTo(String value) {
            addCriterion("tax_amount_for <=", value, "taxAmountFor");
            return (Criteria) this;
        }

        public Criteria andTaxAmountForLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("tax_amount_for <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxAmountForLike(String value) {
            addCriterion("tax_amount_for like", value, "taxAmountFor");
            return (Criteria) this;
        }

        public Criteria andTaxAmountForNotLike(String value) {
            addCriterion("tax_amount_for not like", value, "taxAmountFor");
            return (Criteria) this;
        }

        public Criteria andTaxAmountForIn(List<String> values) {
            addCriterion("tax_amount_for in", values, "taxAmountFor");
            return (Criteria) this;
        }

        public Criteria andTaxAmountForNotIn(List<String> values) {
            addCriterion("tax_amount_for not in", values, "taxAmountFor");
            return (Criteria) this;
        }

        public Criteria andTaxAmountForBetween(String value1, String value2) {
            addCriterion("tax_amount_for between", value1, value2, "taxAmountFor");
            return (Criteria) this;
        }

        public Criteria andTaxAmountForNotBetween(String value1, String value2) {
            addCriterion("tax_amount_for not between", value1, value2, "taxAmountFor");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForIsNull() {
            addCriterion("no_tax_amount_for is null");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForIsNotNull() {
            addCriterion("no_tax_amount_for is not null");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForEqualTo(String value) {
            addCriterion("no_tax_amount_for =", value, "noTaxAmountFor");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("no_tax_amount_for = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForNotEqualTo(String value) {
            addCriterion("no_tax_amount_for <>", value, "noTaxAmountFor");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("no_tax_amount_for <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForGreaterThan(String value) {
            addCriterion("no_tax_amount_for >", value, "noTaxAmountFor");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("no_tax_amount_for > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForGreaterThanOrEqualTo(String value) {
            addCriterion("no_tax_amount_for >=", value, "noTaxAmountFor");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("no_tax_amount_for >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForLessThan(String value) {
            addCriterion("no_tax_amount_for <", value, "noTaxAmountFor");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("no_tax_amount_for < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForLessThanOrEqualTo(String value) {
            addCriterion("no_tax_amount_for <=", value, "noTaxAmountFor");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("no_tax_amount_for <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForLike(String value) {
            addCriterion("no_tax_amount_for like", value, "noTaxAmountFor");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForNotLike(String value) {
            addCriterion("no_tax_amount_for not like", value, "noTaxAmountFor");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForIn(List<String> values) {
            addCriterion("no_tax_amount_for in", values, "noTaxAmountFor");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForNotIn(List<String> values) {
            addCriterion("no_tax_amount_for not in", values, "noTaxAmountFor");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForBetween(String value1, String value2) {
            addCriterion("no_tax_amount_for between", value1, value2, "noTaxAmountFor");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForNotBetween(String value1, String value2) {
            addCriterion("no_tax_amount_for not between", value1, value2, "noTaxAmountFor");
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdIsNull() {
            addCriterion("sale_dept_id is null");
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdIsNotNull() {
            addCriterion("sale_dept_id is not null");
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdEqualTo(String value) {
            addCriterion("sale_dept_id =", value, "saleDeptId");
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_dept_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdNotEqualTo(String value) {
            addCriterion("sale_dept_id <>", value, "saleDeptId");
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_dept_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdGreaterThan(String value) {
            addCriterion("sale_dept_id >", value, "saleDeptId");
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_dept_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdGreaterThanOrEqualTo(String value) {
            addCriterion("sale_dept_id >=", value, "saleDeptId");
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_dept_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdLessThan(String value) {
            addCriterion("sale_dept_id <", value, "saleDeptId");
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_dept_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdLessThanOrEqualTo(String value) {
            addCriterion("sale_dept_id <=", value, "saleDeptId");
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("sale_dept_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdLike(String value) {
            addCriterion("sale_dept_id like", value, "saleDeptId");
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdNotLike(String value) {
            addCriterion("sale_dept_id not like", value, "saleDeptId");
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdIn(List<String> values) {
            addCriterion("sale_dept_id in", values, "saleDeptId");
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdNotIn(List<String> values) {
            addCriterion("sale_dept_id not in", values, "saleDeptId");
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdBetween(String value1, String value2) {
            addCriterion("sale_dept_id between", value1, value2, "saleDeptId");
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdNotBetween(String value1, String value2) {
            addCriterion("sale_dept_id not between", value1, value2, "saleDeptId");
            return (Criteria) this;
        }

        public Criteria andBillNoNumberIsNull() {
            addCriterion("bill_no_number is null");
            return (Criteria) this;
        }

        public Criteria andBillNoNumberIsNotNull() {
            addCriterion("bill_no_number is not null");
            return (Criteria) this;
        }

        public Criteria andBillNoNumberEqualTo(String value) {
            addCriterion("bill_no_number =", value, "billNoNumber");
            return (Criteria) this;
        }

        public Criteria andBillNoNumberEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_number = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoNumberNotEqualTo(String value) {
            addCriterion("bill_no_number <>", value, "billNoNumber");
            return (Criteria) this;
        }

        public Criteria andBillNoNumberNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_number <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoNumberGreaterThan(String value) {
            addCriterion("bill_no_number >", value, "billNoNumber");
            return (Criteria) this;
        }

        public Criteria andBillNoNumberGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_number > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoNumberGreaterThanOrEqualTo(String value) {
            addCriterion("bill_no_number >=", value, "billNoNumber");
            return (Criteria) this;
        }

        public Criteria andBillNoNumberGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_number >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoNumberLessThan(String value) {
            addCriterion("bill_no_number <", value, "billNoNumber");
            return (Criteria) this;
        }

        public Criteria andBillNoNumberLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_number < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoNumberLessThanOrEqualTo(String value) {
            addCriterion("bill_no_number <=", value, "billNoNumber");
            return (Criteria) this;
        }

        public Criteria andBillNoNumberLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_number <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoNumberLike(String value) {
            addCriterion("bill_no_number like", value, "billNoNumber");
            return (Criteria) this;
        }

        public Criteria andBillNoNumberNotLike(String value) {
            addCriterion("bill_no_number not like", value, "billNoNumber");
            return (Criteria) this;
        }

        public Criteria andBillNoNumberIn(List<String> values) {
            addCriterion("bill_no_number in", values, "billNoNumber");
            return (Criteria) this;
        }

        public Criteria andBillNoNumberNotIn(List<String> values) {
            addCriterion("bill_no_number not in", values, "billNoNumber");
            return (Criteria) this;
        }

        public Criteria andBillNoNumberBetween(String value1, String value2) {
            addCriterion("bill_no_number between", value1, value2, "billNoNumber");
            return (Criteria) this;
        }

        public Criteria andBillNoNumberNotBetween(String value1, String value2) {
            addCriterion("bill_no_number not between", value1, value2, "billNoNumber");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeIsNull() {
            addCriterion("bill_no_time is null");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeIsNotNull() {
            addCriterion("bill_no_time is not null");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeEqualTo(Date value) {
            addCriterion("bill_no_time =", value, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoTimeNotEqualTo(Date value) {
            addCriterion("bill_no_time <>", value, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoTimeGreaterThan(Date value) {
            addCriterion("bill_no_time >", value, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("bill_no_time >=", value, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoTimeLessThan(Date value) {
            addCriterion("bill_no_time <", value, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoTimeLessThanOrEqualTo(Date value) {
            addCriterion("bill_no_time <=", value, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoTimeIn(List<Date> values) {
            addCriterion("bill_no_time in", values, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeNotIn(List<Date> values) {
            addCriterion("bill_no_time not in", values, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeBetween(Date value1, Date value2) {
            addCriterion("bill_no_time between", value1, value2, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeNotBetween(Date value1, Date value2) {
            addCriterion("bill_no_time not between", value1, value2, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andSettleStatusIsNull() {
            addCriterion("settle_status is null");
            return (Criteria) this;
        }

        public Criteria andSettleStatusIsNotNull() {
            addCriterion("settle_status is not null");
            return (Criteria) this;
        }

        public Criteria andSettleStatusEqualTo(String value) {
            addCriterion("settle_status =", value, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("settle_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleStatusNotEqualTo(String value) {
            addCriterion("settle_status <>", value, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("settle_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleStatusGreaterThan(String value) {
            addCriterion("settle_status >", value, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("settle_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleStatusGreaterThanOrEqualTo(String value) {
            addCriterion("settle_status >=", value, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("settle_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleStatusLessThan(String value) {
            addCriterion("settle_status <", value, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("settle_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleStatusLessThanOrEqualTo(String value) {
            addCriterion("settle_status <=", value, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("settle_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleStatusLike(String value) {
            addCriterion("settle_status like", value, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusNotLike(String value) {
            addCriterion("settle_status not like", value, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusIn(List<String> values) {
            addCriterion("settle_status in", values, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusNotIn(List<String> values) {
            addCriterion("settle_status not in", values, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusBetween(String value1, String value2) {
            addCriterion("settle_status between", value1, value2, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusNotBetween(String value1, String value2) {
            addCriterion("settle_status not between", value1, value2, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(YuntongOrderInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andClientNameLikeInsensitive(String value) {
            addCriterion("upper(client_name) like", value.toUpperCase(), "clientName");
            return (Criteria) this;
        }

        public Criteria andApproveIdLikeInsensitive(String value) {
            addCriterion("upper(approve_id) like", value.toUpperCase(), "approveId");
            return (Criteria) this;
        }

        public Criteria andValoremTotalLikeInsensitive(String value) {
            addCriterion("upper(valorem_total) like", value.toUpperCase(), "valoremTotal");
            return (Criteria) this;
        }

        public Criteria andClientLikeInsensitive(String value) {
            addCriterion("upper(client) like", value.toUpperCase(), "client");
            return (Criteria) this;
        }

        public Criteria andSaleOrgIdLikeInsensitive(String value) {
            addCriterion("upper(sale_org_id) like", value.toUpperCase(), "saleOrgId");
            return (Criteria) this;
        }

        public Criteria andSaleOrgNameLikeInsensitive(String value) {
            addCriterion("upper(sale_org_name) like", value.toUpperCase(), "saleOrgName");
            return (Criteria) this;
        }

        public Criteria andCostCenterNameLikeInsensitive(String value) {
            addCriterion("upper(cost_center_name) like", value.toUpperCase(), "costCenterName");
            return (Criteria) this;
        }

        public Criteria andCostCenterCodeLikeInsensitive(String value) {
            addCriterion("upper(cost_center_code) like", value.toUpperCase(), "costCenterCode");
            return (Criteria) this;
        }

        public Criteria andSalesmanLikeInsensitive(String value) {
            addCriterion("upper(salesman) like", value.toUpperCase(), "salesman");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameLikeInsensitive(String value) {
            addCriterion("upper(salesman_name) like", value.toUpperCase(), "salesmanName");
            return (Criteria) this;
        }

        public Criteria andPayOrgIdLikeInsensitive(String value) {
            addCriterion("upper(pay_org_id) like", value.toUpperCase(), "payOrgId");
            return (Criteria) this;
        }

        public Criteria andRedBlueLikeInsensitive(String value) {
            addCriterion("upper(red_blue) like", value.toUpperCase(), "redBlue");
            return (Criteria) this;
        }

        public Criteria andResponsiblePersonLikeInsensitive(String value) {
            addCriterion("upper(responsible_person) like", value.toUpperCase(), "responsiblePerson");
            return (Criteria) this;
        }

        public Criteria andTaxAmountForLikeInsensitive(String value) {
            addCriterion("upper(tax_amount_for) like", value.toUpperCase(), "taxAmountFor");
            return (Criteria) this;
        }

        public Criteria andNoTaxAmountForLikeInsensitive(String value) {
            addCriterion("upper(no_tax_amount_for) like", value.toUpperCase(), "noTaxAmountFor");
            return (Criteria) this;
        }

        public Criteria andSaleDeptIdLikeInsensitive(String value) {
            addCriterion("upper(sale_dept_id) like", value.toUpperCase(), "saleDeptId");
            return (Criteria) this;
        }

        public Criteria andBillNoNumberLikeInsensitive(String value) {
            addCriterion("upper(bill_no_number) like", value.toUpperCase(), "billNoNumber");
            return (Criteria) this;
        }

        public Criteria andSettleStatusLikeInsensitive(String value) {
            addCriterion("upper(settle_status) like", value.toUpperCase(), "settleStatus");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Mon Mar 10 15:29:26 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Mon Mar 10 15:29:26 CST 2025
         */
        private YuntongOrderInfoExample example;

        /**
         *
         * @mbg.generated Mon Mar 10 15:29:26 CST 2025
         */
        protected Criteria(YuntongOrderInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:29:26 CST 2025
         */
        public YuntongOrderInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:29:26 CST 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:29:26 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:29:26 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Mon Mar 10 15:29:26 CST 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:29:26 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Mon Mar 10 15:29:26 CST 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Mon Mar 10 15:29:26 CST 2025
         */
        void example(com.chinamobile.yuntong.pojo.entity.YuntongOrderInfoExample example);
    }
}