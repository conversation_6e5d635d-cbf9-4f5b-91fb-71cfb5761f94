package com.chinamobile.yuntong.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * created by l<PERSON><PERSON><PERSON> on 2025/1/10 09:50
 */
@Configuration
@ConfigurationProperties(prefix = "yuntong")
@Data
public class YuntongConfig {

    //用户设备套餐关联关系接口
    private String userDevicePackageUrl;

    //电商渠道套餐同步接口
    private String mallPackageSyncUrl;

    //套餐失效接口
    private String packageInvalidUrl;

}
