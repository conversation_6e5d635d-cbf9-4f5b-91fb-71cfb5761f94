package com.chinamobile.yuntong.util;

import com.chinamobile.yuntong.exception.ServicePowerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class YuntongSignUtils {
    private static String secretKey;

    @Value("${supply.sign.secret_key}")
    public void setSecretKey(String value) {
        secretKey = value;
    }

    public static String getSign(String paramStr) {
        log.debug("SignUtils secretKey:{}", secretKey);
        if (StringUtils.isEmpty(paramStr)) {
            return DigestUtils.md5DigestAsHex(secretKey.getBytes(StandardCharsets.UTF_8));
        } else {
            //md5 加密
            log.info("md5加密:{}", DigestUtils.md5DigestAsHex((paramStr + secretKey).getBytes(StandardCharsets.UTF_8)));
            return DigestUtils.md5DigestAsHex((paramStr + secretKey).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 校验签名
     *
     * @param paramStr
     * @param reqSign
     */
    public static void checkSign(String paramStr, String reqSign) throws ServicePowerException {
        if (org.apache.commons.lang3.StringUtils.isBlank(paramStr)) {
            throw new ServicePowerException(500, "input输入信息不能为空");
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(reqSign)) {
            throw new ServicePowerException(500, "sign签名信息不能为空");
        }
        //根据内容获得 鉴权签名
        String sign = YuntongSignUtils.getSign(paramStr);
        if (!sign.equals(reqSign)) {
            throw new ServicePowerException(500, "签名检验失败");
        }
        //md5 加密校验通过
    }

    public static String md532bit(String plainText) {
        String re_md5;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(plainText.getBytes());
            byte b[] = md.digest();

            int i;
            StringBuffer buf = new StringBuffer();
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0) {
                    i += 256;
                }
                if (i < 16) {
                    buf.append("0");
                }
                buf.append(Integer.toHexString(i));
            }
            re_md5 = buf.toString();
        } catch (Exception e) {
            log.error("对字符串：{}进行MD532bit加密时发生异常：{}", plainText, e.getMessage(), e);
            return null;
        }
        return re_md5;
    }

    // 生成HMAC-SHA256签名
    public static String hmacSha256(String secretKey, String message) {
        try {
            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes("UTF-8"), "HmacSHA256");
            hmacSha256.init(secretKeySpec);
            byte[] hash = hmacSha256.doFinal(message.getBytes("UTF-8"));
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            throw new RuntimeException("HMAC签名生成失败", e);
        }
    }
    public static void main(String[] args) {
//        String signed = getSignTest("{\"orderNum\":\"jkiot40100000001936041\",\"optionType\":\"1\"}", "b244ff421031fde652f9bb66d1486576");
////        String signed = getSignTest("{\\\"linkType\\\":\\\"1\\\",\\\"loginRole\\\":\\\"2\\\",\\\"userInfo\\\":[{\\\"userCode\\\":\\\"FXY25855\\\",\\\"userPhone\\\":\\\"MmYyYWU1Njc0ODMxMTAwODE0YjI0MmU4ZmE1MGQzMDE=\\\",\\\"userProvince\\\":\\\"531\\\",\\\"userCity\\\":\\\"5310\\\",\\\"userID\\\":\\\"1661947044770903038\\\"}]}", "E4FE7FE78FE2933D");
//        System.out.println(signed);

//        String signed = getSignTest("{\"orderNum\":\"jkiot30100000000341017\",\"type\":\"3\"}", "b244ff421031fde652f9bb66d1486576");
       String signed = md532bit("{\"orderNumber\":\"1111111111111\",\"invoiceUrl\":\"http://xx.xx\",\"invoicingTime\":\"1741227209338\"}");

       System.out.println(signed);



    }

    public static String getSignTest(String paramStr, String secretKey) {
//        log.info("SignUtils secretKey:{}", secretKey);
        if (StringUtils.isEmpty(paramStr)) {
            return DigestUtils.md5DigestAsHex(secretKey.getBytes(StandardCharsets.UTF_8));
        } else {
            //md5 加密
            log.info("md5加密:{}", DigestUtils.md5DigestAsHex((paramStr + secretKey).getBytes(StandardCharsets.UTF_8)));
            return DigestUtils.md5DigestAsHex((paramStr + secretKey).getBytes(StandardCharsets.UTF_8));
        }
    }

}
