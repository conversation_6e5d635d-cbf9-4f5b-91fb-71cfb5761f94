<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.install.dao.AfterMarketOrder2cOfferingInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.install.pojo.entity.AfterMarketOrder2cOfferingInfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="service_order_id" jdbcType="VARCHAR" property="serviceOrderId" />
    <result column="after_market_type" jdbcType="INTEGER" property="afterMarketType" />
    <result column="after_market_code" jdbcType="VARCHAR" property="afterMarketCode" />
    <result column="quantity" jdbcType="BIGINT" property="quantity" />
    <result column="present_send_order_name" jdbcType="VARCHAR" property="presentSendOrderName" />
    <result column="present_send_order_phone" jdbcType="VARCHAR" property="presentSendOrderPhone" />
    <result column="send_order_company" jdbcType="VARCHAR" property="sendOrderCompany" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="after_market_name" jdbcType="VARCHAR" property="afterMarketName" />
    <result column="after_market_settle_price" jdbcType="VARCHAR" property="afterMarketSettlePrice" />
    <result column="order_take_type" jdbcType="INTEGER" property="orderTakeType" />
    <result column="admin_cooperator_id" jdbcType="VARCHAR" property="adminCooperatorId" />
    <result column="install_manager_id" jdbcType="VARCHAR" property="installManagerId" />
    <result column="province_install_platform" jdbcType="VARCHAR" property="provinceInstallPlatform" />
    <result column="send_order_time" jdbcType="TIMESTAMP" property="sendOrderTime" />
    <result column="deliver_time" jdbcType="TIMESTAMP" property="deliverTime" />
    <result column="sku_offering_code" jdbcType="VARCHAR" property="skuOfferingCode" />
    <result column="atom_offering_code" jdbcType="VARCHAR" property="atomOfferingCode" />
    <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode" />
    <result column="spu_offering_class" jdbcType="VARCHAR" property="spuOfferingClass" />
    <result column="sku_offering_name" jdbcType="VARCHAR" property="skuOfferingName" />
    <result column="atom_offering_name" jdbcType="VARCHAR" property="atomOfferingName" />
    <result column="admin_cooperator_name" jdbcType="VARCHAR" property="adminCooperatorName" />
    <result column="deliver_failed_msg" jdbcType="VARCHAR" property="deliverFailedMsg" />
    <result column="after_market_version" jdbcType="VARCHAR" property="afterMarketVersion" />
    <result column="spu_offering_version" jdbcType="VARCHAR" property="spuOfferingVersion" />
    <result column="sku_offering_version" jdbcType="VARCHAR" property="skuOfferingVersion" />
    <result column="atom_offering_version" jdbcType="VARCHAR" property="atomOfferingVersion" />
    <result column="check_in_time" jdbcType="TIMESTAMP" property="checkInTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, service_order_id, after_market_type, after_market_code, quantity, present_send_order_name, 
    present_send_order_phone, send_order_company, user_id, create_time, update_time, 
    after_market_name, after_market_settle_price, order_take_type, admin_cooperator_id, 
    install_manager_id, province_install_platform, send_order_time, deliver_time, sku_offering_code, 
    atom_offering_code, spu_offering_code, spu_offering_class, sku_offering_name, atom_offering_name, 
    admin_cooperator_name, deliver_failed_msg, after_market_version, spu_offering_version, 
    sku_offering_version, atom_offering_version, check_in_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.install.pojo.entity.AfterMarketOrder2cOfferingInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from after_market_order_2c_offering_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from after_market_order_2c_offering_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from after_market_order_2c_offering_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.install.pojo.entity.AfterMarketOrder2cOfferingInfoExample">
    delete from after_market_order_2c_offering_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.install.pojo.entity.AfterMarketOrder2cOfferingInfo">
    insert into after_market_order_2c_offering_info (id, service_order_id, after_market_type, 
      after_market_code, quantity, present_send_order_name, 
      present_send_order_phone, send_order_company, 
      user_id, create_time, update_time, 
      after_market_name, after_market_settle_price, 
      order_take_type, admin_cooperator_id, install_manager_id, 
      province_install_platform, send_order_time, 
      deliver_time, sku_offering_code, atom_offering_code, 
      spu_offering_code, spu_offering_class, sku_offering_name, 
      atom_offering_name, admin_cooperator_name, 
      deliver_failed_msg, after_market_version, spu_offering_version, 
      sku_offering_version, atom_offering_version, 
      check_in_time)
    values (#{id,jdbcType=VARCHAR}, #{serviceOrderId,jdbcType=VARCHAR}, #{afterMarketType,jdbcType=INTEGER}, 
      #{afterMarketCode,jdbcType=VARCHAR}, #{quantity,jdbcType=BIGINT}, #{presentSendOrderName,jdbcType=VARCHAR}, 
      #{presentSendOrderPhone,jdbcType=VARCHAR}, #{sendOrderCompany,jdbcType=VARCHAR}, 
      #{userId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{afterMarketName,jdbcType=VARCHAR}, #{afterMarketSettlePrice,jdbcType=VARCHAR}, 
      #{orderTakeType,jdbcType=INTEGER}, #{adminCooperatorId,jdbcType=VARCHAR}, #{installManagerId,jdbcType=VARCHAR}, 
      #{provinceInstallPlatform,jdbcType=VARCHAR}, #{sendOrderTime,jdbcType=TIMESTAMP}, 
      #{deliverTime,jdbcType=TIMESTAMP}, #{skuOfferingCode,jdbcType=VARCHAR}, #{atomOfferingCode,jdbcType=VARCHAR}, 
      #{spuOfferingCode,jdbcType=VARCHAR}, #{spuOfferingClass,jdbcType=VARCHAR}, #{skuOfferingName,jdbcType=VARCHAR}, 
      #{atomOfferingName,jdbcType=VARCHAR}, #{adminCooperatorName,jdbcType=VARCHAR}, 
      #{deliverFailedMsg,jdbcType=VARCHAR}, #{afterMarketVersion,jdbcType=VARCHAR}, #{spuOfferingVersion,jdbcType=VARCHAR}, 
      #{skuOfferingVersion,jdbcType=VARCHAR}, #{atomOfferingVersion,jdbcType=VARCHAR}, 
      #{checkInTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.install.pojo.entity.AfterMarketOrder2cOfferingInfo">
    insert into after_market_order_2c_offering_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="serviceOrderId != null">
        service_order_id,
      </if>
      <if test="afterMarketType != null">
        after_market_type,
      </if>
      <if test="afterMarketCode != null">
        after_market_code,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="presentSendOrderName != null">
        present_send_order_name,
      </if>
      <if test="presentSendOrderPhone != null">
        present_send_order_phone,
      </if>
      <if test="sendOrderCompany != null">
        send_order_company,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="afterMarketName != null">
        after_market_name,
      </if>
      <if test="afterMarketSettlePrice != null">
        after_market_settle_price,
      </if>
      <if test="orderTakeType != null">
        order_take_type,
      </if>
      <if test="adminCooperatorId != null">
        admin_cooperator_id,
      </if>
      <if test="installManagerId != null">
        install_manager_id,
      </if>
      <if test="provinceInstallPlatform != null">
        province_install_platform,
      </if>
      <if test="sendOrderTime != null">
        send_order_time,
      </if>
      <if test="deliverTime != null">
        deliver_time,
      </if>
      <if test="skuOfferingCode != null">
        sku_offering_code,
      </if>
      <if test="atomOfferingCode != null">
        atom_offering_code,
      </if>
      <if test="spuOfferingCode != null">
        spu_offering_code,
      </if>
      <if test="spuOfferingClass != null">
        spu_offering_class,
      </if>
      <if test="skuOfferingName != null">
        sku_offering_name,
      </if>
      <if test="atomOfferingName != null">
        atom_offering_name,
      </if>
      <if test="adminCooperatorName != null">
        admin_cooperator_name,
      </if>
      <if test="deliverFailedMsg != null">
        deliver_failed_msg,
      </if>
      <if test="afterMarketVersion != null">
        after_market_version,
      </if>
      <if test="spuOfferingVersion != null">
        spu_offering_version,
      </if>
      <if test="skuOfferingVersion != null">
        sku_offering_version,
      </if>
      <if test="atomOfferingVersion != null">
        atom_offering_version,
      </if>
      <if test="checkInTime != null">
        check_in_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="serviceOrderId != null">
        #{serviceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketType != null">
        #{afterMarketType,jdbcType=INTEGER},
      </if>
      <if test="afterMarketCode != null">
        #{afterMarketCode,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=BIGINT},
      </if>
      <if test="presentSendOrderName != null">
        #{presentSendOrderName,jdbcType=VARCHAR},
      </if>
      <if test="presentSendOrderPhone != null">
        #{presentSendOrderPhone,jdbcType=VARCHAR},
      </if>
      <if test="sendOrderCompany != null">
        #{sendOrderCompany,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="afterMarketName != null">
        #{afterMarketName,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketSettlePrice != null">
        #{afterMarketSettlePrice,jdbcType=VARCHAR},
      </if>
      <if test="orderTakeType != null">
        #{orderTakeType,jdbcType=INTEGER},
      </if>
      <if test="adminCooperatorId != null">
        #{adminCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="installManagerId != null">
        #{installManagerId,jdbcType=VARCHAR},
      </if>
      <if test="provinceInstallPlatform != null">
        #{provinceInstallPlatform,jdbcType=VARCHAR},
      </if>
      <if test="sendOrderTime != null">
        #{sendOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliverTime != null">
        #{deliverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="skuOfferingCode != null">
        #{skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingCode != null">
        #{atomOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingCode != null">
        #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingClass != null">
        #{spuOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingName != null">
        #{skuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingName != null">
        #{atomOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="adminCooperatorName != null">
        #{adminCooperatorName,jdbcType=VARCHAR},
      </if>
      <if test="deliverFailedMsg != null">
        #{deliverFailedMsg,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketVersion != null">
        #{afterMarketVersion,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingVersion != null">
        #{spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingVersion != null">
        #{skuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingVersion != null">
        #{atomOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="checkInTime != null">
        #{checkInTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.install.pojo.entity.AfterMarketOrder2cOfferingInfoExample" resultType="java.lang.Long">
    select count(*) from after_market_order_2c_offering_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update after_market_order_2c_offering_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceOrderId != null">
        service_order_id = #{record.serviceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.afterMarketType != null">
        after_market_type = #{record.afterMarketType,jdbcType=INTEGER},
      </if>
      <if test="record.afterMarketCode != null">
        after_market_code = #{record.afterMarketCode,jdbcType=VARCHAR},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=BIGINT},
      </if>
      <if test="record.presentSendOrderName != null">
        present_send_order_name = #{record.presentSendOrderName,jdbcType=VARCHAR},
      </if>
      <if test="record.presentSendOrderPhone != null">
        present_send_order_phone = #{record.presentSendOrderPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.sendOrderCompany != null">
        send_order_company = #{record.sendOrderCompany,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.afterMarketName != null">
        after_market_name = #{record.afterMarketName,jdbcType=VARCHAR},
      </if>
      <if test="record.afterMarketSettlePrice != null">
        after_market_settle_price = #{record.afterMarketSettlePrice,jdbcType=VARCHAR},
      </if>
      <if test="record.orderTakeType != null">
        order_take_type = #{record.orderTakeType,jdbcType=INTEGER},
      </if>
      <if test="record.adminCooperatorId != null">
        admin_cooperator_id = #{record.adminCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.installManagerId != null">
        install_manager_id = #{record.installManagerId,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceInstallPlatform != null">
        province_install_platform = #{record.provinceInstallPlatform,jdbcType=VARCHAR},
      </if>
      <if test="record.sendOrderTime != null">
        send_order_time = #{record.sendOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deliverTime != null">
        deliver_time = #{record.deliverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.skuOfferingCode != null">
        sku_offering_code = #{record.skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOfferingCode != null">
        atom_offering_code = #{record.atomOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingCode != null">
        spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingClass != null">
        spu_offering_class = #{record.spuOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="record.skuOfferingName != null">
        sku_offering_name = #{record.skuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOfferingName != null">
        atom_offering_name = #{record.atomOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="record.adminCooperatorName != null">
        admin_cooperator_name = #{record.adminCooperatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.deliverFailedMsg != null">
        deliver_failed_msg = #{record.deliverFailedMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.afterMarketVersion != null">
        after_market_version = #{record.afterMarketVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingVersion != null">
        spu_offering_version = #{record.spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.skuOfferingVersion != null">
        sku_offering_version = #{record.skuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOfferingVersion != null">
        atom_offering_version = #{record.atomOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.checkInTime != null">
        check_in_time = #{record.checkInTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update after_market_order_2c_offering_info
    set id = #{record.id,jdbcType=VARCHAR},
      service_order_id = #{record.serviceOrderId,jdbcType=VARCHAR},
      after_market_type = #{record.afterMarketType,jdbcType=INTEGER},
      after_market_code = #{record.afterMarketCode,jdbcType=VARCHAR},
      quantity = #{record.quantity,jdbcType=BIGINT},
      present_send_order_name = #{record.presentSendOrderName,jdbcType=VARCHAR},
      present_send_order_phone = #{record.presentSendOrderPhone,jdbcType=VARCHAR},
      send_order_company = #{record.sendOrderCompany,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      after_market_name = #{record.afterMarketName,jdbcType=VARCHAR},
      after_market_settle_price = #{record.afterMarketSettlePrice,jdbcType=VARCHAR},
      order_take_type = #{record.orderTakeType,jdbcType=INTEGER},
      admin_cooperator_id = #{record.adminCooperatorId,jdbcType=VARCHAR},
      install_manager_id = #{record.installManagerId,jdbcType=VARCHAR},
      province_install_platform = #{record.provinceInstallPlatform,jdbcType=VARCHAR},
      send_order_time = #{record.sendOrderTime,jdbcType=TIMESTAMP},
      deliver_time = #{record.deliverTime,jdbcType=TIMESTAMP},
      sku_offering_code = #{record.skuOfferingCode,jdbcType=VARCHAR},
      atom_offering_code = #{record.atomOfferingCode,jdbcType=VARCHAR},
      spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      spu_offering_class = #{record.spuOfferingClass,jdbcType=VARCHAR},
      sku_offering_name = #{record.skuOfferingName,jdbcType=VARCHAR},
      atom_offering_name = #{record.atomOfferingName,jdbcType=VARCHAR},
      admin_cooperator_name = #{record.adminCooperatorName,jdbcType=VARCHAR},
      deliver_failed_msg = #{record.deliverFailedMsg,jdbcType=VARCHAR},
      after_market_version = #{record.afterMarketVersion,jdbcType=VARCHAR},
      spu_offering_version = #{record.spuOfferingVersion,jdbcType=VARCHAR},
      sku_offering_version = #{record.skuOfferingVersion,jdbcType=VARCHAR},
      atom_offering_version = #{record.atomOfferingVersion,jdbcType=VARCHAR},
      check_in_time = #{record.checkInTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.install.pojo.entity.AfterMarketOrder2cOfferingInfo">
    update after_market_order_2c_offering_info
    <set>
      <if test="serviceOrderId != null">
        service_order_id = #{serviceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketType != null">
        after_market_type = #{afterMarketType,jdbcType=INTEGER},
      </if>
      <if test="afterMarketCode != null">
        after_market_code = #{afterMarketCode,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=BIGINT},
      </if>
      <if test="presentSendOrderName != null">
        present_send_order_name = #{presentSendOrderName,jdbcType=VARCHAR},
      </if>
      <if test="presentSendOrderPhone != null">
        present_send_order_phone = #{presentSendOrderPhone,jdbcType=VARCHAR},
      </if>
      <if test="sendOrderCompany != null">
        send_order_company = #{sendOrderCompany,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="afterMarketName != null">
        after_market_name = #{afterMarketName,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketSettlePrice != null">
        after_market_settle_price = #{afterMarketSettlePrice,jdbcType=VARCHAR},
      </if>
      <if test="orderTakeType != null">
        order_take_type = #{orderTakeType,jdbcType=INTEGER},
      </if>
      <if test="adminCooperatorId != null">
        admin_cooperator_id = #{adminCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="installManagerId != null">
        install_manager_id = #{installManagerId,jdbcType=VARCHAR},
      </if>
      <if test="provinceInstallPlatform != null">
        province_install_platform = #{provinceInstallPlatform,jdbcType=VARCHAR},
      </if>
      <if test="sendOrderTime != null">
        send_order_time = #{sendOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliverTime != null">
        deliver_time = #{deliverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="skuOfferingCode != null">
        sku_offering_code = #{skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingCode != null">
        atom_offering_code = #{atomOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingCode != null">
        spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingClass != null">
        spu_offering_class = #{spuOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingName != null">
        sku_offering_name = #{skuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingName != null">
        atom_offering_name = #{atomOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="adminCooperatorName != null">
        admin_cooperator_name = #{adminCooperatorName,jdbcType=VARCHAR},
      </if>
      <if test="deliverFailedMsg != null">
        deliver_failed_msg = #{deliverFailedMsg,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketVersion != null">
        after_market_version = #{afterMarketVersion,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingVersion != null">
        spu_offering_version = #{spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingVersion != null">
        sku_offering_version = #{skuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingVersion != null">
        atom_offering_version = #{atomOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="checkInTime != null">
        check_in_time = #{checkInTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.install.pojo.entity.AfterMarketOrder2cOfferingInfo">
    update after_market_order_2c_offering_info
    set service_order_id = #{serviceOrderId,jdbcType=VARCHAR},
      after_market_type = #{afterMarketType,jdbcType=INTEGER},
      after_market_code = #{afterMarketCode,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=BIGINT},
      present_send_order_name = #{presentSendOrderName,jdbcType=VARCHAR},
      present_send_order_phone = #{presentSendOrderPhone,jdbcType=VARCHAR},
      send_order_company = #{sendOrderCompany,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      after_market_name = #{afterMarketName,jdbcType=VARCHAR},
      after_market_settle_price = #{afterMarketSettlePrice,jdbcType=VARCHAR},
      order_take_type = #{orderTakeType,jdbcType=INTEGER},
      admin_cooperator_id = #{adminCooperatorId,jdbcType=VARCHAR},
      install_manager_id = #{installManagerId,jdbcType=VARCHAR},
      province_install_platform = #{provinceInstallPlatform,jdbcType=VARCHAR},
      send_order_time = #{sendOrderTime,jdbcType=TIMESTAMP},
      deliver_time = #{deliverTime,jdbcType=TIMESTAMP},
      sku_offering_code = #{skuOfferingCode,jdbcType=VARCHAR},
      atom_offering_code = #{atomOfferingCode,jdbcType=VARCHAR},
      spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      spu_offering_class = #{spuOfferingClass,jdbcType=VARCHAR},
      sku_offering_name = #{skuOfferingName,jdbcType=VARCHAR},
      atom_offering_name = #{atomOfferingName,jdbcType=VARCHAR},
      admin_cooperator_name = #{adminCooperatorName,jdbcType=VARCHAR},
      deliver_failed_msg = #{deliverFailedMsg,jdbcType=VARCHAR},
      after_market_version = #{afterMarketVersion,jdbcType=VARCHAR},
      spu_offering_version = #{spuOfferingVersion,jdbcType=VARCHAR},
      sku_offering_version = #{skuOfferingVersion,jdbcType=VARCHAR},
      atom_offering_version = #{atomOfferingVersion,jdbcType=VARCHAR},
      check_in_time = #{checkInTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into after_market_order_2c_offering_info
    (id, service_order_id, after_market_type, after_market_code, quantity, present_send_order_name, 
      present_send_order_phone, send_order_company, user_id, create_time, update_time, 
      after_market_name, after_market_settle_price, order_take_type, admin_cooperator_id, 
      install_manager_id, province_install_platform, send_order_time, deliver_time, sku_offering_code, 
      atom_offering_code, spu_offering_code, spu_offering_class, sku_offering_name, atom_offering_name, 
      admin_cooperator_name, deliver_failed_msg, after_market_version, spu_offering_version, 
      sku_offering_version, atom_offering_version, check_in_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.serviceOrderId,jdbcType=VARCHAR}, #{item.afterMarketType,jdbcType=INTEGER}, 
        #{item.afterMarketCode,jdbcType=VARCHAR}, #{item.quantity,jdbcType=BIGINT}, #{item.presentSendOrderName,jdbcType=VARCHAR}, 
        #{item.presentSendOrderPhone,jdbcType=VARCHAR}, #{item.sendOrderCompany,jdbcType=VARCHAR}, 
        #{item.userId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.afterMarketName,jdbcType=VARCHAR}, #{item.afterMarketSettlePrice,jdbcType=VARCHAR}, 
        #{item.orderTakeType,jdbcType=INTEGER}, #{item.adminCooperatorId,jdbcType=VARCHAR}, 
        #{item.installManagerId,jdbcType=VARCHAR}, #{item.provinceInstallPlatform,jdbcType=VARCHAR}, 
        #{item.sendOrderTime,jdbcType=TIMESTAMP}, #{item.deliverTime,jdbcType=TIMESTAMP}, 
        #{item.skuOfferingCode,jdbcType=VARCHAR}, #{item.atomOfferingCode,jdbcType=VARCHAR}, 
        #{item.spuOfferingCode,jdbcType=VARCHAR}, #{item.spuOfferingClass,jdbcType=VARCHAR}, 
        #{item.skuOfferingName,jdbcType=VARCHAR}, #{item.atomOfferingName,jdbcType=VARCHAR}, 
        #{item.adminCooperatorName,jdbcType=VARCHAR}, #{item.deliverFailedMsg,jdbcType=VARCHAR}, 
        #{item.afterMarketVersion,jdbcType=VARCHAR}, #{item.spuOfferingVersion,jdbcType=VARCHAR}, 
        #{item.skuOfferingVersion,jdbcType=VARCHAR}, #{item.atomOfferingVersion,jdbcType=VARCHAR}, 
        #{item.checkInTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into after_market_order_2c_offering_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'service_order_id'.toString() == column.value">
          #{item.serviceOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'after_market_type'.toString() == column.value">
          #{item.afterMarketType,jdbcType=INTEGER}
        </if>
        <if test="'after_market_code'.toString() == column.value">
          #{item.afterMarketCode,jdbcType=VARCHAR}
        </if>
        <if test="'quantity'.toString() == column.value">
          #{item.quantity,jdbcType=BIGINT}
        </if>
        <if test="'present_send_order_name'.toString() == column.value">
          #{item.presentSendOrderName,jdbcType=VARCHAR}
        </if>
        <if test="'present_send_order_phone'.toString() == column.value">
          #{item.presentSendOrderPhone,jdbcType=VARCHAR}
        </if>
        <if test="'send_order_company'.toString() == column.value">
          #{item.sendOrderCompany,jdbcType=VARCHAR}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'after_market_name'.toString() == column.value">
          #{item.afterMarketName,jdbcType=VARCHAR}
        </if>
        <if test="'after_market_settle_price'.toString() == column.value">
          #{item.afterMarketSettlePrice,jdbcType=VARCHAR}
        </if>
        <if test="'order_take_type'.toString() == column.value">
          #{item.orderTakeType,jdbcType=INTEGER}
        </if>
        <if test="'admin_cooperator_id'.toString() == column.value">
          #{item.adminCooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'install_manager_id'.toString() == column.value">
          #{item.installManagerId,jdbcType=VARCHAR}
        </if>
        <if test="'province_install_platform'.toString() == column.value">
          #{item.provinceInstallPlatform,jdbcType=VARCHAR}
        </if>
        <if test="'send_order_time'.toString() == column.value">
          #{item.sendOrderTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'deliver_time'.toString() == column.value">
          #{item.deliverTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'sku_offering_code'.toString() == column.value">
          #{item.skuOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'atom_offering_code'.toString() == column.value">
          #{item.atomOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_code'.toString() == column.value">
          #{item.spuOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_class'.toString() == column.value">
          #{item.spuOfferingClass,jdbcType=VARCHAR}
        </if>
        <if test="'sku_offering_name'.toString() == column.value">
          #{item.skuOfferingName,jdbcType=VARCHAR}
        </if>
        <if test="'atom_offering_name'.toString() == column.value">
          #{item.atomOfferingName,jdbcType=VARCHAR}
        </if>
        <if test="'admin_cooperator_name'.toString() == column.value">
          #{item.adminCooperatorName,jdbcType=VARCHAR}
        </if>
        <if test="'deliver_failed_msg'.toString() == column.value">
          #{item.deliverFailedMsg,jdbcType=VARCHAR}
        </if>
        <if test="'after_market_version'.toString() == column.value">
          #{item.afterMarketVersion,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_version'.toString() == column.value">
          #{item.spuOfferingVersion,jdbcType=VARCHAR}
        </if>
        <if test="'sku_offering_version'.toString() == column.value">
          #{item.skuOfferingVersion,jdbcType=VARCHAR}
        </if>
        <if test="'atom_offering_version'.toString() == column.value">
          #{item.atomOfferingVersion,jdbcType=VARCHAR}
        </if>
        <if test="'check_in_time'.toString() == column.value">
          #{item.checkInTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>