#spring:
#  cloud:
#    nacos:
#      discovery:
#        namespace: test
#        server-addr: **********:8848
#  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    hikari:
#      idle-timeout: 180000
#      max-lifetime: 30000
#      maximum-pool-size: 8
#      minimum-idle: 4
#    password: app_!QAZxsw2
#    url: ************************************************************************************************************************************
#    username: supply
#  jackson:
#    date-format: yyyy-MM-dd HH:mm:ss
#    time-zone: GMT+8
#  redis:
#    cluster:
#      nodes: **********:6381,**********:6381,**********:6381
#    password: app_!QAZxsw2
#    pool:
#      max-active: 5
#      max-idle: 5
#      max-wait: -1
#      min-idle: 0
#    timeout: 10000
#  servlet:
#    multipart:
#      max-file-size: 10MB
#      max-request-size: 10MB
##oneNET 对象存储
#onenet-storage:
#  queryHttpInner: http://*************:9092/mallos/oss/ #访问代理地址
#  queryHttpOuter: http://**********/mallos/oss/
#  endpoint: http://s3-qos.iot-st-armtest.qiniu-solutions.com
#  bucketName: mallos-test
#  accessKey: W1aowUWsredwHbsuCeLUbI_wXI8_eNJtSWelhbxD
#  secretKey: zht2qc8vrIdCrL50PB5EdFrZSNAApdlOxQ7wZIFD
## 装维相关
#zhuangwei:
#  methodCode:
#    # 认证
#    authentication: OSP_IOT_GET_TOKEN
#    # 派发业务开通
#    openCreate: OSP_IOT_OPEN_CREATE
#  url:
#    henanOpenUrl: http://**********/oppf
#
##sms 发送短信templateId
#sms:
#  AfterMarketOrderAppointmentTemplateId: 106381
#  AfterMarketOrderDispatchTemplateId: 106382
#  AfterMarketOrderTerminationTemplateId: 106383
#h5:
#  loginAddress: http://**********:31330/mallos/orderProcess/login
#install:
#  encodeKey: 3D88F1C1AAE7
#  secretKey: 82E4FE7FE78FE293
#  sm4Key: iotmalltoos24122
#  sm4Iv: iotmalltoos24122
#  synOrderRefundResultUrl: https://************:8601/apiaccess/os/v1/orderservice/refundResult
#  serviceOrderResultUrl: https://************:8601/apiaccess/os/v1/orderservice/serviceOrderResult
#  # 加密
#supply:
#  des:
#    key: 3D88F1C1AAE7 #用于敏感信息加密
#  sign:
#    secret_key: b244ff421031fde652f9bb66d1486576 # md5用于计算sign
#feign:
#  client:
#    config:
#      default:
#        connectTimeout: 10000 #单位毫秒
#        readTimeout: 10000 #单位毫秒
#        loggerLevel: FULL
#    url:
#      # 物料系统接口地址
#      # materialSystemUrl: http://*********** #正式环境
#      materialSystemUrl: http://**********
#      # 统一用户管理平台接口地址
#      ldapManagerUrl: http://**********:8080
#      qlyOpenUrl: http://**********