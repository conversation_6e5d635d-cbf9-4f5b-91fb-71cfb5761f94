package com.chinamobile.install.config;

/**
 * @Author: YSC
 * @Date: 2021/12/16 11:34
 * @Description:
 */
public class RedisLockConstant {
    public static final String ORDER_LOCK = "SC:ORDER_LOCK:";

    public static final String RENEWAL_ORDER_LOCK = "SC:ORDER_LOCK:RENEWAL:";


    /**
     * 产品引入redis 常量key
     */
    public static final String PRODUCT_APPLY_SERIAL ="SC:PRODUCT_SERIAL";

    /**
     * 新产品上架
     */
    public static final String NEW_PRODUCT_ONLINE_SERIAL ="SC:NEW_PRODUCT_ONLINE_SERIAL";

    /**
     * 新产品上下架状态的临时保存
     */
    public static final String NEW_PRODUCT_ONLINE_STATUS ="SC:NEW_PRODUCT_ONLINE_SERIAL";

    public static final String AFTER_ORDER_DISPATCH_INSTALL_REDIS_KEY ="SC:AFTER_ORDER_DISPATCH_AFFIRM_CODE";

    public static final String MALL_SYNC_LOCK = "SC:MALL_SYNC_LOCK:";

    public static final String TRADE_NO_KEY ="SC:TRADE_NO";
    public static final String PRODUCT_FLOW_SHELF_LOCK = "SC:PRODUCT_FLOW_SHELF_LOCK";
    public static final String PRODUCT_NAVIGATION_DIRECTORY_LOCK = "SC:PRODUCT_NAVIGATION_DIRECTORY_LOCK";
}
