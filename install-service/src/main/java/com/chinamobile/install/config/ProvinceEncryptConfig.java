package com.chinamobile.install.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 省份加密密钥配置类
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2024/01/15
 * @description 管理不同省份的加密密钥配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "zhuangwei.des")
public class ProvinceEncryptConfig {

    /**
     * 各省份的加密密钥配置
     * key: 省份代码（如：henan, shandong）
     * value: 对应的加密密钥
     */
    private Map<String, String> key;

    /**
     * 获取指定省份的加密密钥
     *
     * @param province 省份代码
     * @return 加密密钥，如果省份不存在则返回河南的密钥作为默认值
     */
    public String getEncryptKey(String province) {
        if (key == null || key.isEmpty()) {
            throw new IllegalStateException("省份加密密钥配置未初始化");
        }
        
        // 如果指定省份存在，返回对应密钥
        if (key.containsKey(province)) {
            return key.get(province);
        }
        
        // 如果指定省份不存在，返回河南密钥作为默认值
        if (key.containsKey("henan")) {
            return key.get("henan");
        }
        
        // 如果河南密钥也不存在，抛出异常
        throw new IllegalStateException("未找到省份 " + province + " 的加密密钥配置，且默认河南密钥也不存在");
    }


}
