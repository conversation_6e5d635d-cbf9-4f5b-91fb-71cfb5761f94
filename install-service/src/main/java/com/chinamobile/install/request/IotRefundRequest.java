package com.chinamobile.install.request;

import lombok.Data;

import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/12/16 14:18
 * @Description:
 */
@Data
public class IotRefundRequest {
    /**
     * 退款请求流水号
     * 本次发起退货退款请求的流水号
     */
    private String refundOrderId;
    /**
     * 业务订单流水号
     * 销售业务订单的唯一标识
     */
    private String orderId;
    /**
     * 操作类型
     * 1：仅退款
     * 2：退货退款
     * 3：换货（暂不启用）
     */
    private String refundsType;
    /**
     * 审核结果
     * 1：通过
     * 2：不通过
     */
    private String auditResult;
    /**
     * 审核原因
     * 当auditResult=2时必填
     * 当auditResult=1时可为空
     */
    private String auditResultReason;
    /**
     * 收货人信息
     * 当refundsType=2且auditResult=1时，必填
     */
    private List<ContactInfoDTO> contactInfo;

    @Data
    public static class ContactInfoDTO {
        /**
         * 收货人姓名
         * 合作伙伴的信息
         */
        private String contactPersonName;
        /**
         * 收货人手机号
         * 合作伙伴的联系方式
         */
        private String contactPhone;
        /**
         * 收货地址
         * 合作伙伴的收货地址
         */
        private String addresstInfo;
    }
}
