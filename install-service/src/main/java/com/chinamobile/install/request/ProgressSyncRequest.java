package com.chinamobile.install.request;

import com.chinamobile.install.validator.ValidProgressSync;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 单进度接口（透明化）请求参数
 * 用于省侧装维业务平台向商城OS系统同步工单处理进度信息
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@ValidProgressSync
public class ProgressSyncRequest {

    /**
     * IOT商城订单编号
     */
    @NotBlank(message = "IOT商城订单编号不能为空")
    @Size(max = 50, message = "IOT商城订单编号长度不能超过50")
    private String orderNo;

    /**
     * 省侧装维业务工单编号
     */
    @NotBlank(message = "省侧装维业务工单编号不能为空")
    @Size(max = 50, message = "省侧装维业务工单编号长度不能超过50")
    private String serialNo;

    /**
     * 产品名称
     * 例如：qly：千里眼（后续可能新增）
     */
    @NotBlank(message = "产品名称不能为空")
    @Size(max = 50, message = "产品名称长度不能超过50")
    private String productName;

    /**
     * 流程名称
     * 枚举: A01：业务开通流程
     */
    @NotBlank(message = "流程名称不能为空")
    @Size(max = 50, message = "流程名称长度不能超过50")
    private String flowName;

    /**
     * 工单状态
     * 枚举：S01：运行中；S02：已归档；
     */
    @NotBlank(message = "工单状态不能为空")
    @Size(max = 50, message = "工单状态长度不能超过50")
    private String sheetState;

    /**
     * 操作动作
     * 枚举：C01:待派单；C02:已驳回；C03:已派单；C04:已签到；C05:已完结（成功）；C06:已完结（失败）；
     */
    @NotBlank(message = "操作动作不能为空")
    @Size(max = 10, message = "操作动作长度不能超过10")
    private String dealAction;

    /**
     * 驳回原因
     * 操作动作="C02"或"C06"时，必填
     */
    @Size(max = 255, message = "驳回原因长度不能超过255")
    private String resultDesc;

    /**
     * 当前环节联系人
     * 操作动作="C03"时，必填
     */
    @Size(max = 50, message = "当前环节联系人长度不能超过50")
    private String opPerson;

    /**
     * 当前环节联系人联系方式
     * 操作动作="C03"时，必填
     */
    @Size(max = 50, message = "当前环节联系人联系方式长度不能超过50")
    private String opContact;

    /**
     * 当前环节到达时间
     * 时间格式：yyyy-mm-dd hh:mm:ss
     */
    private Date arTime;

    /**
     * 当前环节完成时间
     * 时间格式：yyyy-mm-dd hh:mm:ss
     */
    private Date endTime;

    /**
     * 渠道来源
     * 字符串,省份拼音,如:"shandong"
     */
    @NotBlank(message = "渠道来源不能为空")
    @Size(max = 50, message = "渠道来源长度不能超过50")
    private String sourceChannel;
}
