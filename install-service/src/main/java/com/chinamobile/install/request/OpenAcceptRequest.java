package com.chinamobile.install.request;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/21 11:48
 * @description 河南装维工单受理请求 二级业务编排系统 -> OS
 */
@Data
public class OpenAcceptRequest {

    /**
     * 记录唯一标识
     */
    private String recordUuid;

    /**
     * IOT商城订单编号
     */
    private String orderNo;

    /**
     * 业编工单编号
     */
    private String serialNo;

    /**
     * 业务类型
     */
    private String serviceType;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 流程名称
     * 枚举:
     * A01:勘察流程、A02:变更勘察流程、A03:业务开通流程、A04:业务变更流程、A05:业务停机流程、A06:业务复机流程、A07:业务拆机流程、A08:预评估流程。
     */
    private String flowName;

    /**
     * 流程环节名称
     * 枚举：N01:定单审核; N02:外线施工;N03:装机配置; N04:开通确认;N05归档
     */
    private String linkName;

    /**
     * 工单状态
     * 枚举：S01：运行中；S02：已归档；S03：已驳回；
     */
    private String sheetState;

    /**
     * 当前环节联系人
     * 操作动作=“C13”时，可为空
     */
    private String opPerson;

    /**
     * 当前环节联系人联系方式
     * 操作动作=“C13”时，可为空
     */
    private String opContact;

    /**
     * 当前环节到达时间 时间格式：yyyy-mm-dd hh:mm:ss
     */
    private Date arTime;

    /**
     * 当前环节完成时间 操作动作=“C13、C02、C07”时，可为空；时间格式：yyyy-mm-dd hh:mm:ss
     */
    private Date endTime;

    /**
     * 环节停留时长 操作动作=“C13、C02、C07”时，可为空；时间格式：yyyy-mm-dd hh:mm:ss
     */
    private Date dealTime;

    /**
     * 环节操作时间 操作动作=“C13”时，可为空
     */
    private Date operTime;

    /**
     * 环节处理结果 默认完成
     * 操作动作=“C13、C02、C07”时，可为空
     */
    private String dealResult;

    /**
     * 操作动作
     * 枚举：C01:受理操作；C02:取消受理操作；C03:提交操作；C04:驳回上一个环节；C05:退单；C06:撤单；C07:移交；C08:已打单；C09:已完成;C10：工单异常；C11：退撤单；C12:打单中；C13:未受理；
     */
    private String dealAction;

    /**
     * 预约确认时间 日期：yyyy-mm-dd hh:mm:ss
     * 【装机配置】环节，提交时为必填
     */
    private Date PreAckTime;

    /**
     * 改约时间 日期：yyyy-mm-dd hh:mm:ss
     */
    private Date ReschedulingTime;

    /**
     * 签到时间 日期：yyyy-mm-dd hh:mm:ss 【装机配置】环节，提交时为必填
     */
    private Date SignTime;

    /**
     * 流程全部环节列表 流程环节名称逗号,分开
     */
    private String allLinkName;
}
