package com.chinamobile.install.dao;

import com.chinamobile.install.pojo.entity.MallAddress2ProvinceAddressInfo;
import com.chinamobile.install.pojo.entity.MallAddress2ProvinceAddressInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MallAddress2ProvinceAddressInfoMapper {
    /**
     *
     * @mbg.generated Tue May 27 17:43:06 CST 2025
     */
    long countByExample(MallAddress2ProvinceAddressInfoExample example);

    /**
     *
     * @mbg.generated Tue May 27 17:43:06 CST 2025
     */
    int deleteByExample(MallAddress2ProvinceAddressInfoExample example);

    /**
     *
     * @mbg.generated Tue May 27 17:43:06 CST 2025
     */
    int insert(MallAddress2ProvinceAddressInfo record);

    /**
     *
     * @mbg.generated Tue May 27 17:43:06 CST 2025
     */
    int insertSelective(MallAddress2ProvinceAddressInfo record);

    /**
     *
     * @mbg.generated Tue May 27 17:43:06 CST 2025
     */
    List<MallAddress2ProvinceAddressInfo> selectByExample(MallAddress2ProvinceAddressInfoExample example);

    /**
     *
     * @mbg.generated Tue May 27 17:43:06 CST 2025
     */
    int updateByExampleSelective(@Param("record") MallAddress2ProvinceAddressInfo record, @Param("example") MallAddress2ProvinceAddressInfoExample example);

    /**
     *
     * @mbg.generated Tue May 27 17:43:06 CST 2025
     */
    int updateByExample(@Param("record") MallAddress2ProvinceAddressInfo record, @Param("example") MallAddress2ProvinceAddressInfoExample example);

    /**
     *
     * @mbg.generated Tue May 27 17:43:06 CST 2025
     */
    int batchInsert(@Param("list") List<MallAddress2ProvinceAddressInfo> list);

    /**
     *
     * @mbg.generated Tue May 27 17:43:06 CST 2025
     */
    int batchInsertSelective(@Param("list") List<MallAddress2ProvinceAddressInfo> list, @Param("selective") MallAddress2ProvinceAddressInfo.Column ... selective);
}