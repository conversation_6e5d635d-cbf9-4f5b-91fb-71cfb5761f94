package com.chinamobile.install.service.impl;

import com.chinamobile.install.dao.OrderHenanZwHistoryMapper;
import com.chinamobile.install.enums.OrderHenanZwStatusEnum;
import com.chinamobile.install.pojo.entity.OrderHenanZwHistory;
import com.chinamobile.install.pojo.entity.OrderHenanZwHistoryExample;
import com.chinamobile.install.pojo.param.SaveSheetHistoryParam;
import com.chinamobile.install.pojo.vo.OrderHenanZwHistoryVO;
import com.chinamobile.install.service.OrderHenanZwHistoryService;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/24 14:40
 * @description TODO
 */
@Service
public class OrderHenanZwHistoryServiceImpl implements OrderHenanZwHistoryService {

    @Resource
    private OrderHenanZwHistoryMapper orderHenanZwHistoryMapper;

    @Override
    public List<OrderHenanZwHistoryVO> getSheetHistory(String sheetNo) {
        List<OrderHenanZwHistory> orderHenanZwHistories = orderHenanZwHistoryMapper.selectByExample(
            new OrderHenanZwHistoryExample().createCriteria()
                .andSheetNoEqualTo(sheetNo)
                .example()
        );
        List<OrderHenanZwHistoryVO> orderHenanZwHistoryVOS = Collections.emptyList();
        if (!CollectionUtils.isEmpty(orderHenanZwHistories)) {
            orderHenanZwHistoryVOS = orderHenanZwHistories.stream().map(orderHenanZwHistory -> {
                OrderHenanZwHistoryVO orderHenanZwHistoryVO = new OrderHenanZwHistoryVO();
                BeanUtils.copyProperties(orderHenanZwHistory, orderHenanZwHistoryVO);
                orderHenanZwHistoryVO.setStatusMessage(OrderHenanZwStatusEnum.getMessage(orderHenanZwHistory.getStatus()));
                return orderHenanZwHistoryVO;
            }).collect(Collectors.toList());
        }
        return orderHenanZwHistoryVOS;
    }

    @Override
    public void saveSheetHistory(SaveSheetHistoryParam param) {
        OrderHenanZwHistory orderHenanZwHistory = new OrderHenanZwHistory();
        BeanUtils.copyProperties(param, orderHenanZwHistory);
        orderHenanZwHistory.setId(BaseServiceUtils.getId());
        orderHenanZwHistoryMapper.insert(orderHenanZwHistory);
    }

    @Override
    public void batchSaveSheetHistories(List<SaveSheetHistoryParam> params) {
        if (!CollectionUtils.isEmpty(params)) {
            List<OrderHenanZwHistory> orderHenanZwHistories = new ArrayList<>();
            for (SaveSheetHistoryParam param : params) {
                OrderHenanZwHistory orderHenanZwHistory = new OrderHenanZwHistory();
                BeanUtils.copyProperties(param, orderHenanZwHistory);
                orderHenanZwHistory.setId(BaseServiceUtils.getId());
                orderHenanZwHistories.add(orderHenanZwHistory);
            }
            orderHenanZwHistoryMapper.batchInsert(orderHenanZwHistories);
        }
    }
}
