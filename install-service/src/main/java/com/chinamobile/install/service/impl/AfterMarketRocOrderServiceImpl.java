package com.chinamobile.install.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.chinamobile.install.config.RedisLockConstant;
import com.chinamobile.install.config.RestTemplateConfig;
import com.chinamobile.install.dao.*;
import com.chinamobile.install.dao.ext.AfterMarketRocOrderInfoMapperExt;
import com.chinamobile.install.enums.AfterMarketRocTypeEnum;
import com.chinamobile.install.exception.StatusConstant;
import com.chinamobile.install.pojo.entity.*;
import com.chinamobile.install.pojo.param.AfterMarketOrderQueryParam;
import com.chinamobile.install.pojo.param.AuditRefundRequest;
import com.chinamobile.install.pojo.vo.*;
import com.chinamobile.install.request.IotRefundRequest;
import com.chinamobile.install.service.IAfterMarketRocOrderService;
import com.chinamobile.install.util.IOTRequestUtils;
import com.chinamobile.install.util.IotLogUtil;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.enums.log.AfterSaleServiceOperateEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.common.BaseConstant.PARTNER_ROLE;

/**
 * <AUTHOR>
 * @Date 2022/12/22 16:38
 **/
@Service
@Slf4j
public class AfterMarketRocOrderServiceImpl implements IAfterMarketRocOrderService {

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private AftermarketOrderRocInfoMapper aftermarketOrderRocInfoMapper;

    @Resource
    private AfterMarketRocOrderInfoMapperExt afterMarketRocOrderInfoMapperExt;

    @Resource
    private AfterMarketOrder2cOfferingInfoMapper afterMarketOrder2cOfferingInfoMapper;

    @Resource
    private UserPartnerMapper userPartnerMapper;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;

    @Resource
    private AftermarketOrderHistoryMapper aftermarketOrderHistoryMapper;

    @Resource
    private SpuOfferingInfoMapper spuOfferingInfoMapper;

    @Resource
    private SpuOfferingInfoHistoryMapper spuOfferingInfoHistoryMapper;
    @Value("${install.encodeKey}")
    private String encodeKey;

    @Value("${install.sm4Key}")
    private String iotSm4Key;

    @Value("${install.sm4Iv}")
    private String iotSm4Iv;

    @Value("${install.synOrderRefundResultUrl}")
    private String synOrderRefundResultUrl;

    @Value("${install.secretKey}")
    private String secretKey;

    @Resource
    private AfterMarketOrder2cInfoMapper afterMarketOrder2cInfoMapper;

    @Resource
    private Order2cDistributorInfoMapper order2cDistributorInfoMapper;
    @Resource
    private Order2cAgentInfoMapper order2cAgentInfoMapper;

    @Resource
    private AftermarketOrderDeliveryAttachmentsMapper aftermarketOrderDeliveryAttachmentsMapper;

    @Resource
    private AftermarketOrderSignInAttachmentsMapper aftermarketOrderSignInAttachmentsMapper;
    @Resource
    private LogService logService;

    @Autowired
    private RedisUtil redisUtil;

    @Resource
    private RedisTemplate redisTemplate;
    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));



    // 数据权限-售后服务退换
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<PageData<AfterMarketRocOrderItemVO>> getAfterMarketRocOrderList(AfterMarketOrderQueryParam param, LoginIfo4Redis loginIfo4Redis) {
        if (param.getAppointmentTime() != null) {
            param.setAppointmentTimeDate(new Date(param.getAppointmentTime()));
        }

        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (ObjectUtils.isEmpty(dataPermissionCodes) || (
            !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_AFTER_MARKET_REFUND_SYSTEM)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_AFTER_MARKET_REFUND_COMPANY)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_AFTER_MARKET_REFUND_PERSONAL)
        )) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }

        // 商品名称搜索支持反斜杠适配
        if(param.getSkuOfferingName() != null){
            param.setSkuOfferingName(param.getSkuOfferingName().replaceAll("\\\\","\\\\\\\\"));
        }
        if(param.getServiceName() != null){
            param.setServiceName(param.getServiceName().replaceAll("\\\\","\\\\\\\\"));
        }

        boolean forPartner = false;
        String roleType = loginIfo4Redis.getRoleType();
        String userId = loginIfo4Redis.getUserId();
//        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_AFTER_MARKET_REFUND_COMPANY)) {
//            //从伙伴，查找对应主伙伴的订单
//            BaseAnswer<Data4User> userBaseAnswer = userFeignClient.queryPrimaryUserPhone(userId);
//            if (userBaseAnswer == null || !userBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) || userBaseAnswer.getData() == null) {
//                log.error("调用获取主用户信息失败。从合作伙伴ID:{}", userId);
//                throw new BusinessException(StatusConstant.INTERNAL_ERROR, "调用获取主用户信息失败。从合作伙伴ID:" + userId);
//            }
//            param.setAdminCooperatorId(userBaseAnswer.getData().getUserId());
//            forPartner = true;
//        }else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_AFTER_MARKET_REFUND_PERSONAL)) {
//
//            if (roleType.equals(BaseConstant.PARTNER_PROVINCE)) {
//                // 省管配置权限为主合作伙伴权限
//                BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
//                if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
//                    throw new BusinessException("10004", "合作伙伴省管账号错误");
//                }
//
//                Data4User data4User = data4UserBaseAnswer.getData();
//                String companyType = data4User.getCompanyType();
//                boolean isProvinceUser = org.apache.commons.lang3.StringUtils.isNotEmpty(companyType) && "2".equals(companyType);
//                String userLocation = data4User.getLocationIdPartner();
//                if (isProvinceUser) {
//                    BaseAnswer<Data4User> userPartner = userFeignClient.getUserPartnerPrimaryByPartnerName(data4User.getPartnerName());
//                    if (userPartner == null || !SUCCESS.getStateCode().equals(userPartner.getStateCode())) {
//                        throw new BusinessException("10004", "合作伙伴省管获取主合作伙伴账号错误");
//                    }
//                    Data4User userPartnerData = userPartner.getData();
//                    if (Optional.ofNullable(userPartnerData).isPresent()) {
//                        param.setAdminCooperatorId(userPartnerData.getUserId());
//                    }
//                    if ("all".equals(userLocation)) {
//                        param.setBeId(data4User.getBeIdPartner());
//                    } else {
//                        param.setLocation(userLocation);
//                    }
//                }
//            }else{
//                //主伙伴，查找自己的
//                param.setAdminCooperatorId(userId);
//            }
//            forPartner = true;
//        }
        // 装维管理员
        if(roleType.equals(BaseConstant.PARTNER_INSTALL_MANAGER_ROLE)){
            forPartner = false;
            param.setInstallManagerId(userId);
        }
        // 装维主
        else if (roleType.equals(BaseConstant.PARTNER_INSTALL_LORD_ROLE)){
            forPartner = true;
            param.setAdminCooperatorId(userId);
        }
        //装维从
        else if (roleType.equals(BaseConstant.PARTNER_INSTALL_SUB_ROLE)){
            forPartner = true;
            //查询对应的从账号对应的主账号的信息
            //听说用户相关的代码要后面在上先用笨方法来
            UserPartner userPartner =  userPartnerMapper.selectByPrimaryKey(userId);

            List<UserPartner> userPartner1 = userPartnerMapper.selectByExample(
                    new UserPartnerExample().createCriteria()
                            .andRoleIdEqualTo("1376576697935507457")
                            .andPartnerNameEqualTo(userPartner.getPartnerName())
                            .andIsLogoffEqualTo(false)
                            .example()
            );
            param.setAdminCooperatorId(userPartner1.get(0).getUserId());

        }
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<AfterMarketRocOrderItemVO> lst = null;
        if (forPartner) {
            lst = afterMarketRocOrderInfoMapperExt.getAfterMarketRocOrderByFilterForPartner(param);
        } else {
            lst = afterMarketRocOrderInfoMapperExt.getAfterMarketRocOrderByFilterForAdmin(param);
        }
        PageInfo<AfterMarketRocOrderItemVO> pageInfo = new PageInfo<>(lst);
        PageData<AfterMarketRocOrderItemVO> pageData = new PageData<>();
        pageData.setPage(param.getPageNum());
        pageData.setData(pageInfo.getList());
        pageData.setCount(pageInfo.getTotal());
        return BaseAnswer.success(pageData);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<AfterMarketRocOrderDetailVO> getAfterMarketRocOrderDetail(String serviceRocOrderId, LoginIfo4Redis loginIfo4Redis) {
        AftermarketOrderRocInfo afterMarketOrderRocInfo = aftermarketOrderRocInfoMapper.selectByPrimaryKey(serviceRocOrderId);
        if(afterMarketOrderRocInfo == null){
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "售后服务退款单：" + serviceRocOrderId + "不存在");
        }

        //判断是否有关联的主订单
        AfterMarketOrder2cInfo afterMarketOrder2cInfo = afterMarketOrder2cInfoMapper.selectByPrimaryKey(afterMarketOrderRocInfo.getServOrderId());
        if (afterMarketOrder2cInfo == null) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "售后服务退款单关联的主订单：" + afterMarketOrderRocInfo.getServOrderId() + "不存在");
        }

        String serviceOrderId = afterMarketOrderRocInfo.getServOrderId();
        AfterMarketRocOrderDetailVO vo = new AfterMarketRocOrderDetailVO();
        BeanUtils.copyProperties(afterMarketOrder2cInfo, vo);
        BeanUtils.copyProperties(afterMarketOrderRocInfo, vo);
        vo.setStatus(afterMarketOrder2cInfo.getStatus());
        vo.setRefundOrderStatus(afterMarketOrderRocInfo.getInnerStatus());
        vo.setPictureOuterUrl(JSON.parseArray(afterMarketOrderRocInfo.getPictureOuterUrl(), String.class));
        //售后服务订单关联商品信息
        AfterMarketOrder2cOfferingInfoExample offeringInfoExample = new AfterMarketOrder2cOfferingInfoExample();
        AfterMarketOrder2cOfferingInfoExample.Criteria offeringInfoCriteria = offeringInfoExample.createCriteria();
        offeringInfoCriteria.andServiceOrderIdEqualTo(afterMarketOrderRocInfo.getServOrderId());
        List<AfterMarketOrder2cOfferingInfo> order2cOfferingInfoList = afterMarketOrder2cOfferingInfoMapper.selectByExample(offeringInfoExample);
        if(CollectionUtils.isNotEmpty(order2cOfferingInfoList)){
            List<AfterMarketOrderOfferingDetailVO> offeringDetailVOList;
            offeringDetailVOList = order2cOfferingInfoList.stream().map(item -> {
                AfterMarketOrderOfferingDetailVO offeringDetailVO = new AfterMarketOrderOfferingDetailVO();
                BeanUtils.copyProperties(item, offeringDetailVO);
                if (StringUtils.isNotEmpty(offeringDetailVO.getAdminCooperatorId())) {
                    BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(offeringDetailVO.getAdminCooperatorId());
                    if (data4UserBaseAnswer != null && data4UserBaseAnswer.getData() != null) {
                        offeringDetailVO.setAdminCooperatorName(data4UserBaseAnswer.getData().getPartnerName());
                        offeringDetailVO.setAdminCooperatorUserName(data4UserBaseAnswer.getData().getName());
                        offeringDetailVO.setAdminCooperatorPhone(IOTEncodeUtils.decryptIOTMessage(data4UserBaseAnswer.getData().getPhone(),encodeKey));
                    }
                }
                return offeringDetailVO;
            }).collect(Collectors.toList());
            vo.setOfferingItems(offeringDetailVOList);
        }

        AftermarketOrderHistoryExample historyExample = new AftermarketOrderHistoryExample();
        AftermarketOrderHistoryExample.Criteria historyCriteria = historyExample.createCriteria();
        historyExample.setOrderByClause("create_time ASC");
        historyCriteria.andRefundServOrderIdEqualTo(serviceRocOrderId);
        //todo 创建历史记录信息
        //只展示售后退款订单状态变化历史，过滤售后订单相关的
        historyCriteria.andOperateTypeEqualTo(2);
        List<AftermarketOrderHistory> histories = aftermarketOrderHistoryMapper.selectByExample(historyExample);

        if(CollectionUtils.isNotEmpty(histories)){
            List<String> historyVOs = histories.stream().map(item -> {
                StringBuilder result = new StringBuilder();
                if (item.getInnerStatus() == AfterMarketRocTypeEnum.ORDER_REFUND.getStatus()||
                        item.getInnerStatus() == AfterMarketRocTypeEnum.REFUND_CANCEL.getStatus()) {
                    result.append("买家于").append(DateUtils.dateToStr(item.getCreateTime(), DateUtils.DATETIME_FORMAT_T))
                            .append(item.getOperateMessage());
                } else if (item.getInnerStatus() == AfterMarketRocTypeEnum.REFUND_AGREE.getStatus()||
                        item.getInnerStatus() == AfterMarketRocTypeEnum.REFUND_REJECT.getStatus()) {
                    result.append("卖家于").append(DateUtils.dateToStr(item.getCreateTime(), DateUtils.DATETIME_FORMAT_T))
                            .append(item.getOperateMessage());
                } else if(item.getInnerStatus() == AfterMarketRocTypeEnum.REFUND_SUCCESS.getStatus()){
                    result.append("商城于").append(DateUtils.dateToStr(item.getCreateTime(), DateUtils.DATETIME_FORMAT_T))
                            .append(item.getOperateMessage());
                } else {
                    result.append("错误订单状态");
                }
                return result.toString();
            }).collect(Collectors.toList());
            vo.setHistories(historyVOs);
        }

        Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(afterMarketOrder2cInfo.getOfferingOrderId());
        if(order2cInfo != null){
            vo.setOfferingOrderStatus(order2cInfo.getOrderStatus());

//            AftermarketOrderRocInfoExample rocInfoExample = new AftermarketOrderRocInfoExample();
//            //todo 查询条件
//            AftermarketOrderRocInfoExample.Criteria rocInfoCriteria = rocInfoExample.createCriteria();
//            rocInfoCriteria.andServOrderIdEqualTo(serviceRocOrderId);
//            List<AftermarketOrderRocInfo> rocInfoList = aftermarketOrderRocInfoMapper.selectByExample(rocInfoExample);
//            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(rocInfoList)) {
//                vo.setRefundOrderStatus(rocInfoList.get(0).getInnerStatus());
//            }

            //非合作伙伴，还需要返回主订单相关信息
            //改成装维管理员返回
            if (BaseConstant.PARTNER_INSTALL_MANAGER_ROLE.equals(loginIfo4Redis.getRoleType())
                && !PARTNER_ROLE.equals(loginIfo4Redis.getRoleType())
                && !loginIfo4Redis.getIsPartner()) {

                MallOrderInfoVO mallOrderInfoVO = new MallOrderInfoVO();
                mallOrderInfoVO.setOrderId(order2cInfo.getOrderId());
                mallOrderInfoVO.setSpuCode(order2cInfo.getSpuOfferingCode());
                mallOrderInfoVO.setSpuOfferingClass(order2cInfo.getSpuOfferingClass());
                mallOrderInfoVO.setSpuOfferingVersion(order2cInfo.getSpuOfferingVersion());
                mallOrderInfoVO.setReceiverPhone(IOTEncodeUtils.decryptSM4(order2cInfo.getContactPhone(), iotSm4Key, iotSm4Iv));
                mallOrderInfoVO.setReceiverName(IOTEncodeUtils.decryptSM4(order2cInfo.getContactPersonName(), iotSm4Key, iotSm4Iv));
                mallOrderInfoVO.setReceiverAddress(contactAddr(StringUtils.isEmpty(order2cInfo.getAddr1()) ? "" : order2cInfo.getAddr1(), StringUtils.isEmpty(order2cInfo.getAddr2()) ? "" : order2cInfo.getAddr2(), StringUtils.isEmpty(order2cInfo.getAddr3())? "" : order2cInfo.getAddr3(),
                        StringUtils.isEmpty(order2cInfo.getAddr4()) ? "" : order2cInfo.getAddr4(), StringUtils.isEmpty(order2cInfo.getUsaddr()) ? "" : order2cInfo.getUsaddr()));
                mallOrderInfoVO.setTotalPrice(IOTEncodeUtils.decryptSM4(order2cInfo.getTotalPrice(), iotSm4Key, iotSm4Iv));

                // 增加版本号相关适配
//                SpuOfferingInfoExample spuOfferingInfoExample = new SpuOfferingInfoExample();
//                SpuOfferingInfoExample.Criteria spuCriteria = spuOfferingInfoExample.createCriteria();
//                spuCriteria.andOfferingCodeEqualTo(order2cInfo.getSpuOfferingCode());
//                List<SpuOfferingInfo> spuOfferingInfos = spuOfferingInfoMapper.selectByExample(spuOfferingInfoExample);

                SpuOfferingInfoHistoryExample spuOfferingInfoHistoryExample = new SpuOfferingInfoHistoryExample();
                SpuOfferingInfoHistoryExample.Criteria spuCriteria = spuOfferingInfoHistoryExample.createCriteria();
                spuCriteria.andOfferingCodeEqualTo(order2cInfo.getSpuOfferingCode());
                spuCriteria.andSpuOfferingVersionEqualTo(order2cInfo.getSpuOfferingVersion());
                List<SpuOfferingInfoHistory> spuOfferingInfos = spuOfferingInfoHistoryMapper.selectByExample(spuOfferingInfoHistoryExample);

                mallOrderInfoVO.setSpuName(spuOfferingInfos.get(0).getOfferingName());
                mallOrderInfoVO.setSpuHeadPicUrl(spuOfferingInfos.get(0).getImgUrl());

                Order2cAtomInfoExample atomInfoExample = new Order2cAtomInfoExample();
                Order2cAtomInfoExample.Criteria atomCriteria = atomInfoExample.createCriteria();
                atomCriteria.andOrderIdEqualTo(order2cInfo.getOrderId()).andAtomOfferingClassNotEqualTo("S");
                List<Order2cAtomInfo> atomInfos = order2cAtomInfoMapper.selectByExample(atomInfoExample);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(atomInfos)) {
                    List<MallAtomOrderInfoVO> atomOrderInfoVOS = atomInfos.stream().map(item -> {
                        MallAtomOrderInfoVO atomOrderInfoVO = new MallAtomOrderInfoVO();
                        atomOrderInfoVO.setAtomName(item.getAtomOfferingName());
                        atomOrderInfoVO.setAtomOfferingClass(item.getAtomOfferingClass());
                        atomOrderInfoVO.setQuantity(item.getAtomQuantity());
                        atomOrderInfoVO.setModel(item.getModel());
                        atomOrderInfoVO.setSkuName(item.getSkuOfferingName());
                        atomOrderInfoVO.setSkuCode(item.getSkuOfferingCode());
                        atomOrderInfoVO.setAtomOfferingVersion(item.getAtomOfferingVersion());
                        atomOrderInfoVO.setSkuOfferingVersion(item.getSkuOfferingVersion());

                        if ("O".equals(item.getAtomOfferingClass())
                                || "D".equals(item.getAtomOfferingClass())
                                || "P".equals(item.getAtomOfferingClass())
                                || "F".equals(item.getAtomOfferingClass())
                        ) {
                            atomOrderInfoVO.setCooperatorName(item.getSupplierName());
                        } else {
                            String cooperatorId = item.getCooperatorId();
                            if (StringUtils.isNotEmpty(cooperatorId)) {
                                BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(cooperatorId);
                                if (data4UserBaseAnswer != null && data4UserBaseAnswer.getData() != null) {
                                    atomOrderInfoVO.setCooperatorName(data4UserBaseAnswer.getData().getPartnerName());
                                }
                            }
                        }
                        return atomOrderInfoVO;
                    }).collect(Collectors.toList());
                    mallOrderInfoVO.setAtomOrders(atomOrderInfoVOS);
                }

                vo.setOfferingOrderInfo(mallOrderInfoVO);
            }
            // 添加分销员 客户经理 渠道商信息
            List<Order2cDistributorInfo> order2cDistributorInfos = order2cDistributorInfoMapper
                    .selectByExample(new Order2cDistributorInfoExample().createCriteria()
                            .andOrderIdEqualTo(order2cInfo.getOrderId()).example());
            if (CollectionUtils.isNotEmpty(order2cDistributorInfos)) {
                AfterMarketRocOrderDetailVO.Distributor distributor = new AfterMarketRocOrderDetailVO.Distributor();
                distributor.setDistributorLevel(order2cDistributorInfos.get(0).getDistributorLevel());
                distributor.setDistributorPhone(order2cDistributorInfos.get(0).getDistributorPhone());
                distributor.setDistributorShareCode(order2cDistributorInfos.get(0).getDistributorShareCode());
                vo.setDistributor(distributor);
            }

            List<Order2cAgentInfo> order2cAgentInfos = order2cAgentInfoMapper
                    .selectByExample(new Order2cAgentInfoExample().createCriteria()
                            .andOrderIdEqualTo(order2cInfo.getOrderId()).example());
            if (CollectionUtils.isNotEmpty(order2cAgentInfos)) {
                AfterMarketRocOrderDetailVO.Agent agent = new AfterMarketRocOrderDetailVO.Agent();
                agent.setAgentName(order2cAgentInfos.get(0).getAgentName());
                agent.setAgentNumber(order2cAgentInfos.get(0).getAgentNumber());
                agent.setAgentPhone(order2cAgentInfos.get(0).getAgentPhone());
                vo.setAgent(agent);
            }
            AfterMarketRocOrderDetailVO.AccountManager accountManager = new AfterMarketRocOrderDetailVO.AccountManager();
            accountManager.setEmployeeNum(order2cInfo.getEmployeeNum());
            accountManager.setCustomerManagerName(order2cInfo.getCustMgName());
            accountManager.setCustomerManagerPhone(order2cInfo.getCustMgPhone());
            vo.setAccountManager(accountManager);

        }
        // 添加安装图片
        AftermarketOrderDeliveryAttachmentsExample aftermarketOrderDeliveryAttachmentsExample = new AftermarketOrderDeliveryAttachmentsExample();
        AftermarketOrderDeliveryAttachmentsExample.Criteria aftermarketOrderDeliveryAttachmentsCriteria = aftermarketOrderDeliveryAttachmentsExample
                .createCriteria();
        aftermarketOrderDeliveryAttachmentsCriteria.andServiceOrderIdEqualTo(serviceOrderId);
        List<AftermarketOrderDeliveryAttachments> aftermarketOrderDeliveryAttachmentsList = aftermarketOrderDeliveryAttachmentsMapper
                .selectByExample(aftermarketOrderDeliveryAttachmentsExample);
        if (aftermarketOrderDeliveryAttachmentsList.size() > 0) {
            List<UpResult> upResultList = aftermarketOrderDeliveryAttachmentsList.stream()
                    .map(aftermarketOrderDeliveryAttachments -> {
                        UpResult upResult = new UpResult();
                        upResult.setFileName(aftermarketOrderDeliveryAttachments.getFileName());
                        upResult.setKey(aftermarketOrderDeliveryAttachments.getFileKey());
                        upResult.setOuterUrl(aftermarketOrderDeliveryAttachments.getFileUrl());
                        return upResult;
                    }).collect(Collectors.toList());
            vo.setImageList(upResultList);
        }
        // 记录日志

        //添加签到图片
        AftermarketOrderSignInAttachmentsExample aftermarketOrderSignInAttachmentsExample = new AftermarketOrderSignInAttachmentsExample();
        List<AftermarketOrderSignInAttachments> signInAttachments = aftermarketOrderSignInAttachmentsMapper.selectByExample(aftermarketOrderSignInAttachmentsExample.createCriteria()
                .andServiceOrderIdEqualTo(serviceOrderId).example());
        if (CollectionUtils.isNotEmpty(signInAttachments)) {
            List<UpResult> upResultList = signInAttachments.stream().map(aftermarketOrderSignInAttachments -> {
                UpResult upResult = new UpResult();
                upResult.setFileName(aftermarketOrderSignInAttachments.getFileName());
                upResult.setKey(aftermarketOrderSignInAttachments.getFileKey());
                upResult.setOuterUrl(aftermarketOrderSignInAttachments.getFileUrl());
                return upResult;
            }).collect(Collectors.toList());
            vo.setSignInImageList(upResultList);
        }


        return BaseAnswer.success(vo);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> refundAudit(AuditRefundRequest request, String userId, String ip) {
        log.info("售后服务单退款审批 rocRefundAudit：{}",request);
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
        return redisUtil.smartLock(RedisLockConstant.ORDER_LOCK + request.getRefundOrderId(), () -> {

            //对结果进行入库，先查询
            AftermarketOrderRocInfo orderRocInfo = aftermarketOrderRocInfoMapper.selectByPrimaryKey(request.getRefundOrderId());
            if(!StringUtils.isEmpty(orderRocInfo.getAuditResult())){
                //审核过的，不能再次审核
                log.error("售后服务退款单已审核过，直接返回");
                executorService.execute(() -> {

                    logService.recordOperateLogAsync(ModuleEnum.AFTER_SALE_SERVICE.code,
                            AfterSaleServiceOperateEnum.AFTER_SALE_EXCHANGE_PURCHASE.code,
                            IotLogUtil.afterSaleRefundAuditFromParam(request), userId, ip, LogResultEnum.LOG_FAIL.code, StatusConstant.AUDIT_FAIL.getMessage());
                });
                throw new BusinessException(StatusConstant.AUDIT_FAIL);
            }

            //查关联售后订单
            AfterMarketOrder2cInfo afterMarketOrder2cInfo = afterMarketOrder2cInfoMapper.selectByPrimaryKey(request.getOrderId());
            if(null == afterMarketOrder2cInfo){
                executorService.execute(() -> {

                    logService.recordOperateLogAsync(ModuleEnum.AFTER_SALE_SERVICE.code,
                            AfterSaleServiceOperateEnum.AFTER_SALE_EXCHANGE_PURCHASE.code,
                            IotLogUtil.afterSaleRefundAuditFromParam(request), userId, ip, LogResultEnum.LOG_FAIL.code,  "售后服务退款单关联的售后服务订单：" + request.getOrderId() + "不存在");
                });
                throw new BusinessException(StatusConstant.INTERNAL_ERROR, "售后服务退款单关联的售后服务订单：" + request.getOrderId() + "不存在");
            }
//        //查售后订单关联的主订单
//        Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(afterMarketOrder2cInfo.getOfferingOrderId());
//        if(null == order2cInfo){
//            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "售后服务订单单关联的主订单：" + afterMarketOrder2cInfo.getOfferingOrderId() + "不存在");
//        }

            //更改退款单审核状态
            Integer rocInnerStatus;
            if("1".equals(request.getAuditResult())){
                //通过
                //对不同售后类型添加状态
                if(request.getRocType().equals(1)){
                    //仅退款
                    rocInnerStatus = AfterMarketRocTypeEnum.REFUND_AGREE.getStatus();
                    orderRocInfo.setInnerStatus(AfterMarketRocTypeEnum.REFUND_AGREE.getStatus());
                }else{
                    //主要针对撤销申请
//                rocInnerStatus = AfterMarketRocTypeEnum.REFUND_AGREE.getStatus();
//                orderRocInfo.setInnerStatus(AfterMarketRocTypeEnum.REFUND_AGREE.getStatus());
                    executorService.execute(() -> {

                        logService.recordOperateLogAsync(ModuleEnum.AFTER_SALE_SERVICE.code,
                                AfterSaleServiceOperateEnum.AFTER_SALE_EXCHANGE_PURCHASE.code,
                                IotLogUtil.afterSaleRefundAuditFromParam(request), userId, ip, LogResultEnum.LOG_FAIL.code,"不支持的退款申请类型");
                    });
                    throw new BusinessException(StatusConstant.INTERNAL_ERROR, "不支持的退款申请类型");
                }
            }else{
                //不通过
                if(StringUtils.isEmpty(request.getAuditResultReason())){
                    //不通过时必须填写具体原因
                    log.error("售后服务退款单需填写审核不通过原因！");
                    executorService.execute(() -> {

                        logService.recordOperateLogAsync(ModuleEnum.AFTER_SALE_SERVICE.code,
                                AfterSaleServiceOperateEnum.AFTER_SALE_EXCHANGE_PURCHASE.code,
                                IotLogUtil.afterSaleRefundAuditFromParam(request), userId, ip, LogResultEnum.LOG_FAIL.code, StatusConstant.NO_AUDIT_REASON.getMessage());
                    });
                    throw new BusinessException(StatusConstant.NO_AUDIT_REASON);
                }
                if(request.getAuditResultReason().length()> 600){
                    executorService.execute(() -> {

                        logService.recordOperateLogAsync(ModuleEnum.AFTER_SALE_SERVICE.code,
                                AfterSaleServiceOperateEnum.AFTER_SALE_EXCHANGE_PURCHASE.code,
                                IotLogUtil.afterSaleRefundAuditFromParam(request), userId, ip, LogResultEnum.LOG_FAIL.code, "审核原因长度不能超过600个字符");
                    });
                    throw new BusinessException(StatusConstant.INTERNAL_ERROR, "审核原因长度不能超过600个字符" );
                }
                if(request.getRocType().equals(1)){
                    //仅退款时状态已结束，修改订单状态 变为
                    rocInnerStatus = AfterMarketRocTypeEnum.REFUND_REJECT.getStatus();
                    orderRocInfo.setInnerStatus(AfterMarketRocTypeEnum.REFUND_REJECT.getStatus());
                }else{
                    //todo 撤销退款申请不通过怎么处理？直接在撤销退款请求同步时处理？
                }

            }

            //增加审批人等信息
            orderRocInfo.setAuditId(userId);
            orderRocInfo.setAuditResult(request.getAuditResult());
            orderRocInfo.setAuditResultReason(request.getAuditResultReason());

            //退款记录审核历史
            Date date = new Date();
            AftermarketOrderHistory aftermarketOrderHistory = new AftermarketOrderHistory()
                    .withId(BaseServiceUtils.getId())
                    .withServiceOrderId(orderRocInfo.getServOrderId())
                    .withRefundServOrderId(orderRocInfo.getRefundServOrderId())
                    .withOperatorId(userId)
                    .withOperateType(2)
                    .withInnerStatus(orderRocInfo.getInnerStatus())
                    .withCreateTime(date)
                    .withUpdateTime(date)
                    .withOperateMessage(AfterMarketRocTypeEnum.getHistoryDescribe(orderRocInfo.getInnerStatus()).concat("。"));
            aftermarketOrderHistoryMapper.insertSelective(aftermarketOrderHistory);


            //更新退单表状态
            //todo 时间？自动更新？
            orderRocInfo.setUpdateTime(date);
            aftermarketOrderRocInfoMapper.updateByPrimaryKeySelective(orderRocInfo);

            //构造同步到IOT商城的结构体
            IotRefundRequest iotRefundRequest = new IotRefundRequest();
            iotRefundRequest.setRefundOrderId(request.getRefundOrderId());
            iotRefundRequest.setRefundsType(orderRocInfo.getRefundsType());
            iotRefundRequest.setOrderId(orderRocInfo.getServOrderId());
            iotRefundRequest.setAuditResult(orderRocInfo.getAuditResult());
            iotRefundRequest.setAuditResultReason(request.getAuditResultReason());

            //发送IOT同步放到最后防止入库失败导致回滚
            sendMsgToIot(iotRefundRequest,afterMarketOrder2cInfo.getBeId(),synOrderRefundResultUrl,afterMarketOrder2cInfo.getRegionId());
            //添加日志
            logService.recordOperateLog(ModuleEnum.AFTER_SALE_SERVICE.code,
                    AfterSaleServiceOperateEnum.AFTER_SALE_EXCHANGE_PURCHASE.code,
                    IotLogUtil.afterSaleRefundAuditFromParam(request),LogResultEnum.LOG_SUCESS.code, null);
            return baseAnswer;
        });
    }


    private String contactAddr(String addr1, String addr2, String addr3, String addr4, String usaddr) {
        return IOTEncodeUtils.decryptSM4(addr1, iotSm4Key, iotSm4Iv) +
                IOTEncodeUtils.decryptSM4(addr2, iotSm4Key, iotSm4Iv) +
                IOTEncodeUtils.decryptSM4(addr3, iotSm4Key, iotSm4Iv) +
                IOTEncodeUtils.decryptSM4(addr4, iotSm4Key, iotSm4Iv) +
                IOTEncodeUtils.decryptSM4(usaddr, iotSm4Key, iotSm4Iv);
    }

    private void sendMsgToIot(Object o, String beId, String url, String regionId) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("content-type", "application/json;charset=utf-8");

        String iotRequest = IOTRequestUtils.getIotRequest(JSON.toJSONString(o), secretKey, beId, regionId);
        log.info("售后退款审批请求IOT商城内容为:" + iotRequest);
        HttpEntity<String> requestEntity = new HttpEntity<>(iotRequest, headers);
        ResponseEntity<IOTAnswer> response;
        try {
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            response = restTemplateHttps.postForEntity(url, requestEntity, IOTAnswer.class);
        } catch (Exception e) {
            log.error("售后退款审批请求IOT商城异常捕获:" + e);
            throw new BusinessException(StatusConstant.SYNC_IOT_FAILED);
        }
        IOTAnswer iotAnswer = response.getBody();
        if (iotAnswer == null || !"0".equals(iotAnswer.getResultCode())) {
            //没返回信息则为提示同步失败，返回错误码则提示返回错误描述
            log.error("同步到IOT商城失败，请求地址为:{}，返回结果为:{}", url, iotAnswer);
            throw new BusinessException(StatusConstant.SYNC_IOT_FAILED, iotAnswer == null ? "同步到IOT商城失败，请重试" : iotAnswer.getResultDesc());
        }
        log.info("请求IOT商城结果返回为:{}", iotAnswer);
    }
}
