package com.chinamobile.install.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/18 16:49
 * @description 河南装维工单批量录入Excel
 */
@Data
public class OrderHenanZwImportExcel {

    @ExcelProperty("订单号")
    private String orderNo;

    @ExcelProperty("产品名称")
    private String productName;

    @ExcelProperty("工单类别")
    private String orderType;

    @ExcelProperty("装维类型")
    private String businessTypeString;

    @ExcelProperty("客户联系人")
    private String customContact;

    @ExcelProperty("客户联系电话")
    private String customContactPhone;

    @ExcelProperty("安装地市所属省份")
    private String provinceName;

    @ExcelProperty("安装地市所属地市")
    private String cityName;

    @ExcelProperty("安装地址所属区县")
    private String areaName;

    @ExcelProperty("安装详细地址")
    private String address;
}
