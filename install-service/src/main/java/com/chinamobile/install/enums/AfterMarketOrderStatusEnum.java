package com.chinamobile.install.enums;

/**
 * @Author: dgj
 * @Description: 售后订单状态枚举量
 */
public enum AfterMarketOrderStatusEnum {
    /**
     * 售后服务订单状态:
     * 1:待预约 -对应商城同步状态【1待预约（付款完成）】
     * 11:待分派-装维管理员接单时，用户提交预约申请后，售后服务订单状态由“待预约”变为“待分派”，装维管理员完成订单分派后，订单状态由“待分派”变为“待派单”;合作伙伴接单时无本状态，直接变为2
     * 2:待派单-对应商城同步状态【2派单中（预约完成）】
     * 3:已预约
     * 31：已签到
     * 4.已完结（成功）
     * 5.已完成（失败）
     * 7.交易完成-对应商城同步状态【3订单计收（订单同步至CMIoT成功后，同步本状态）】
     * 8.交易失败-对应商城同步状态【4退款完成】
     * 9.同步省侧
     * */
    // 订单状态
    TO_APPOINTMENT(1, "待预约"),
    BEFORE_DISPATCHING(11, "待分派"),
    DISPATCHING(2, "待派单"),
    DISPATCHED(3, "已派单"),
    SIGN_IN(31, "已签到"),
    DELIVERY_SUCCESS(4, "已完结（成功）"),
    DELIVERY_FAIL(5, "已完结（失败）"),
    ORDER_CANCELED(6, "已撤销"),
    ORDER_FINISH(7, "交易完成"),
    ORDER_FAIL(8, "交易失败"),
    ORDER_SYNC_PROVINCE(9, "同步省侧"),
    ORDER_CLOSED(10, "已关闭");

    /**
     * 售后订单状态码
     */
    private final Integer status;
    /**
     * 售后订单状态信息
     */
    private final String message;

    AfterMarketOrderStatusEnum(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public Integer getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }
}
