package com.chinamobile.install.enums;

public enum HenanZwSheetActionEnum {

    ACCEPT("C01", "受理操作"),
    CANCEL_ACCEPT("C02", "取消受理操作"),
    SUBMIT("C03", "提交操作"),
    REJECT("C04", "驳回上一个环节"),
    RETURN("C05", "退单"),
    CANCEL("C06", "撤单"),
    MOVE("C07", "移交"),
    PRINTED("C08", "已打单"),
    DONE("C09", "已完成"),
    MISTAKE("C10", "工单异常"),
    RETURN_CANCEL("C11", "退撤单"),
    PRINTING("C12", "打单中"),
    UN_ACCEPT("C13", "未受理");

    private String action;

    private String message;

    HenanZwSheetActionEnum(String action, String message) {
        this.action = action;
        this.message = message;
    }

    public String getAction() {
        return action;
    }

    public String getMessage() {
        return message;
    }
}
