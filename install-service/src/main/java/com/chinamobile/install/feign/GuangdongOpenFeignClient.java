package com.chinamobile.install.feign;


import com.chinamobile.iot.sc.entity.b2b.CommonProvinceSyncParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/09
 * @description 广东能力开放平台接口
 */
@Service
@FeignClient(name = "guangdong-open-feign-client", url = "${zhuangwei.url.guangdongOpenUrl}")
public interface GuangdongOpenFeignClient {

    @PostMapping("/ocdm-adapter/order/receiveOrderGD")
    String sendZwOrder(
        @RequestBody CommonProvinceSyncParam body
    );
}
