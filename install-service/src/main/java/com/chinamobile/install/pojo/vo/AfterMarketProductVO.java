package com.chinamobile.install.pojo.vo;

import lombok.Data;

import java.util.Date;

@Data
public class AfterMarketProductVO {

    //售后服务包名称
    private String afterMarketInternalName;

    //售后服务包对外名称
    private String afterMarketExternalName;

    //售后服务包编码
    private String afterMarketCode;

    //售后服务类型 枚举值： 1：OneNET/OnePark属地化服务 2：铁通增值服务
    private String aftermarketType;

    //关联的主商品信息id
    private String aftermarketOfferingCodeId;

    //关联规格编码
    private String skuOfferingCode;

    //关联原子编码
    private String offeringCode;

    //配置合作伙伴状态  0--未配置  1--已配置
    private Integer configStatus;

    //所属合作伙伴
    private String partnerName;

    //合作伙伴联系人姓名
    private String cooperatorName;

    //配置时间 yyyy-MM-dd HH:mm:ss
    private Date configTime;

    private String spuOfferingVersion;

    private String skuOfferingVersion;

    private String atomOfferingVersion;

    private String afterMarketVersion;


}
