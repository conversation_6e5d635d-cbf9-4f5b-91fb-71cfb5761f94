package com.chinamobile.install.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2022/12/30 00:13
 **/
@Data
public class AfterMarketRocOrderAuditParam {

    /**
     * 退款订单请求流水号
     */
    @NotBlank(message = "请输入售后服务退单订单请求流水号")
    private String refundRocOrderId;

    /**
     * 订单ID
     */
    @NotBlank(message = "请输入售后服务订单ID")
    private String serviceOrderId;

    /**
     * 售后类型 1 仅退款 2 退货退款 3 换货
     */
    private Integer rocType = 1;
    /**
     * 审核结果
     * 1 通过
     * 2 不通过
     */
    @NotBlank(message = "请输入审核结果")
    private String auditResult;

    /**
     * 合作伙伴地址ID
     */
    private String partnerAddressId;
    /**
     * 审核原因
     */
    private String auditResultReason = "";


}
