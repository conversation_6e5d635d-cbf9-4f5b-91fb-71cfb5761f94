package com.chinamobile.install.pojo.param;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

@Data
public class AfterMarketProductConfigPartnerParam {

    //关联的主商品信息id
    @NotNull(message = "主商品信息Id不能为空")
    private String aftermarketOfferingCodeId;

    //硬件名称
    private String hardwareName;

    //型号
    private String model;

    //颜色
    private String color;

    //备注1
    private String remark1;

    //备注2
    private String remark2;

    //备注3
    private String remark3;

    //接单方式 1--OS接单； 2--省内接单
    @Range(min = 1,max = 2,message = "接单方式错误")
    @NotNull(message = "接单方式不能为空")
    private Integer orderTakeType;

    //省侧装维平台编码
    private String provinceInstallPlatformCode;

    //装维管理员用户id
    private String installManagerId;

    //装维主账号用户id
    private String adminCooperatorId;

    //标准服务id
    private String standardServiceId;

}
