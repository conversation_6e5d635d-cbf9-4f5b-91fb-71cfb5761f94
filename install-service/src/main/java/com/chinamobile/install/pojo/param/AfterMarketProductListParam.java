package com.chinamobile.install.pojo.param;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

@Data
public class AfterMarketProductListParam extends BasePageQuery {

    //售后服务包名称
    private String afterMarketInternalName;

    //售后服务包对外名称
    private String afterMarketExternalName;

    //售后服务包编码
    private String afterMarketCode;

    //售后服务类型 枚举值： 1：OneNET/OnePark属地化服务 2：铁通增值服务 3:铁通增值服务(卡+X专用)
    @Range(min = 1,max = 3,message = "售后服务类型错误")
    private String aftermarketType;

    //关联规格编码
    private String skuOfferingCode;

    //关联原子编码
    private String offeringCode;

    //配置合作伙伴状态  0--未配置  1--已配置
    @Range(min = 0,max = 1,message = "配置合作伙伴状态错误")
    private Integer configStatus;

    //所属合作伙伴
    private String partnerName;

}
