package com.chinamobile.install.pojo.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 售后订单
 */
@Data
public class AfterMarketOrderOfferingItemVO {
    /**
     * 售后订单关联售后服务表主键
     */
    private String id;

    /**
     * 售后订单id
     */
    private String serviceOrderId;

    /**
     * 售后商品code
     */
    private String afterMarketCode;

    /**
     * 售后商品名称
     */
    private String afterMarketName;

    /**
     * 售后服务类型,1、OneNET/OnePark属地化服务；2、铁通增值服务；3、铁通增值服务（卡+X专用）
     */
    private Integer afterMarketType;

    /**
     * 接单方式 1--OS接单；2--省内接单
     */
    private Integer orderTakeType;

    /**
     * 售后服务商品结算单价
     */
    private String afterMarketSettlePrice;

    /**
     * 合作伙伴主账号名称
     */
    private String adminCooperatorName;

    /**
     * 合作伙伴主账号ID
     */
    private String adminCooperatorId;

    /**
     * 规格商品名称
     */
    private String skuOfferingName;

    /**
     * 原子商品名称
     */
    private String atomOfferingName;

    /**
     * 订购数量
     */
    private Long quantity;


}
