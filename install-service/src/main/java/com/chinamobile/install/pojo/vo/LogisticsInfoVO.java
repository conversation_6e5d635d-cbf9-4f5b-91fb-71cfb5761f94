package com.chinamobile.install.pojo.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 物流信息VO
 */
@Data
public class LogisticsInfoVO {
    /**
     * 物流主键ID
     */
    private String id;


    /**
     * 0 发货物流，1退货物流
     */
    private Integer logisticsType;

    /**
     * 物流编码
     */
    private String logisCode;

    /**
     * 物流供应商编码
     */
    private String supplierName;
    /**
     * 物流供应商编码名称
     */
    private String supplierCnName;

    /**
     * 备注信息
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 快递100物流状态，快递单当前状态，默认为0在途，1揽收，2疑难，3签收，4退签，5派件，8清关，14拒签等10个基础物流状态
     */
    private Integer logisticsState;

    /**
     * 物流状态描述
     */
    private String logisticsStateDesc;
}
