package com.chinamobile.install.pojo.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 主订单信息
 */
@Data
public class MallOrderInfoVO {
    /**
     * 订单ID
     */
    private String orderId;


    /**
     * 商品组/销售商品名称
     */
    private String spuName;

    /**
     * 商品组编码
     */
    private String spuCode;

    /**
     * 商品类型
     */
    private String spuOfferingClass;

    /**
     * 订购金额
     */
    private String totalPrice;

    /**
     * 收货人姓名
     */
    private String receiverName;


    /**
     * 收货人电话
     */
    private String receiverPhone;
    /**
     * 收货人地址
     */
    private String receiverAddress;

    /**
     * 原子订单信息
     */
    private List<MallAtomOrderInfoVO> atomOrders;

    /**
     * spu版本号
     */
    private String spuOfferingVersion;

    /**
     * 商品头图
     */
    private String spuHeadPicUrl;
}
