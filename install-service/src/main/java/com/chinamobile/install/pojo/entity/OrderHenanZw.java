package com.chinamobile.install.pojo.entity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class OrderHenanZw {
    private String sheetNo;

    private String orderNo;

    private String spuName;

    private String spuCode;

    private String spuOfferingClass;

    private String skuName;

    private String skuCode;

    private String atomName;

    private String atomCode;

    private String atomOfferingClass;

    private String model;

    private String sn;

    private Long quantity;

    private Long price;

    private Integer status;

    private Date sendTime;

    private String cooperatorId;

    private String cooperatorName;

    private Integer businessType;

    private String customContact;

    private String customContactPhone;

    private String provinceCode;

    private String provinceName;

    private String cityCode;

    private String cityName;

    private String areaCode;

    private String areaName;

    private String address;

    private Date createTime;

    private Date updateTime;

    public String getSheetNo() {
        return sheetNo;
    }

    public OrderHenanZw withSheetNo(String sheetNo) {
        this.setSheetNo(sheetNo);
        return this;
    }

    public void setSheetNo(String sheetNo) {
        this.sheetNo = sheetNo == null ? null : sheetNo.trim();
    }

    public String getOrderNo() {
        return orderNo;
    }

    public OrderHenanZw withOrderNo(String orderNo) {
        this.setOrderNo(orderNo);
        return this;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public String getSpuName() {
        return spuName;
    }

    public OrderHenanZw withSpuName(String spuName) {
        this.setSpuName(spuName);
        return this;
    }

    public void setSpuName(String spuName) {
        this.spuName = spuName == null ? null : spuName.trim();
    }

    public String getSpuCode() {
        return spuCode;
    }

    public OrderHenanZw withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode == null ? null : spuCode.trim();
    }

    public String getSpuOfferingClass() {
        return spuOfferingClass;
    }

    public OrderHenanZw withSpuOfferingClass(String spuOfferingClass) {
        this.setSpuOfferingClass(spuOfferingClass);
        return this;
    }

    public void setSpuOfferingClass(String spuOfferingClass) {
        this.spuOfferingClass = spuOfferingClass == null ? null : spuOfferingClass.trim();
    }

    public String getSkuName() {
        return skuName;
    }

    public OrderHenanZw withSkuName(String skuName) {
        this.setSkuName(skuName);
        return this;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName == null ? null : skuName.trim();
    }

    public String getSkuCode() {
        return skuCode;
    }

    public OrderHenanZw withSkuCode(String skuCode) {
        this.setSkuCode(skuCode);
        return this;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode == null ? null : skuCode.trim();
    }

    public String getAtomName() {
        return atomName;
    }

    public OrderHenanZw withAtomName(String atomName) {
        this.setAtomName(atomName);
        return this;
    }

    public void setAtomName(String atomName) {
        this.atomName = atomName == null ? null : atomName.trim();
    }

    public String getAtomCode() {
        return atomCode;
    }

    public OrderHenanZw withAtomCode(String atomCode) {
        this.setAtomCode(atomCode);
        return this;
    }

    public void setAtomCode(String atomCode) {
        this.atomCode = atomCode == null ? null : atomCode.trim();
    }

    public String getAtomOfferingClass() {
        return atomOfferingClass;
    }

    public OrderHenanZw withAtomOfferingClass(String atomOfferingClass) {
        this.setAtomOfferingClass(atomOfferingClass);
        return this;
    }

    public void setAtomOfferingClass(String atomOfferingClass) {
        this.atomOfferingClass = atomOfferingClass == null ? null : atomOfferingClass.trim();
    }

    public String getModel() {
        return model;
    }

    public OrderHenanZw withModel(String model) {
        this.setModel(model);
        return this;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getSn() {
        return sn;
    }

    public OrderHenanZw withSn(String sn) {
        this.setSn(sn);
        return this;
    }

    public void setSn(String sn) {
        this.sn = sn == null ? null : sn.trim();
    }

    public Long getQuantity() {
        return quantity;
    }

    public OrderHenanZw withQuantity(Long quantity) {
        this.setQuantity(quantity);
        return this;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public Long getPrice() {
        return price;
    }

    public OrderHenanZw withPrice(Long price) {
        this.setPrice(price);
        return this;
    }

    public void setPrice(Long price) {
        this.price = price;
    }

    public Integer getStatus() {
        return status;
    }

    public OrderHenanZw withStatus(Integer status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public OrderHenanZw withSendTime(Date sendTime) {
        this.setSendTime(sendTime);
        return this;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public String getCooperatorId() {
        return cooperatorId;
    }

    public OrderHenanZw withCooperatorId(String cooperatorId) {
        this.setCooperatorId(cooperatorId);
        return this;
    }

    public void setCooperatorId(String cooperatorId) {
        this.cooperatorId = cooperatorId == null ? null : cooperatorId.trim();
    }

    public String getCooperatorName() {
        return cooperatorName;
    }

    public OrderHenanZw withCooperatorName(String cooperatorName) {
        this.setCooperatorName(cooperatorName);
        return this;
    }

    public void setCooperatorName(String cooperatorName) {
        this.cooperatorName = cooperatorName == null ? null : cooperatorName.trim();
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public OrderHenanZw withBusinessType(Integer businessType) {
        this.setBusinessType(businessType);
        return this;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getCustomContact() {
        return customContact;
    }

    public OrderHenanZw withCustomContact(String customContact) {
        this.setCustomContact(customContact);
        return this;
    }

    public void setCustomContact(String customContact) {
        this.customContact = customContact == null ? null : customContact.trim();
    }

    public String getCustomContactPhone() {
        return customContactPhone;
    }

    public OrderHenanZw withCustomContactPhone(String customContactPhone) {
        this.setCustomContactPhone(customContactPhone);
        return this;
    }

    public void setCustomContactPhone(String customContactPhone) {
        this.customContactPhone = customContactPhone == null ? null : customContactPhone.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public OrderHenanZw withProvinceCode(String provinceCode) {
        this.setProvinceCode(provinceCode);
        return this;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getProvinceName() {
        return provinceName;
    }

    public OrderHenanZw withProvinceName(String provinceName) {
        this.setProvinceName(provinceName);
        return this;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public OrderHenanZw withCityCode(String cityCode) {
        this.setCityCode(cityCode);
        return this;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getCityName() {
        return cityName;
    }

    public OrderHenanZw withCityName(String cityName) {
        this.setCityName(cityName);
        return this;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    public String getAreaCode() {
        return areaCode;
    }

    public OrderHenanZw withAreaCode(String areaCode) {
        this.setAreaCode(areaCode);
        return this;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode == null ? null : areaCode.trim();
    }

    public String getAreaName() {
        return areaName;
    }

    public OrderHenanZw withAreaName(String areaName) {
        this.setAreaName(areaName);
        return this;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName == null ? null : areaName.trim();
    }

    public String getAddress() {
        return address;
    }

    public OrderHenanZw withAddress(String address) {
        this.setAddress(address);
        return this;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public OrderHenanZw withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public OrderHenanZw withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", sheetNo=").append(sheetNo);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", spuName=").append(spuName);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", spuOfferingClass=").append(spuOfferingClass);
        sb.append(", skuName=").append(skuName);
        sb.append(", skuCode=").append(skuCode);
        sb.append(", atomName=").append(atomName);
        sb.append(", atomCode=").append(atomCode);
        sb.append(", atomOfferingClass=").append(atomOfferingClass);
        sb.append(", model=").append(model);
        sb.append(", sn=").append(sn);
        sb.append(", quantity=").append(quantity);
        sb.append(", price=").append(price);
        sb.append(", status=").append(status);
        sb.append(", sendTime=").append(sendTime);
        sb.append(", cooperatorId=").append(cooperatorId);
        sb.append(", cooperatorName=").append(cooperatorName);
        sb.append(", businessType=").append(businessType);
        sb.append(", customContact=").append(customContact);
        sb.append(", customContactPhone=").append(customContactPhone);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", cityName=").append(cityName);
        sb.append(", areaCode=").append(areaCode);
        sb.append(", areaName=").append(areaName);
        sb.append(", address=").append(address);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OrderHenanZw other = (OrderHenanZw) that;
        return (this.getSheetNo() == null ? other.getSheetNo() == null : this.getSheetNo().equals(other.getSheetNo()))
            && (this.getOrderNo() == null ? other.getOrderNo() == null : this.getOrderNo().equals(other.getOrderNo()))
            && (this.getSpuName() == null ? other.getSpuName() == null : this.getSpuName().equals(other.getSpuName()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getSpuOfferingClass() == null ? other.getSpuOfferingClass() == null : this.getSpuOfferingClass().equals(other.getSpuOfferingClass()))
            && (this.getSkuName() == null ? other.getSkuName() == null : this.getSkuName().equals(other.getSkuName()))
            && (this.getSkuCode() == null ? other.getSkuCode() == null : this.getSkuCode().equals(other.getSkuCode()))
            && (this.getAtomName() == null ? other.getAtomName() == null : this.getAtomName().equals(other.getAtomName()))
            && (this.getAtomCode() == null ? other.getAtomCode() == null : this.getAtomCode().equals(other.getAtomCode()))
            && (this.getAtomOfferingClass() == null ? other.getAtomOfferingClass() == null : this.getAtomOfferingClass().equals(other.getAtomOfferingClass()))
            && (this.getModel() == null ? other.getModel() == null : this.getModel().equals(other.getModel()))
            && (this.getSn() == null ? other.getSn() == null : this.getSn().equals(other.getSn()))
            && (this.getQuantity() == null ? other.getQuantity() == null : this.getQuantity().equals(other.getQuantity()))
            && (this.getPrice() == null ? other.getPrice() == null : this.getPrice().equals(other.getPrice()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getSendTime() == null ? other.getSendTime() == null : this.getSendTime().equals(other.getSendTime()))
            && (this.getCooperatorId() == null ? other.getCooperatorId() == null : this.getCooperatorId().equals(other.getCooperatorId()))
            && (this.getCooperatorName() == null ? other.getCooperatorName() == null : this.getCooperatorName().equals(other.getCooperatorName()))
            && (this.getBusinessType() == null ? other.getBusinessType() == null : this.getBusinessType().equals(other.getBusinessType()))
            && (this.getCustomContact() == null ? other.getCustomContact() == null : this.getCustomContact().equals(other.getCustomContact()))
            && (this.getCustomContactPhone() == null ? other.getCustomContactPhone() == null : this.getCustomContactPhone().equals(other.getCustomContactPhone()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getCityCode() == null ? other.getCityCode() == null : this.getCityCode().equals(other.getCityCode()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getAreaCode() == null ? other.getAreaCode() == null : this.getAreaCode().equals(other.getAreaCode()))
            && (this.getAreaName() == null ? other.getAreaName() == null : this.getAreaName().equals(other.getAreaName()))
            && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getSheetNo() == null) ? 0 : getSheetNo().hashCode());
        result = prime * result + ((getOrderNo() == null) ? 0 : getOrderNo().hashCode());
        result = prime * result + ((getSpuName() == null) ? 0 : getSpuName().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getSpuOfferingClass() == null) ? 0 : getSpuOfferingClass().hashCode());
        result = prime * result + ((getSkuName() == null) ? 0 : getSkuName().hashCode());
        result = prime * result + ((getSkuCode() == null) ? 0 : getSkuCode().hashCode());
        result = prime * result + ((getAtomName() == null) ? 0 : getAtomName().hashCode());
        result = prime * result + ((getAtomCode() == null) ? 0 : getAtomCode().hashCode());
        result = prime * result + ((getAtomOfferingClass() == null) ? 0 : getAtomOfferingClass().hashCode());
        result = prime * result + ((getModel() == null) ? 0 : getModel().hashCode());
        result = prime * result + ((getSn() == null) ? 0 : getSn().hashCode());
        result = prime * result + ((getQuantity() == null) ? 0 : getQuantity().hashCode());
        result = prime * result + ((getPrice() == null) ? 0 : getPrice().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getSendTime() == null) ? 0 : getSendTime().hashCode());
        result = prime * result + ((getCooperatorId() == null) ? 0 : getCooperatorId().hashCode());
        result = prime * result + ((getCooperatorName() == null) ? 0 : getCooperatorName().hashCode());
        result = prime * result + ((getBusinessType() == null) ? 0 : getBusinessType().hashCode());
        result = prime * result + ((getCustomContact() == null) ? 0 : getCustomContact().hashCode());
        result = prime * result + ((getCustomContactPhone() == null) ? 0 : getCustomContactPhone().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getCityCode() == null) ? 0 : getCityCode().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getAreaCode() == null) ? 0 : getAreaCode().hashCode());
        result = prime * result + ((getAreaName() == null) ? 0 : getAreaName().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    public enum Column {
        sheetNo("sheet_no", "sheetNo", "VARCHAR", false),
        orderNo("order_no", "orderNo", "VARCHAR", false),
        spuName("spu_name", "spuName", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        spuOfferingClass("spu_offering_class", "spuOfferingClass", "VARCHAR", false),
        skuName("sku_name", "skuName", "VARCHAR", false),
        skuCode("sku_code", "skuCode", "VARCHAR", false),
        atomName("atom_name", "atomName", "VARCHAR", false),
        atomCode("atom_code", "atomCode", "VARCHAR", false),
        atomOfferingClass("atom_offering_class", "atomOfferingClass", "VARCHAR", false),
        model("model", "model", "VARCHAR", false),
        sn("sn", "sn", "VARCHAR", false),
        quantity("quantity", "quantity", "BIGINT", false),
        price("price", "price", "BIGINT", false),
        status("status", "status", "INTEGER", false),
        sendTime("send_time", "sendTime", "TIMESTAMP", false),
        cooperatorId("cooperator_id", "cooperatorId", "VARCHAR", false),
        cooperatorName("cooperator_name", "cooperatorName", "VARCHAR", false),
        businessType("business_type", "businessType", "INTEGER", false),
        customContact("custom_contact", "customContact", "VARCHAR", false),
        customContactPhone("custom_contact_phone", "customContactPhone", "VARCHAR", false),
        provinceCode("province_code", "provinceCode", "VARCHAR", false),
        provinceName("province_name", "provinceName", "VARCHAR", false),
        cityCode("city_code", "cityCode", "VARCHAR", false),
        cityName("city_name", "cityName", "VARCHAR", false),
        areaCode("area_code", "areaCode", "VARCHAR", false),
        areaName("area_name", "areaName", "VARCHAR", false),
        address("address", "address", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}