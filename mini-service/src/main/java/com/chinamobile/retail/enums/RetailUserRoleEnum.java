package com.chinamobile.retail.enums;

/**
 * 分销中心合伙人角色类型
 */
public enum RetailUserRoleEnum {

//    MANAGER(0,"客户经理"),
    FIRST_LEVEL_DISTRIBUTOR(1,"一级分销员"),
    SECOND_LEVEL_DISTRIBUTOR(2,"二级分销员"),
    AGENT(3,"渠道商"),
    ;

    public Integer code;
    public String name;

    RetailUserRoleEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static RetailUserRoleEnum fromCode(Integer code){
        RetailUserRoleEnum[] values = RetailUserRoleEnum.values();
        for (RetailUserRoleEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return value;
            }
        }
        return null;
    }
}
