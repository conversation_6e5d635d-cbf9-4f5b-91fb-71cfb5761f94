package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramHomeInfo;
import com.chinamobile.retail.pojo.entity.MiniProgramHomeInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramHomeInfoMapper {
    /**
     *
     * @mbg.generated Mon Oct 14 11:36:38 CST 2024
     */
    long countByExample(MiniProgramHomeInfoExample example);

    /**
     *
     * @mbg.generated Mon Oct 14 11:36:38 CST 2024
     */
    int deleteByExample(MiniProgramHomeInfoExample example);

    /**
     *
     * @mbg.generated Mon Oct 14 11:36:38 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Oct 14 11:36:38 CST 2024
     */
    int insert(MiniProgramHomeInfo record);

    /**
     *
     * @mbg.generated Mon Oct 14 11:36:38 CST 2024
     */
    int insertSelective(MiniProgramHomeInfo record);

    /**
     *
     * @mbg.generated Mon Oct 14 11:36:38 CST 2024
     */
    List<MiniProgramHomeInfo> selectByExample(MiniProgramHomeInfoExample example);

    /**
     *
     * @mbg.generated Mon Oct 14 11:36:38 CST 2024
     */
    MiniProgramHomeInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Oct 14 11:36:38 CST 2024
     */
    int updateByExampleSelective(@Param("record") MiniProgramHomeInfo record, @Param("example") MiniProgramHomeInfoExample example);

    /**
     *
     * @mbg.generated Mon Oct 14 11:36:38 CST 2024
     */
    int updateByExample(@Param("record") MiniProgramHomeInfo record, @Param("example") MiniProgramHomeInfoExample example);

    /**
     *
     * @mbg.generated Mon Oct 14 11:36:38 CST 2024
     */
    int updateByPrimaryKeySelective(MiniProgramHomeInfo record);

    /**
     *
     * @mbg.generated Mon Oct 14 11:36:38 CST 2024
     */
    int updateByPrimaryKey(MiniProgramHomeInfo record);

    /**
     *
     * @mbg.generated Mon Oct 14 11:36:38 CST 2024
     */
    int batchInsert(@Param("list") List<MiniProgramHomeInfo> list);

    /**
     *
     * @mbg.generated Mon Oct 14 11:36:38 CST 2024
     */
    int batchInsertSelective(@Param("list") List<MiniProgramHomeInfo> list, @Param("selective") MiniProgramHomeInfo.Column ... selective);
}