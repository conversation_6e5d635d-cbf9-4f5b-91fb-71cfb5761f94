package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsTemplateQuestion;
import com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsTemplateQuestionExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramSceneRequirementsTemplateQuestionMapper {
    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    long countByExample(MiniProgramSceneRequirementsTemplateQuestionExample example);

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    int deleteByExample(MiniProgramSceneRequirementsTemplateQuestionExample example);

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    int insert(MiniProgramSceneRequirementsTemplateQuestion record);

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    int insertSelective(MiniProgramSceneRequirementsTemplateQuestion record);

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    List<MiniProgramSceneRequirementsTemplateQuestion> selectByExample(MiniProgramSceneRequirementsTemplateQuestionExample example);

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    MiniProgramSceneRequirementsTemplateQuestion selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    int updateByExampleSelective(@Param("record") MiniProgramSceneRequirementsTemplateQuestion record, @Param("example") MiniProgramSceneRequirementsTemplateQuestionExample example);

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    int updateByExample(@Param("record") MiniProgramSceneRequirementsTemplateQuestion record, @Param("example") MiniProgramSceneRequirementsTemplateQuestionExample example);

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    int updateByPrimaryKeySelective(MiniProgramSceneRequirementsTemplateQuestion record);

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    int updateByPrimaryKey(MiniProgramSceneRequirementsTemplateQuestion record);

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    int batchInsert(@Param("list") List<MiniProgramSceneRequirementsTemplateQuestion> list);

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    int batchInsertSelective(@Param("list") List<MiniProgramSceneRequirementsTemplateQuestion> list, @Param("selective") MiniProgramSceneRequirementsTemplateQuestion.Column ... selective);
}