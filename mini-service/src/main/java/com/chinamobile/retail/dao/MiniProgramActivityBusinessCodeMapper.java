package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramActivityBusinessCode;
import com.chinamobile.retail.pojo.entity.MiniProgramActivityBusinessCodeExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramActivityBusinessCodeMapper {
    /**
     *
     * @mbg.generated Mon Jul 15 16:49:50 CST 2024
     */
    long countByExample(MiniProgramActivityBusinessCodeExample example);

    /**
     *
     * @mbg.generated Mon Jul 15 16:49:50 CST 2024
     */
    int deleteByExample(MiniProgramActivityBusinessCodeExample example);

    /**
     *
     * @mbg.generated Mon Jul 15 16:49:50 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Jul 15 16:49:50 CST 2024
     */
    int insert(MiniProgramActivityBusinessCode record);

    /**
     *
     * @mbg.generated Mon Jul 15 16:49:50 CST 2024
     */
    int insertSelective(MiniProgramActivityBusinessCode record);

    /**
     *
     * @mbg.generated Mon Jul 15 16:49:50 CST 2024
     */
    List<MiniProgramActivityBusinessCode> selectByExample(MiniProgramActivityBusinessCodeExample example);

    /**
     *
     * @mbg.generated Mon Jul 15 16:49:50 CST 2024
     */
    MiniProgramActivityBusinessCode selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Jul 15 16:49:50 CST 2024
     */
    int updateByExampleSelective(@Param("record") MiniProgramActivityBusinessCode record, @Param("example") MiniProgramActivityBusinessCodeExample example);

    /**
     *
     * @mbg.generated Mon Jul 15 16:49:50 CST 2024
     */
    int updateByExample(@Param("record") MiniProgramActivityBusinessCode record, @Param("example") MiniProgramActivityBusinessCodeExample example);

    /**
     *
     * @mbg.generated Mon Jul 15 16:49:50 CST 2024
     */
    int updateByPrimaryKeySelective(MiniProgramActivityBusinessCode record);

    /**
     *
     * @mbg.generated Mon Jul 15 16:49:50 CST 2024
     */
    int updateByPrimaryKey(MiniProgramActivityBusinessCode record);

    /**
     *
     * @mbg.generated Mon Jul 15 16:49:50 CST 2024
     */
    int batchInsert(@Param("list") List<MiniProgramActivityBusinessCode> list);

    /**
     *
     * @mbg.generated Mon Jul 15 16:49:50 CST 2024
     */
    int batchInsertSelective(@Param("list") List<MiniProgramActivityBusinessCode> list, @Param("selective") MiniProgramActivityBusinessCode.Column ... selective);
}