package com.chinamobile.retail.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 合伙人积分查询参数
 * <AUTHOR>
 */
@Data
public class PartnerPointQueryParam {

    /**合伙人类型*/
    private List<Integer> roleTypes;

    /**合伙人省份*/
    private List<String> provinces;

    /**合伙人城市*/
    private List<String> cities;

    /**积分供应商*/
    private List<String> suppliers;

    /**搜索关键字*/
    private String searchKey;

    //是否可兑换（可兑换积分>0）
    private Boolean availablePoint;

    private Integer page;

    private Integer pageSize;
    /**积分渠道*/
    private Integer channel;


}
