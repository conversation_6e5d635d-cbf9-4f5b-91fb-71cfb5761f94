package com.chinamobile.retail.pojo.vo;

import lombok.Data;

import java.util.Date;

@Data
public class AfterMarketOrderInfoVO {

    /**
     * 售后订单ID
     */
    private String serviceOrderId;

    /**
     * 售后服务订单状态:
     * 1:待预约 -对应商城同步状态【1待预约（付款完成）】
     * 11:待分派-装维管理员接单时，用户提交预约申请后，售后服务订单状态由“待预约”变为“待分派”，装维管理员完成订单分派后，订单状态由“待分派”变为“待派单”
     * 2:派单中-对应商城同步状态【2派单中（预约完成）】
     * 3:已预约
     * 31：已签到
     * 4.已完结（成功）
     * 5.已完成（失败）
     * 6.已撤销
     * 7.交易完成-对应商城同步状态【3订单计收（订单同步至CMIoT成功后，同步本状态）】
     * 8.交易失败-对应商城同步状态【4退款完成】
     */
    private Integer status;

    /**
     * 预约时间
     */
    private Date appointmentTime;

    /**
     * 订购数量
     */
    private Long quantity;

    /**
     * 订单总金额
     */
    private String totalPrice;

    /**
     * 售后商品名称
     */
    private String afterMarketName;
}
