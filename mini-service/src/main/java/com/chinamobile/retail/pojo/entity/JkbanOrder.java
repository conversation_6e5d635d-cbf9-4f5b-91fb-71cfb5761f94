package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 
 *
 * <AUTHOR>
public class JkbanOrder implements Serializable {
    /**
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String id;

    /**
     * 物联卡号码
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String msisdn;

    /**
     * 集成电路卡识别码
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String iccid;

    /**
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String orderId;

    /**
     * 在线公司入库省份编码
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String orderProviceCode;

    /**
     * 0 工单保存 1 作废 2 归档 3 初始化 4 活动 5 休眠
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String orderStatus;

    /**
     * 故障业务
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String questionType;

    /**
     * 故障类型,编码
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String question;

    /**
     * 联系人手机号
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String contactPhone;

    /**
     * 省编码
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String provinceCode;

    /**
     * 地市编码
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String cityCode;

    /**
     * 区编码
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String regionCode;

    /**
     * 详细地址
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String address;

    /**
     * 提单人手机号
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String userPhone;

    /**
     * 问题时间
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private Date questionTime;

    /**
     * 问题描述
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String questionDesc;

    /**
     * 短信接入码，故障类型为短信时填入
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String smsAcptCode;

    /**
     * 对端号码，故障类型为普通语音填入
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String oppositeNumber;

    /**
     * onelink账号,平台报表问题 、OneLink平台登录问题  、OneLink平台功能使用问题填入
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String onelinkAccount;

    /**
     * 调用url,API调用失败、API调用时间过长、API调用数据不准确填入
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String callUrl;

    /**
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private Date updateTime;

    /**
     * 用户id
     *
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private String userId;

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..jkban_order.id
     *
     * @return the value of supply_chain..jkban_order.id
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.id
     *
     * @param id the value for supply_chain..jkban_order.id
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.msisdn
     *
     * @return the value of supply_chain..jkban_order.msisdn
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getMsisdn() {
        return msisdn;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withMsisdn(String msisdn) {
        this.setMsisdn(msisdn);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.msisdn
     *
     * @param msisdn the value for supply_chain..jkban_order.msisdn
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.iccid
     *
     * @return the value of supply_chain..jkban_order.iccid
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getIccid() {
        return iccid;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withIccid(String iccid) {
        this.setIccid(iccid);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.iccid
     *
     * @param iccid the value for supply_chain..jkban_order.iccid
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setIccid(String iccid) {
        this.iccid = iccid;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.order_id
     *
     * @return the value of supply_chain..jkban_order.order_id
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.order_id
     *
     * @param orderId the value for supply_chain..jkban_order.order_id
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.order_provice_code
     *
     * @return the value of supply_chain..jkban_order.order_provice_code
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getOrderProviceCode() {
        return orderProviceCode;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withOrderProviceCode(String orderProviceCode) {
        this.setOrderProviceCode(orderProviceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.order_provice_code
     *
     * @param orderProviceCode the value for supply_chain..jkban_order.order_provice_code
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setOrderProviceCode(String orderProviceCode) {
        this.orderProviceCode = orderProviceCode;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.order_status
     *
     * @return the value of supply_chain..jkban_order.order_status
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getOrderStatus() {
        return orderStatus;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withOrderStatus(String orderStatus) {
        this.setOrderStatus(orderStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.order_status
     *
     * @param orderStatus the value for supply_chain..jkban_order.order_status
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.question_type
     *
     * @return the value of supply_chain..jkban_order.question_type
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getQuestionType() {
        return questionType;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withQuestionType(String questionType) {
        this.setQuestionType(questionType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.question_type
     *
     * @param questionType the value for supply_chain..jkban_order.question_type
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.question
     *
     * @return the value of supply_chain..jkban_order.question
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getQuestion() {
        return question;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withQuestion(String question) {
        this.setQuestion(question);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.question
     *
     * @param question the value for supply_chain..jkban_order.question
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setQuestion(String question) {
        this.question = question;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.contact_phone
     *
     * @return the value of supply_chain..jkban_order.contact_phone
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getContactPhone() {
        return contactPhone;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withContactPhone(String contactPhone) {
        this.setContactPhone(contactPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.contact_phone
     *
     * @param contactPhone the value for supply_chain..jkban_order.contact_phone
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.province_code
     *
     * @return the value of supply_chain..jkban_order.province_code
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withProvinceCode(String provinceCode) {
        this.setProvinceCode(provinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.province_code
     *
     * @param provinceCode the value for supply_chain..jkban_order.province_code
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.city_code
     *
     * @return the value of supply_chain..jkban_order.city_code
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withCityCode(String cityCode) {
        this.setCityCode(cityCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.city_code
     *
     * @param cityCode the value for supply_chain..jkban_order.city_code
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.region_code
     *
     * @return the value of supply_chain..jkban_order.region_code
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getRegionCode() {
        return regionCode;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withRegionCode(String regionCode) {
        this.setRegionCode(regionCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.region_code
     *
     * @param regionCode the value for supply_chain..jkban_order.region_code
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.address
     *
     * @return the value of supply_chain..jkban_order.address
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getAddress() {
        return address;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withAddress(String address) {
        this.setAddress(address);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.address
     *
     * @param address the value for supply_chain..jkban_order.address
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.user_phone
     *
     * @return the value of supply_chain..jkban_order.user_phone
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getUserPhone() {
        return userPhone;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withUserPhone(String userPhone) {
        this.setUserPhone(userPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.user_phone
     *
     * @param userPhone the value for supply_chain..jkban_order.user_phone
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.question_time
     *
     * @return the value of supply_chain..jkban_order.question_time
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public Date getQuestionTime() {
        return questionTime;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withQuestionTime(Date questionTime) {
        this.setQuestionTime(questionTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.question_time
     *
     * @param questionTime the value for supply_chain..jkban_order.question_time
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setQuestionTime(Date questionTime) {
        this.questionTime = questionTime;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.question_desc
     *
     * @return the value of supply_chain..jkban_order.question_desc
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getQuestionDesc() {
        return questionDesc;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withQuestionDesc(String questionDesc) {
        this.setQuestionDesc(questionDesc);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.question_desc
     *
     * @param questionDesc the value for supply_chain..jkban_order.question_desc
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setQuestionDesc(String questionDesc) {
        this.questionDesc = questionDesc;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.sms_acpt_code
     *
     * @return the value of supply_chain..jkban_order.sms_acpt_code
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getSmsAcptCode() {
        return smsAcptCode;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withSmsAcptCode(String smsAcptCode) {
        this.setSmsAcptCode(smsAcptCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.sms_acpt_code
     *
     * @param smsAcptCode the value for supply_chain..jkban_order.sms_acpt_code
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setSmsAcptCode(String smsAcptCode) {
        this.smsAcptCode = smsAcptCode;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.opposite_number
     *
     * @return the value of supply_chain..jkban_order.opposite_number
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getOppositeNumber() {
        return oppositeNumber;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withOppositeNumber(String oppositeNumber) {
        this.setOppositeNumber(oppositeNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.opposite_number
     *
     * @param oppositeNumber the value for supply_chain..jkban_order.opposite_number
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setOppositeNumber(String oppositeNumber) {
        this.oppositeNumber = oppositeNumber;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.onelink_account
     *
     * @return the value of supply_chain..jkban_order.onelink_account
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getOnelinkAccount() {
        return onelinkAccount;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withOnelinkAccount(String onelinkAccount) {
        this.setOnelinkAccount(onelinkAccount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.onelink_account
     *
     * @param onelinkAccount the value for supply_chain..jkban_order.onelink_account
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setOnelinkAccount(String onelinkAccount) {
        this.onelinkAccount = onelinkAccount;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.call_url
     *
     * @return the value of supply_chain..jkban_order.call_url
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getCallUrl() {
        return callUrl;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withCallUrl(String callUrl) {
        this.setCallUrl(callUrl);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.call_url
     *
     * @param callUrl the value for supply_chain..jkban_order.call_url
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setCallUrl(String callUrl) {
        this.callUrl = callUrl;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.create_time
     *
     * @return the value of supply_chain..jkban_order.create_time
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.create_time
     *
     * @param createTime the value for supply_chain..jkban_order.create_time
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.update_time
     *
     * @return the value of supply_chain..jkban_order.update_time
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.update_time
     *
     * @param updateTime the value for supply_chain..jkban_order.update_time
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..jkban_order.user_id
     *
     * @return the value of supply_chain..jkban_order.user_id
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public JkbanOrder withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..jkban_order.user_id
     *
     * @param userId the value for supply_chain..jkban_order.user_id
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", msisdn=").append(msisdn);
        sb.append(", iccid=").append(iccid);
        sb.append(", orderId=").append(orderId);
        sb.append(", orderProviceCode=").append(orderProviceCode);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", questionType=").append(questionType);
        sb.append(", question=").append(question);
        sb.append(", contactPhone=").append(contactPhone);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", regionCode=").append(regionCode);
        sb.append(", address=").append(address);
        sb.append(", userPhone=").append(userPhone);
        sb.append(", questionTime=").append(questionTime);
        sb.append(", questionDesc=").append(questionDesc);
        sb.append(", smsAcptCode=").append(smsAcptCode);
        sb.append(", oppositeNumber=").append(oppositeNumber);
        sb.append(", onelinkAccount=").append(onelinkAccount);
        sb.append(", callUrl=").append(callUrl);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", userId=").append(userId);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        JkbanOrder other = (JkbanOrder) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getMsisdn() == null ? other.getMsisdn() == null : this.getMsisdn().equals(other.getMsisdn()))
            && (this.getIccid() == null ? other.getIccid() == null : this.getIccid().equals(other.getIccid()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getOrderProviceCode() == null ? other.getOrderProviceCode() == null : this.getOrderProviceCode().equals(other.getOrderProviceCode()))
            && (this.getOrderStatus() == null ? other.getOrderStatus() == null : this.getOrderStatus().equals(other.getOrderStatus()))
            && (this.getQuestionType() == null ? other.getQuestionType() == null : this.getQuestionType().equals(other.getQuestionType()))
            && (this.getQuestion() == null ? other.getQuestion() == null : this.getQuestion().equals(other.getQuestion()))
            && (this.getContactPhone() == null ? other.getContactPhone() == null : this.getContactPhone().equals(other.getContactPhone()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getCityCode() == null ? other.getCityCode() == null : this.getCityCode().equals(other.getCityCode()))
            && (this.getRegionCode() == null ? other.getRegionCode() == null : this.getRegionCode().equals(other.getRegionCode()))
            && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
            && (this.getUserPhone() == null ? other.getUserPhone() == null : this.getUserPhone().equals(other.getUserPhone()))
            && (this.getQuestionTime() == null ? other.getQuestionTime() == null : this.getQuestionTime().equals(other.getQuestionTime()))
            && (this.getQuestionDesc() == null ? other.getQuestionDesc() == null : this.getQuestionDesc().equals(other.getQuestionDesc()))
            && (this.getSmsAcptCode() == null ? other.getSmsAcptCode() == null : this.getSmsAcptCode().equals(other.getSmsAcptCode()))
            && (this.getOppositeNumber() == null ? other.getOppositeNumber() == null : this.getOppositeNumber().equals(other.getOppositeNumber()))
            && (this.getOnelinkAccount() == null ? other.getOnelinkAccount() == null : this.getOnelinkAccount().equals(other.getOnelinkAccount()))
            && (this.getCallUrl() == null ? other.getCallUrl() == null : this.getCallUrl().equals(other.getCallUrl()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()));
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getMsisdn() == null) ? 0 : getMsisdn().hashCode());
        result = prime * result + ((getIccid() == null) ? 0 : getIccid().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getOrderProviceCode() == null) ? 0 : getOrderProviceCode().hashCode());
        result = prime * result + ((getOrderStatus() == null) ? 0 : getOrderStatus().hashCode());
        result = prime * result + ((getQuestionType() == null) ? 0 : getQuestionType().hashCode());
        result = prime * result + ((getQuestion() == null) ? 0 : getQuestion().hashCode());
        result = prime * result + ((getContactPhone() == null) ? 0 : getContactPhone().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getCityCode() == null) ? 0 : getCityCode().hashCode());
        result = prime * result + ((getRegionCode() == null) ? 0 : getRegionCode().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getUserPhone() == null) ? 0 : getUserPhone().hashCode());
        result = prime * result + ((getQuestionTime() == null) ? 0 : getQuestionTime().hashCode());
        result = prime * result + ((getQuestionDesc() == null) ? 0 : getQuestionDesc().hashCode());
        result = prime * result + ((getSmsAcptCode() == null) ? 0 : getSmsAcptCode().hashCode());
        result = prime * result + ((getOppositeNumber() == null) ? 0 : getOppositeNumber().hashCode());
        result = prime * result + ((getOnelinkAccount() == null) ? 0 : getOnelinkAccount().hashCode());
        result = prime * result + ((getCallUrl() == null) ? 0 : getCallUrl().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Jul 23 09:21:05 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        msisdn("msisdn", "msisdn", "VARCHAR", false),
        iccid("iccid", "iccid", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        orderProviceCode("order_provice_code", "orderProviceCode", "VARCHAR", false),
        orderStatus("order_status", "orderStatus", "VARCHAR", false),
        questionType("question_type", "questionType", "VARCHAR", false),
        question("question", "question", "VARCHAR", false),
        contactPhone("contact_phone", "contactPhone", "VARCHAR", false),
        provinceCode("province_code", "provinceCode", "VARCHAR", false),
        cityCode("city_code", "cityCode", "VARCHAR", false),
        regionCode("region_code", "regionCode", "VARCHAR", false),
        address("address", "address", "VARCHAR", false),
        userPhone("user_phone", "userPhone", "VARCHAR", false),
        questionTime("question_time", "questionTime", "TIMESTAMP", false),
        questionDesc("question_desc", "questionDesc", "VARCHAR", false),
        smsAcptCode("sms_acpt_code", "smsAcptCode", "VARCHAR", false),
        oppositeNumber("opposite_number", "oppositeNumber", "VARCHAR", false),
        onelinkAccount("onelink_account", "onelinkAccount", "VARCHAR", false),
        callUrl("call_url", "callUrl", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        userId("user_id", "userId", "VARCHAR", false);

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Jul 23 09:21:05 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}