package com.chinamobile.retail.pojo.param;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * created by l<PERSON>xiang on 2022/11/9 15:20
 */
@Data
public class PointExchangeItem {
    @NotNull(message = "积分供应商不能为空")
    private String supplierId;

    @Valid
    @NotNull(message = "合伙人列表不能为空")
    private List<PartnerPointItem> partnerPointList;
}
