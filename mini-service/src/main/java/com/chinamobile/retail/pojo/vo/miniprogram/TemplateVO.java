package com.chinamobile.retail.pojo.vo.miniprogram;

import com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsTemplateQuestion;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 小程序场景需求模版
 *
 * <AUTHOR>
@Data
public class TemplateVO implements Serializable {
    /**
     * 主键

     */
    private String id;

    /**
     * 模版名称
     */
    private String name;

    /**
     * 创建人用户id
     */
    private String createUid;

    /**
     * 是否已删除，0-否，1-是
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     *
     */
    private String createUserName;


    /**
     * 问题列表
     *
     */
    private List<MiniProgramSceneRequirementsTemplateQuestion> questions;
}