package com.chinamobile.retail.pojo.param.miniprogram;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 16:47
 * @description TODO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageRequirementListParam extends BasePageQuery {


    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 名称
     */
    private String sceneName;

    /**
     * 所属目录
     */
    private String dirName;

    /**
     * 审核状态 0-待审核(前端不可见)，1-待审核（前端可见），2-审核通过，3-驳回
     */
    private List<Integer> auditStatusList;

    /**
     * 商机经理用户id
     */
    private String partnerBusinessId;




}
