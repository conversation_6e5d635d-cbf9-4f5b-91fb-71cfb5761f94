package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 小程序首页广告
 *
 * <AUTHOR>
public class MiniProgramHomeAd implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    private String id;

    /**
     * 名称
     *
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    private String name;

    /**
     * 主页id
     *
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    private String homeId;

    /**
     * 广告图片链接
     *
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    private String imgUrl;

    /**
     * 品牌专区ID
     *
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    private String activityId;

    /**
     * 品牌专区名称
     *
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    private String activityName;

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_home_ad.id
     *
     * @return the value of supply_chain..mini_program_home_ad.id
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public MiniProgramHomeAd withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home_ad.id
     *
     * @param id the value for supply_chain..mini_program_home_ad.id
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_home_ad.name
     *
     * @return the value of supply_chain..mini_program_home_ad.name
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public MiniProgramHomeAd withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home_ad.name
     *
     * @param name the value for supply_chain..mini_program_home_ad.name
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_home_ad.home_id
     *
     * @return the value of supply_chain..mini_program_home_ad.home_id
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public String getHomeId() {
        return homeId;
    }

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public MiniProgramHomeAd withHomeId(String homeId) {
        this.setHomeId(homeId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home_ad.home_id
     *
     * @param homeId the value for supply_chain..mini_program_home_ad.home_id
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public void setHomeId(String homeId) {
        this.homeId = homeId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_home_ad.img_url
     *
     * @return the value of supply_chain..mini_program_home_ad.img_url
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public String getImgUrl() {
        return imgUrl;
    }

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public MiniProgramHomeAd withImgUrl(String imgUrl) {
        this.setImgUrl(imgUrl);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home_ad.img_url
     *
     * @param imgUrl the value for supply_chain..mini_program_home_ad.img_url
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_home_ad.activity_id
     *
     * @return the value of supply_chain..mini_program_home_ad.activity_id
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public String getActivityId() {
        return activityId;
    }

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public MiniProgramHomeAd withActivityId(String activityId) {
        this.setActivityId(activityId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home_ad.activity_id
     *
     * @param activityId the value for supply_chain..mini_program_home_ad.activity_id
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_home_ad.activity_name
     *
     * @return the value of supply_chain..mini_program_home_ad.activity_name
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public String getActivityName() {
        return activityName;
    }

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public MiniProgramHomeAd withActivityName(String activityName) {
        this.setActivityName(activityName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home_ad.activity_name
     *
     * @param activityName the value for supply_chain..mini_program_home_ad.activity_name
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", homeId=").append(homeId);
        sb.append(", imgUrl=").append(imgUrl);
        sb.append(", activityId=").append(activityId);
        sb.append(", activityName=").append(activityName);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramHomeAd other = (MiniProgramHomeAd) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getHomeId() == null ? other.getHomeId() == null : this.getHomeId().equals(other.getHomeId()))
            && (this.getImgUrl() == null ? other.getImgUrl() == null : this.getImgUrl().equals(other.getImgUrl()))
            && (this.getActivityId() == null ? other.getActivityId() == null : this.getActivityId().equals(other.getActivityId()))
            && (this.getActivityName() == null ? other.getActivityName() == null : this.getActivityName().equals(other.getActivityName()));
    }

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getHomeId() == null) ? 0 : getHomeId().hashCode());
        result = prime * result + ((getImgUrl() == null) ? 0 : getImgUrl().hashCode());
        result = prime * result + ((getActivityId() == null) ? 0 : getActivityId().hashCode());
        result = prime * result + ((getActivityName() == null) ? 0 : getActivityName().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        homeId("home_id", "homeId", "VARCHAR", false),
        imgUrl("img_url", "imgUrl", "VARCHAR", false),
        activityId("activity_id", "activityId", "VARCHAR", false),
        activityName("activity_name", "activityName", "VARCHAR", false);

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Oct 21 11:10:57 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}