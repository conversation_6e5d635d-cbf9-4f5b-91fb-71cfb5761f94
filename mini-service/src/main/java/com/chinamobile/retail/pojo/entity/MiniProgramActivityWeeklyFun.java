package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 销售端小程序周周乐信息
 *
 * <AUTHOR>
public class MiniProgramActivityWeeklyFun {
    /**
     * 主键
     *
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    private String id;

    /**
     * 小程序活动id
     *
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    private String activityId;

    /**
     * 活动规则
     *
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    private String rule;

    /**
     * 活动说明
     *
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    private String description;

    /**
     * 轮盘等分数量
     *
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    private Integer wheelParts;

    /**
     * 订单开始时间
     *
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    private Date orderStart;

    /**
     * 订单结束时间
     *
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    private Date orderStop;

    /**
     * 参加活动需要完成的订单数量
     *
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    private Integer orderCount;

    /**
     * 销售人员注册起始时间
     *
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    private Date registerStart;

    /**
     * 销售人员注册结束时间
     *
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    private Date registerStop;

    /**
     * 最大参与人数
     *
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    private Long maxPlayer;

    /**
     * 标语
     *
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    private String slogan;

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun.id
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun.id
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFun withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun.id
     *
     * @param id the value for supply_chain..mini_program_activity_weekly_fun.id
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun.activity_id
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun.activity_id
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public String getActivityId() {
        return activityId;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFun withActivityId(String activityId) {
        this.setActivityId(activityId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun.activity_id
     *
     * @param activityId the value for supply_chain..mini_program_activity_weekly_fun.activity_id
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void setActivityId(String activityId) {
        this.activityId = activityId == null ? null : activityId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun.rule
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun.rule
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public String getRule() {
        return rule;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFun withRule(String rule) {
        this.setRule(rule);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun.rule
     *
     * @param rule the value for supply_chain..mini_program_activity_weekly_fun.rule
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void setRule(String rule) {
        this.rule = rule == null ? null : rule.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun.description
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun.description
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public String getDescription() {
        return description;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFun withDescription(String description) {
        this.setDescription(description);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun.description
     *
     * @param description the value for supply_chain..mini_program_activity_weekly_fun.description
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun.wheel_parts
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun.wheel_parts
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public Integer getWheelParts() {
        return wheelParts;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFun withWheelParts(Integer wheelParts) {
        this.setWheelParts(wheelParts);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun.wheel_parts
     *
     * @param wheelParts the value for supply_chain..mini_program_activity_weekly_fun.wheel_parts
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void setWheelParts(Integer wheelParts) {
        this.wheelParts = wheelParts;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun.order_start
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun.order_start
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public Date getOrderStart() {
        return orderStart;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFun withOrderStart(Date orderStart) {
        this.setOrderStart(orderStart);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun.order_start
     *
     * @param orderStart the value for supply_chain..mini_program_activity_weekly_fun.order_start
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void setOrderStart(Date orderStart) {
        this.orderStart = orderStart;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun.order_stop
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun.order_stop
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public Date getOrderStop() {
        return orderStop;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFun withOrderStop(Date orderStop) {
        this.setOrderStop(orderStop);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun.order_stop
     *
     * @param orderStop the value for supply_chain..mini_program_activity_weekly_fun.order_stop
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void setOrderStop(Date orderStop) {
        this.orderStop = orderStop;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun.order_count
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun.order_count
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public Integer getOrderCount() {
        return orderCount;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFun withOrderCount(Integer orderCount) {
        this.setOrderCount(orderCount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun.order_count
     *
     * @param orderCount the value for supply_chain..mini_program_activity_weekly_fun.order_count
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun.register_start
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun.register_start
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public Date getRegisterStart() {
        return registerStart;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFun withRegisterStart(Date registerStart) {
        this.setRegisterStart(registerStart);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun.register_start
     *
     * @param registerStart the value for supply_chain..mini_program_activity_weekly_fun.register_start
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void setRegisterStart(Date registerStart) {
        this.registerStart = registerStart;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun.register_stop
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun.register_stop
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public Date getRegisterStop() {
        return registerStop;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFun withRegisterStop(Date registerStop) {
        this.setRegisterStop(registerStop);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun.register_stop
     *
     * @param registerStop the value for supply_chain..mini_program_activity_weekly_fun.register_stop
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void setRegisterStop(Date registerStop) {
        this.registerStop = registerStop;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun.max_player
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun.max_player
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public Long getMaxPlayer() {
        return maxPlayer;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFun withMaxPlayer(Long maxPlayer) {
        this.setMaxPlayer(maxPlayer);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun.max_player
     *
     * @param maxPlayer the value for supply_chain..mini_program_activity_weekly_fun.max_player
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void setMaxPlayer(Long maxPlayer) {
        this.maxPlayer = maxPlayer;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun.slogan
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun.slogan
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public String getSlogan() {
        return slogan;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFun withSlogan(String slogan) {
        this.setSlogan(slogan);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun.slogan
     *
     * @param slogan the value for supply_chain..mini_program_activity_weekly_fun.slogan
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void setSlogan(String slogan) {
        this.slogan = slogan == null ? null : slogan.trim();
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", activityId=").append(activityId);
        sb.append(", rule=").append(rule);
        sb.append(", description=").append(description);
        sb.append(", wheelParts=").append(wheelParts);
        sb.append(", orderStart=").append(orderStart);
        sb.append(", orderStop=").append(orderStop);
        sb.append(", orderCount=").append(orderCount);
        sb.append(", registerStart=").append(registerStart);
        sb.append(", registerStop=").append(registerStop);
        sb.append(", maxPlayer=").append(maxPlayer);
        sb.append(", slogan=").append(slogan);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramActivityWeeklyFun other = (MiniProgramActivityWeeklyFun) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getActivityId() == null ? other.getActivityId() == null : this.getActivityId().equals(other.getActivityId()))
            && (this.getRule() == null ? other.getRule() == null : this.getRule().equals(other.getRule()))
            && (this.getDescription() == null ? other.getDescription() == null : this.getDescription().equals(other.getDescription()))
            && (this.getWheelParts() == null ? other.getWheelParts() == null : this.getWheelParts().equals(other.getWheelParts()))
            && (this.getOrderStart() == null ? other.getOrderStart() == null : this.getOrderStart().equals(other.getOrderStart()))
            && (this.getOrderStop() == null ? other.getOrderStop() == null : this.getOrderStop().equals(other.getOrderStop()))
            && (this.getOrderCount() == null ? other.getOrderCount() == null : this.getOrderCount().equals(other.getOrderCount()))
            && (this.getRegisterStart() == null ? other.getRegisterStart() == null : this.getRegisterStart().equals(other.getRegisterStart()))
            && (this.getRegisterStop() == null ? other.getRegisterStop() == null : this.getRegisterStop().equals(other.getRegisterStop()))
            && (this.getMaxPlayer() == null ? other.getMaxPlayer() == null : this.getMaxPlayer().equals(other.getMaxPlayer()))
            && (this.getSlogan() == null ? other.getSlogan() == null : this.getSlogan().equals(other.getSlogan()));
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getActivityId() == null) ? 0 : getActivityId().hashCode());
        result = prime * result + ((getRule() == null) ? 0 : getRule().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((getWheelParts() == null) ? 0 : getWheelParts().hashCode());
        result = prime * result + ((getOrderStart() == null) ? 0 : getOrderStart().hashCode());
        result = prime * result + ((getOrderStop() == null) ? 0 : getOrderStop().hashCode());
        result = prime * result + ((getOrderCount() == null) ? 0 : getOrderCount().hashCode());
        result = prime * result + ((getRegisterStart() == null) ? 0 : getRegisterStart().hashCode());
        result = prime * result + ((getRegisterStop() == null) ? 0 : getRegisterStop().hashCode());
        result = prime * result + ((getMaxPlayer() == null) ? 0 : getMaxPlayer().hashCode());
        result = prime * result + ((getSlogan() == null) ? 0 : getSlogan().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        activityId("activity_id", "activityId", "VARCHAR", false),
        rule("rule", "rule", "VARCHAR", false),
        description("description", "description", "VARCHAR", false),
        wheelParts("wheel_parts", "wheelParts", "INTEGER", false),
        orderStart("order_start", "orderStart", "TIMESTAMP", false),
        orderStop("order_stop", "orderStop", "TIMESTAMP", false),
        orderCount("order_count", "orderCount", "INTEGER", false),
        registerStart("register_start", "registerStart", "TIMESTAMP", false),
        registerStop("register_stop", "registerStop", "TIMESTAMP", false),
        maxPlayer("max_player", "maxPlayer", "BIGINT", false),
        slogan("slogan", "slogan", "VARCHAR", false);

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}