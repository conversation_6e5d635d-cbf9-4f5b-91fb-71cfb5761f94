package com.chinamobile.retail.pojo.param;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2022/8/31 15:17
 */
@Data
public class ConfigPointStatusParam {

    @NotEmpty(message = "请首先勾选条目")
    private List<String> idList;

    @NotNull(message = "积分状态不能为空")
    @Range(min = 1,max = 1,message = "积分状态的值只能是1")
    private Integer pointStatus;

}
