package com.chinamobile.retail.pojo.param.miniprogram;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/17 10:43
 * @description TODO
 */
@Data
public class InfoAuditParam {

    /**
     * 小程序活动id
     */
    @NotEmpty(message = "资讯id不能为空")
    private List<String> ids;

    /**
     * 同意/驳回，true-同意，false-驳回
     */
    @NotNull(message = "审核状态不能为空")
    private Boolean approve;
}
