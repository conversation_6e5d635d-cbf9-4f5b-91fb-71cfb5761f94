package com.chinamobile.retail.pojo.param.miniprogram;

import com.chinamobile.iot.sc.entity.UpResult;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 销售端小程序主页信息
 */
@Data
public class SkuCoreComponentParam implements Serializable {


    /**
     * spu编码
     *
     */
    private String spuCode;

    /**
     * sku编码
     *
     */
    private String skuCode;

    /**
     * sku名称
     *
     */
    private String skuName;

    /**
     * 核心部件名称
     */
    private String coreComponentName;

    /**
     * 轮播图
     */
    private List<String> coreBannerList ;

    /**
     * 视频
     */
    private List<String> coreVideoList ;


}