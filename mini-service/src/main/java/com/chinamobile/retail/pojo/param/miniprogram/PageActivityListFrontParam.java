package com.chinamobile.retail.pojo.param.miniprogram;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 16:47
 * @description TODO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageActivityListFrontParam extends BasePageQuery{
    /**
     * 活动类型
     */
    private Integer activityType;

    /**
     * 活动状态
     */
    private Integer status;

    /**
     * 是否参与
     */
    private Boolean isParticipate;
}
