package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 小程序首页
 *
 * <AUTHOR>
public class MiniProgramHome {
    /**
     * 主键
     *
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    private String id;

    /**
     * 省编码
     *
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    private String provinceCode;

    /**
     * 状态,0:已上传、1:已发布、2:已下线、3:审核中,4:已驳回
     *
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    private Integer status;

    /**
     * 审核状态 0-待审核，1-审核中，2-审核通过，3-驳回
     *
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    private Integer auditStatus;

    /**
     * 创建人ID
     *
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    private String creatorId;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    private Date updateTime;

    /**
     * 是否删除 0-未删除，1-已删除
     *
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    private Integer isDelete;

    /**
     * This method returns the value of the database column supply_chain..mini_program_home.id
     *
     * @return the value of supply_chain..mini_program_home.id
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public MiniProgramHome withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home.id
     *
     * @param id the value for supply_chain..mini_program_home.id
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_home.province_code
     *
     * @return the value of supply_chain..mini_program_home.province_code
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public MiniProgramHome withProvinceCode(String provinceCode) {
        this.setProvinceCode(provinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home.province_code
     *
     * @param provinceCode the value for supply_chain..mini_program_home.province_code
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_home.status
     *
     * @return the value of supply_chain..mini_program_home.status
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public Integer getStatus() {
        return status;
    }

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public MiniProgramHome withStatus(Integer status) {
        this.setStatus(status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home.status
     *
     * @param status the value for supply_chain..mini_program_home.status
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_home.audit_status
     *
     * @return the value of supply_chain..mini_program_home.audit_status
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public Integer getAuditStatus() {
        return auditStatus;
    }

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public MiniProgramHome withAuditStatus(Integer auditStatus) {
        this.setAuditStatus(auditStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home.audit_status
     *
     * @param auditStatus the value for supply_chain..mini_program_home.audit_status
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_home.creator_id
     *
     * @return the value of supply_chain..mini_program_home.creator_id
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public String getCreatorId() {
        return creatorId;
    }

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public MiniProgramHome withCreatorId(String creatorId) {
        this.setCreatorId(creatorId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home.creator_id
     *
     * @param creatorId the value for supply_chain..mini_program_home.creator_id
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId == null ? null : creatorId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_home.create_time
     *
     * @return the value of supply_chain..mini_program_home.create_time
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public MiniProgramHome withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home.create_time
     *
     * @param createTime the value for supply_chain..mini_program_home.create_time
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_home.update_time
     *
     * @return the value of supply_chain..mini_program_home.update_time
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public MiniProgramHome withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home.update_time
     *
     * @param updateTime the value for supply_chain..mini_program_home.update_time
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_home.is_delete
     *
     * @return the value of supply_chain..mini_program_home.is_delete
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public MiniProgramHome withIsDelete(Integer isDelete) {
        this.setIsDelete(isDelete);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_home.is_delete
     *
     * @param isDelete the value for supply_chain..mini_program_home.is_delete
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", status=").append(status);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", creatorId=").append(creatorId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramHome other = (MiniProgramHome) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getAuditStatus() == null ? other.getAuditStatus() == null : this.getAuditStatus().equals(other.getAuditStatus()))
            && (this.getCreatorId() == null ? other.getCreatorId() == null : this.getCreatorId().equals(other.getCreatorId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()));
    }

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getAuditStatus() == null) ? 0 : getAuditStatus().hashCode());
        result = prime * result + ((getCreatorId() == null) ? 0 : getCreatorId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        provinceCode("province_code", "provinceCode", "VARCHAR", false),
        status("status", "status", "INTEGER", false),
        auditStatus("audit_status", "auditStatus", "INTEGER", false),
        creatorId("creator_id", "creatorId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        isDelete("is_delete", "isDelete", "INTEGER", false);

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Dec 04 09:54:29 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}