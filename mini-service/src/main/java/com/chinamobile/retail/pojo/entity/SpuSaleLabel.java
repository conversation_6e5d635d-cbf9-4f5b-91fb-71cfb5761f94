package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * spu销售标签表
 *
 * <AUTHOR>
public class SpuSaleLabel implements Serializable {
    /**
     *
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    private String id;

    /**
     * spu编码
     *
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    private String spuCode;

    /**
     * 标签
     *
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    private String label;

    /**
     * 0-主标签,1-副标签
     *
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    private String type;

    /**
     *
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..spu_sale_label.id
     *
     * @return the value of supply_chain..spu_sale_label.id
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public SpuSaleLabel withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_sale_label.id
     *
     * @param id the value for supply_chain..spu_sale_label.id
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_sale_label.spu_code
     *
     * @return the value of supply_chain..spu_sale_label.spu_code
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public String getSpuCode() {
        return spuCode;
    }

    /**
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public SpuSaleLabel withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_sale_label.spu_code
     *
     * @param spuCode the value for supply_chain..spu_sale_label.spu_code
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_sale_label.label
     *
     * @return the value of supply_chain..spu_sale_label.label
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public String getLabel() {
        return label;
    }

    /**
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public SpuSaleLabel withLabel(String label) {
        this.setLabel(label);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_sale_label.label
     *
     * @param label the value for supply_chain..spu_sale_label.label
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public void setLabel(String label) {
        this.label = label;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_sale_label.type
     *
     * @return the value of supply_chain..spu_sale_label.type
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public String getType() {
        return type;
    }

    /**
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public SpuSaleLabel withType(String type) {
        this.setType(type);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_sale_label.type
     *
     * @param type the value for supply_chain..spu_sale_label.type
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_sale_label.create_time
     *
     * @return the value of supply_chain..spu_sale_label.create_time
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public SpuSaleLabel withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_sale_label.create_time
     *
     * @param createTime the value for supply_chain..spu_sale_label.create_time
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_sale_label.update_time
     *
     * @return the value of supply_chain..spu_sale_label.update_time
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public SpuSaleLabel withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_sale_label.update_time
     *
     * @param updateTime the value for supply_chain..spu_sale_label.update_time
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", label=").append(label);
        sb.append(", type=").append(type);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SpuSaleLabel other = (SpuSaleLabel) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getLabel() == null ? other.getLabel() == null : this.getLabel().equals(other.getLabel()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getLabel() == null) ? 0 : getLabel().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Fri Feb 28 10:09:36 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        label("label", "label", "VARCHAR", false),
        type("type", "type", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Fri Feb 28 10:09:36 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}