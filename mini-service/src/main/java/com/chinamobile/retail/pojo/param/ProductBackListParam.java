package com.chinamobile.retail.pojo.param;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * created by l<PERSON>xia<PERSON> on 2022/8/31 11:06
 */
@Data
public class ProductBackListParam extends BasePageQuery {

    //商品类型，A04：DICT；A06：代销；A07：合同履约；A08：OneNET; A09:OnePark; A10:OneTraffic
    private List<String> productType;

    private List<String> pointSupplierId;

    private Integer partnerRoleType;

    //积分状态, 1– 暂停 2– 生效
    private List<Integer> pointStatus;

    //sku或spu名称模糊搜索
    private String queryParam;

    //发布范围：是否选择全国，如果选择全国，则不能选择省市
    private Boolean nationwide = false;

    //发布范围：省编码集合
    private List<String> provinceCodeList;

    //发布范围：市编码集合
    private List<String> cityCodeList;

}
