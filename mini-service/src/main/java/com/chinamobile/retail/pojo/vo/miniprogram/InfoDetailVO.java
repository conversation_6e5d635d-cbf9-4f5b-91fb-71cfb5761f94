package com.chinamobile.retail.pojo.vo.miniprogram;

import com.chinamobile.retail.pojo.entity.MiniProgramInfoActivity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 销售端小程序资讯信息
 */
@Data
public class InfoDetailVO implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 资讯主题名称
     *
     */
    private String name;

    /**
     * 发布区域
     */
    private Integer category;
    /**
     * 发布区域，用逗号分隔。 1-资讯中心 2-营销素材
     * 示例，“1,2”
     */
    private String categoryString;
    /**
     * 资讯类型，拼接字符串；1-产品推荐，2-产品评测，3-真实案例
     * 示例，“1,2”
     */
    private String infoType;

    /**
     * 素材类型 1-图文素材 2-视频素材
     */
    private int contentType;


    /**
     * 资讯状态 0-待发布，1-已发布，2-已下线
     *
     */
    private Integer status;


    /**
     * 审核状态 0-待审核，1-审核中，2-审核通过，3-驳回
     *
     */
    private Integer auditStatus;

    /**
     * 资讯头图url
     *
     */
    private String headImgUrl1;

    /**
     * 营销素材头图url
     *
     */
    private String headImgUrl2;

    /**
     * 资讯内容
     */
    private String content;

    /**
     * 资讯关键词，拼接字符串；
     * 示例，“千里眼,设备”
     */
    private String keyWords;

    /**
     * 素材描述
     */
    private String description;

    /**
     * 资讯关键词，自选关联产品
     */
    private List<InfoSpuItemVO> spuItems;
    /**
     * 专区名称
     */
    private String activityName;
    /**
     * 专区列表
     */
    private List<MiniProgramInfoActivity> activityList;

    /**
     * 创建人姓名
     */
    private String createUserName;
    /**
     * 文档类型 0-产品资料，1-操作指南
     */
    private Integer wordType;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人姓名
     */
    private Boolean hasProduct;



}