<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.UserAgreementMapperExt">
  <select id="userAgreementList" parameterType="com.chinamobile.retail.pojo.param.miniprogram.UserAgreementListParam" resultType="com.chinamobile.retail.pojo.mapper.UserAgreementListDO">
    SELECT
      ua.id,
      ua.`name`,
      ua.content,
      ua.type,
      ua.status,
      ua.audit_status auditStatus,
      ua.create_time createTime,
      u.`name` creatorName
    FROM
      user_agreement ua
        LEFT JOIN USER u ON ua.creator_id = u.user_id
    WHERE
      1 = 1
    <if test="param.searchWord != null and param.searchWord != ''">
      and ua.name = #{param.searchWord}
    </if>
    <if test="param.status != null ">
      and ua.status = #{param.status}
    </if>
    <if test="param.type != null ">
      and ua.type = #{param.type}
    </if>
    <if test="param.sortType != null and param.sortType == 1">
      order by ua.create_time desc
    </if>
    <if test="param.sortType != null and param.sortType == 2">
      order by ua.submit_audit_time desc
    </if>
  </select>
</mapper>