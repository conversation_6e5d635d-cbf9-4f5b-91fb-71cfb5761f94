<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.UserMinProgramRequestHeaderMapperExt">
  <select id="listUserHeaderRequest"
          resultType="com.chinamobile.retail.pojo.vo.UserHeaderVO">
      select
          umprh.id,
          ump.phone,
          umprh.create_time createTime,
          umprh.hearder_url headerUrl
      from
          user_min_program_request_header umprh,
          user_mini_program ump
      where
          umprh.user_retail_id = ump.id
      and umprh.audit_status = 1
  </select>
</mapper>