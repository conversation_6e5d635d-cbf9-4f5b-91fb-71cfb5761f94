<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.SkuCoreComponentMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.SkuCoreComponent">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="core_component_name" jdbcType="VARCHAR" property="coreComponentName" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, spu_code, core_component_name, sku_code, is_delete
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.SkuCoreComponentExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sku_core_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from sku_core_component
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from sku_core_component
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.SkuCoreComponentExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from sku_core_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.SkuCoreComponent">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into sku_core_component (id, spu_code, core_component_name, 
      sku_code, is_delete)
    values (#{id,jdbcType=VARCHAR}, #{spuCode,jdbcType=VARCHAR}, #{coreComponentName,jdbcType=VARCHAR}, 
      #{skuCode,jdbcType=VARCHAR}, #{isDelete,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.SkuCoreComponent">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into sku_core_component
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="coreComponentName != null">
        core_component_name,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="coreComponentName != null">
        #{coreComponentName,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.SkuCoreComponentExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from sku_core_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    update sku_core_component
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.coreComponentName != null">
        core_component_name = #{record.coreComponentName,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCode != null">
        sku_code = #{record.skuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    update sku_core_component
    set id = #{record.id,jdbcType=VARCHAR},
      spu_code = #{record.spuCode,jdbcType=VARCHAR},
      core_component_name = #{record.coreComponentName,jdbcType=VARCHAR},
      sku_code = #{record.skuCode,jdbcType=VARCHAR},
      is_delete = #{record.isDelete,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.SkuCoreComponent">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    update sku_core_component
    <set>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="coreComponentName != null">
        core_component_name = #{coreComponentName,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.SkuCoreComponent">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    update sku_core_component
    set spu_code = #{spuCode,jdbcType=VARCHAR},
      core_component_name = #{coreComponentName,jdbcType=VARCHAR},
      sku_code = #{skuCode,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=BIT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into sku_core_component
    (id, spu_code, core_component_name, sku_code, is_delete)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.spuCode,jdbcType=VARCHAR}, #{item.coreComponentName,jdbcType=VARCHAR}, 
        #{item.skuCode,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=BIT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 25 16:38:37 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into sku_core_component (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'spu_code'.toString() == column.value">
          #{item.spuCode,jdbcType=VARCHAR}
        </if>
        <if test="'core_component_name'.toString() == column.value">
          #{item.coreComponentName,jdbcType=VARCHAR}
        </if>
        <if test="'sku_code'.toString() == column.value">
          #{item.skuCode,jdbcType=VARCHAR}
        </if>
        <if test="'is_delete'.toString() == column.value">
          #{item.isDelete,jdbcType=BIT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>