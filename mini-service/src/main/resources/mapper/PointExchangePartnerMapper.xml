<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.PointExchangePartnerMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.PointExchangePartner">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="exchange_id" jdbcType="VARCHAR" property="exchangeId" />
    <result column="supplier_id" jdbcType="VARCHAR" property="supplierId" />
    <result column="partner_id" jdbcType="VARCHAR" property="partnerId" />
    <result column="point" jdbcType="BIGINT" property="point" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="activity_id" jdbcType="VARCHAR" property="activityId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, exchange_id, supplier_id, partner_id, point, status, fail_reason, create_time, 
    update_time, channel, activity_id
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.PointExchangePartnerExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from point_exchange_partner
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from point_exchange_partner
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from point_exchange_partner
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.PointExchangePartnerExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from point_exchange_partner
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.PointExchangePartner">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into point_exchange_partner (id, exchange_id, supplier_id, 
      partner_id, point, status, 
      fail_reason, create_time, update_time, 
      channel, activity_id)
    values (#{id,jdbcType=VARCHAR}, #{exchangeId,jdbcType=VARCHAR}, #{supplierId,jdbcType=VARCHAR}, 
      #{partnerId,jdbcType=VARCHAR}, #{point,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, 
      #{failReason,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{channel,jdbcType=INTEGER}, #{activityId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.PointExchangePartner">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into point_exchange_partner
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="exchangeId != null">
        exchange_id,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="partnerId != null">
        partner_id,
      </if>
      <if test="point != null">
        point,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="failReason != null">
        fail_reason,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="exchangeId != null">
        #{exchangeId,jdbcType=VARCHAR},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="partnerId != null">
        #{partnerId,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        #{point,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="failReason != null">
        #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.PointExchangePartnerExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from point_exchange_partner
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    update point_exchange_partner
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.exchangeId != null">
        exchange_id = #{record.exchangeId,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierId != null">
        supplier_id = #{record.supplierId,jdbcType=VARCHAR},
      </if>
      <if test="record.partnerId != null">
        partner_id = #{record.partnerId,jdbcType=VARCHAR},
      </if>
      <if test="record.point != null">
        point = #{record.point,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.failReason != null">
        fail_reason = #{record.failReason,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    update point_exchange_partner
    set id = #{record.id,jdbcType=VARCHAR},
      exchange_id = #{record.exchangeId,jdbcType=VARCHAR},
      supplier_id = #{record.supplierId,jdbcType=VARCHAR},
      partner_id = #{record.partnerId,jdbcType=VARCHAR},
      point = #{record.point,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      fail_reason = #{record.failReason,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      channel = #{record.channel,jdbcType=INTEGER},
      activity_id = #{record.activityId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.PointExchangePartner">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    update point_exchange_partner
    <set>
      <if test="exchangeId != null">
        exchange_id = #{exchangeId,jdbcType=VARCHAR},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="partnerId != null">
        partner_id = #{partnerId,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        point = #{point,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="failReason != null">
        fail_reason = #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.PointExchangePartner">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    update point_exchange_partner
    set exchange_id = #{exchangeId,jdbcType=VARCHAR},
      supplier_id = #{supplierId,jdbcType=VARCHAR},
      partner_id = #{partnerId,jdbcType=VARCHAR},
      point = #{point,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      fail_reason = #{failReason,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      channel = #{channel,jdbcType=INTEGER},
      activity_id = #{activityId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into point_exchange_partner
    (id, exchange_id, supplier_id, partner_id, point, status, fail_reason, create_time, 
      update_time, channel, activity_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.exchangeId,jdbcType=VARCHAR}, #{item.supplierId,jdbcType=VARCHAR}, 
        #{item.partnerId,jdbcType=VARCHAR}, #{item.point,jdbcType=BIGINT}, #{item.status,jdbcType=INTEGER}, 
        #{item.failReason,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.channel,jdbcType=INTEGER}, #{item.activityId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 14 19:12:07 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into point_exchange_partner (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'exchange_id'.toString() == column.value">
          #{item.exchangeId,jdbcType=VARCHAR}
        </if>
        <if test="'supplier_id'.toString() == column.value">
          #{item.supplierId,jdbcType=VARCHAR}
        </if>
        <if test="'partner_id'.toString() == column.value">
          #{item.partnerId,jdbcType=VARCHAR}
        </if>
        <if test="'point'.toString() == column.value">
          #{item.point,jdbcType=BIGINT}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'fail_reason'.toString() == column.value">
          #{item.failReason,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'channel'.toString() == column.value">
          #{item.channel,jdbcType=INTEGER}
        </if>
        <if test="'activity_id'.toString() == column.value">
          #{item.activityId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>