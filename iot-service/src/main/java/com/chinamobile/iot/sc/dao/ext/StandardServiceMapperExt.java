package com.chinamobile.iot.sc.dao.ext;

import com.chinamobile.iot.sc.pojo.mapper.StandardServiceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StandardServiceMapperExt {

    //查询售后服务商品对应的标准服务
    StandardServiceDO findByAfterMarketCode(@Param("afterMarketCode")String afterMarketCode,@Param("afterMarketVersion")String afterMarketVersion);

    //查询原子商品对应的标准服务信息，多个逗号分隔
    List<StandardServiceDO> findByAtomCodeList(@Param("skuCode")String skuCode,@Param("atomCodeList") List<String> atomCodeList);

}