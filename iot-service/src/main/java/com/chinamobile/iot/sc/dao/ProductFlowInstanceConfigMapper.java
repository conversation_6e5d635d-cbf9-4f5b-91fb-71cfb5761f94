package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.ProductFlowInstanceConfig;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProductFlowInstanceConfigMapper {
    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    long countByExample(ProductFlowInstanceConfigExample example);

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    int deleteByExample(ProductFlowInstanceConfigExample example);

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    int insert(ProductFlowInstanceConfig record);

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    int insertSelective(ProductFlowInstanceConfig record);

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    List<ProductFlowInstanceConfig> selectByExample(ProductFlowInstanceConfigExample example);

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    ProductFlowInstanceConfig selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    int updateByExampleSelective(@Param("record") ProductFlowInstanceConfig record, @Param("example") ProductFlowInstanceConfigExample example);

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    int updateByExample(@Param("record") ProductFlowInstanceConfig record, @Param("example") ProductFlowInstanceConfigExample example);

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    int updateByPrimaryKeySelective(ProductFlowInstanceConfig record);

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    int updateByPrimaryKey(ProductFlowInstanceConfig record);

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    int batchInsert(@Param("list") List<ProductFlowInstanceConfig> list);

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    int batchInsertSelective(@Param("list") List<ProductFlowInstanceConfig> list, @Param("selective") ProductFlowInstanceConfig.Column ... selective);
}