package com.chinamobile.iot.sc.pojo.mapper;

import lombok.Data;

import java.util.Date;
@Data
public class ProductFlowDO {
    // 定义类的属性，每个属性对应表中的一个字段，使用 private 修饰符
    // 属性的类型和名称与字段的类型和名称一致，使用驼峰命名法
    private String flowInstanceId;
    private String name; // 流程名称
    private String number; // 流程编号
    private Integer flowType; // 流程类型
    private Integer operateType; // 运营类型，使用包装类，可以表示 null 值
    private Integer status; // 流程状态
    private String creatorId; // 创建人id
    private Date createTime; // 创建时间，使用 java.sql.Date 类
    private Date updateTime; // 更新时间，使用 java.sql.Date 类
}
