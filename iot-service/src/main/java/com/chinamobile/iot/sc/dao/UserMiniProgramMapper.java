package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.UserMiniProgram;
import com.chinamobile.iot.sc.pojo.entity.UserMiniProgramExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserMiniProgramMapper {
    long countByExample(UserMiniProgramExample example);

    int deleteByExample(UserMiniProgramExample example);

    int deleteByPrimaryKey(String id);

    int insert(UserMiniProgram record);

    int insertSelective(UserMiniProgram record);

    List<UserMiniProgram> selectByExample(UserMiniProgramExample example);

    UserMiniProgram selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") UserMiniProgram record, @Param("example") UserMiniProgramExample example);

    int updateByExample(@Param("record") UserMiniProgram record, @Param("example") UserMiniProgramExample example);

    int updateByPrimaryKeySelective(UserMiniProgram record);

    int updateByPrimaryKey(UserMiniProgram record);

    int batchInsert(@Param("list") List<UserMiniProgram> list);

    int batchInsertSelective(@Param("list") List<UserMiniProgram> list, @Param("selective") UserMiniProgram.Column ... selective);
}