package com.chinamobile.iot.sc.pojo.vo.miniprogram;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/21 16:24
 * @description TODO
 */
@Data
public class ProductNavigationDirectoryVO {

    private String id;

    private String name;

    private String image;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean isNew;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ProductNavigationDirectoryVO> children;

}
