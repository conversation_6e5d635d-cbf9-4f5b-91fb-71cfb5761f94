package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.SpuSkuAttachmentHistory;
import com.chinamobile.iot.sc.pojo.SpuSkuAttachmentHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SpuSkuAttachmentHistoryMapper {
    /**
     *
     * @mbg.generated Thu Feb 13 11:31:29 CST 2025
     */
    long countByExample(SpuSkuAttachmentHistoryExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 11:31:29 CST 2025
     */
    int deleteByExample(SpuSkuAttachmentHistoryExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 11:31:29 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Feb 13 11:31:29 CST 2025
     */
    int insert(SpuSkuAttachmentHistory record);

    /**
     *
     * @mbg.generated Thu Feb 13 11:31:29 CST 2025
     */
    int insertSelective(SpuSkuAttachmentHistory record);

    /**
     *
     * @mbg.generated Thu Feb 13 11:31:29 CST 2025
     */
    List<SpuSkuAttachmentHistory> selectByExample(SpuSkuAttachmentHistoryExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 11:31:29 CST 2025
     */
    SpuSkuAttachmentHistory selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Feb 13 11:31:29 CST 2025
     */
    int updateByExampleSelective(@Param("record") SpuSkuAttachmentHistory record, @Param("example") SpuSkuAttachmentHistoryExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 11:31:29 CST 2025
     */
    int updateByExample(@Param("record") SpuSkuAttachmentHistory record, @Param("example") SpuSkuAttachmentHistoryExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 11:31:29 CST 2025
     */
    int updateByPrimaryKeySelective(SpuSkuAttachmentHistory record);

    /**
     *
     * @mbg.generated Thu Feb 13 11:31:29 CST 2025
     */
    int updateByPrimaryKey(SpuSkuAttachmentHistory record);

    /**
     *
     * @mbg.generated Thu Feb 13 11:31:29 CST 2025
     */
    int batchInsert(@Param("list") List<SpuSkuAttachmentHistory> list);

    /**
     *
     * @mbg.generated Thu Feb 13 11:31:29 CST 2025
     */
    int batchInsertSelective(@Param("list") List<SpuSkuAttachmentHistory> list, @Param("selective") SpuSkuAttachmentHistory.Column ... selective);
}