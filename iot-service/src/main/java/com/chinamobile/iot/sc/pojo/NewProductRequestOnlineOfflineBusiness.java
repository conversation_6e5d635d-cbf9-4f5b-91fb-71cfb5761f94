package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class NewProductRequestOnlineOfflineBusiness implements Serializable {
    private String id;

    private String newProductRequestId;

    private String comboInfoId;

    private String fileName;

    private String fileInnerUrl;

    private String fileOuterUrl;

    private String fileType;

    private String fileKey;

    private Date createTime;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public NewProductRequestOnlineOfflineBusiness withId(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getNewProductRequestId() {
        return newProductRequestId;
    }

    public NewProductRequestOnlineOfflineBusiness withNewProductRequestId(String newProductRequestId) {
        this.setNewProductRequestId(newProductRequestId);
        return this;
    }

    public void setNewProductRequestId(String newProductRequestId) {
        this.newProductRequestId = newProductRequestId == null ? null : newProductRequestId.trim();
    }

    public String getComboInfoId() {
        return comboInfoId;
    }

    public NewProductRequestOnlineOfflineBusiness withComboInfoId(String comboInfoId) {
        this.setComboInfoId(comboInfoId);
        return this;
    }

    public void setComboInfoId(String comboInfoId) {
        this.comboInfoId = comboInfoId == null ? null : comboInfoId.trim();
    }

    public String getFileName() {
        return fileName;
    }

    public NewProductRequestOnlineOfflineBusiness withFileName(String fileName) {
        this.setFileName(fileName);
        return this;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName == null ? null : fileName.trim();
    }

    public String getFileInnerUrl() {
        return fileInnerUrl;
    }

    public NewProductRequestOnlineOfflineBusiness withFileInnerUrl(String fileInnerUrl) {
        this.setFileInnerUrl(fileInnerUrl);
        return this;
    }

    public void setFileInnerUrl(String fileInnerUrl) {
        this.fileInnerUrl = fileInnerUrl == null ? null : fileInnerUrl.trim();
    }

    public String getFileOuterUrl() {
        return fileOuterUrl;
    }

    public NewProductRequestOnlineOfflineBusiness withFileOuterUrl(String fileOuterUrl) {
        this.setFileOuterUrl(fileOuterUrl);
        return this;
    }

    public void setFileOuterUrl(String fileOuterUrl) {
        this.fileOuterUrl = fileOuterUrl == null ? null : fileOuterUrl.trim();
    }

    public String getFileType() {
        return fileType;
    }

    public NewProductRequestOnlineOfflineBusiness withFileType(String fileType) {
        this.setFileType(fileType);
        return this;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType == null ? null : fileType.trim();
    }

    public String getFileKey() {
        return fileKey;
    }

    public NewProductRequestOnlineOfflineBusiness withFileKey(String fileKey) {
        this.setFileKey(fileKey);
        return this;
    }

    public void setFileKey(String fileKey) {
        this.fileKey = fileKey == null ? null : fileKey.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public NewProductRequestOnlineOfflineBusiness withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", newProductRequestId=").append(newProductRequestId);
        sb.append(", comboInfoId=").append(comboInfoId);
        sb.append(", fileName=").append(fileName);
        sb.append(", fileInnerUrl=").append(fileInnerUrl);
        sb.append(", fileOuterUrl=").append(fileOuterUrl);
        sb.append(", fileType=").append(fileType);
        sb.append(", fileKey=").append(fileKey);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        NewProductRequestOnlineOfflineBusiness other = (NewProductRequestOnlineOfflineBusiness) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getNewProductRequestId() == null ? other.getNewProductRequestId() == null : this.getNewProductRequestId().equals(other.getNewProductRequestId()))
            && (this.getComboInfoId() == null ? other.getComboInfoId() == null : this.getComboInfoId().equals(other.getComboInfoId()))
            && (this.getFileName() == null ? other.getFileName() == null : this.getFileName().equals(other.getFileName()))
            && (this.getFileInnerUrl() == null ? other.getFileInnerUrl() == null : this.getFileInnerUrl().equals(other.getFileInnerUrl()))
            && (this.getFileOuterUrl() == null ? other.getFileOuterUrl() == null : this.getFileOuterUrl().equals(other.getFileOuterUrl()))
            && (this.getFileType() == null ? other.getFileType() == null : this.getFileType().equals(other.getFileType()))
            && (this.getFileKey() == null ? other.getFileKey() == null : this.getFileKey().equals(other.getFileKey()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getNewProductRequestId() == null) ? 0 : getNewProductRequestId().hashCode());
        result = prime * result + ((getComboInfoId() == null) ? 0 : getComboInfoId().hashCode());
        result = prime * result + ((getFileName() == null) ? 0 : getFileName().hashCode());
        result = prime * result + ((getFileInnerUrl() == null) ? 0 : getFileInnerUrl().hashCode());
        result = prime * result + ((getFileOuterUrl() == null) ? 0 : getFileOuterUrl().hashCode());
        result = prime * result + ((getFileType() == null) ? 0 : getFileType().hashCode());
        result = prime * result + ((getFileKey() == null) ? 0 : getFileKey().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    public enum Column {
        id("id", "id", "VARCHAR", false),
        newProductRequestId("new_product_request_id", "newProductRequestId", "VARCHAR", false),
        comboInfoId("combo_info_id", "comboInfoId", "VARCHAR", false),
        fileName("file_name", "fileName", "VARCHAR", false),
        fileInnerUrl("file_inner_url", "fileInnerUrl", "VARCHAR", false),
        fileOuterUrl("file_outer_url", "fileOuterUrl", "VARCHAR", false),
        fileType("file_type", "fileType", "VARCHAR", false),
        fileKey("file_key", "fileKey", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}