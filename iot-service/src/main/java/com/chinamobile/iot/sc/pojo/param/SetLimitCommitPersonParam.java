package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR> Assistant
 * @date 2025/07/29
 * @description 设置限制提交人员Redis参数类
 */
@Data
public class SetLimitCommitPersonParam {

    /**
     * 订单号
     */
    @NotEmpty(message = "订单号不能为空")
    private String orderId;

    /**
     * 原子订单号
     */
    @NotEmpty(message = "原子订单号不能为空")
    private String atomOrderId;

    /**
     * Redis值对象
     */
    @NotNull(message = "Redis值不能为空")
    @Valid
    private LimitCommitPersonValue value;

    /**
     * 限制提交人员值对象
     */
    @Data
    public static class LimitCommitPersonValue {
        /**
         * 限制金额
         */
        private BigDecimal limit;

        /**
         * 区域ID
         */
        private String regionId;
    }
}
