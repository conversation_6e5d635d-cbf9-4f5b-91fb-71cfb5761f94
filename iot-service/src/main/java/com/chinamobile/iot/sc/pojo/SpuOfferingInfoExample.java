package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SpuOfferingInfoExample {
    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public SpuOfferingInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public SpuOfferingInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public SpuOfferingInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public static Criteria newAndCreateCriteria() {
        SpuOfferingInfoExample example = new SpuOfferingInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public SpuOfferingInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public SpuOfferingInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOperIdIsNull() {
            addCriterion("oper_id is null");
            return (Criteria) this;
        }

        public Criteria andOperIdIsNotNull() {
            addCriterion("oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andOperIdEqualTo(String value) {
            addCriterion("oper_id =", value, "operId");
            return (Criteria) this;
        }

        public Criteria andOperIdEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperIdNotEqualTo(String value) {
            addCriterion("oper_id <>", value, "operId");
            return (Criteria) this;
        }

        public Criteria andOperIdNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperIdGreaterThan(String value) {
            addCriterion("oper_id >", value, "operId");
            return (Criteria) this;
        }

        public Criteria andOperIdGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperIdGreaterThanOrEqualTo(String value) {
            addCriterion("oper_id >=", value, "operId");
            return (Criteria) this;
        }

        public Criteria andOperIdGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperIdLessThan(String value) {
            addCriterion("oper_id <", value, "operId");
            return (Criteria) this;
        }

        public Criteria andOperIdLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperIdLessThanOrEqualTo(String value) {
            addCriterion("oper_id <=", value, "operId");
            return (Criteria) this;
        }

        public Criteria andOperIdLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperIdLike(String value) {
            addCriterion("oper_id like", value, "operId");
            return (Criteria) this;
        }

        public Criteria andOperIdNotLike(String value) {
            addCriterion("oper_id not like", value, "operId");
            return (Criteria) this;
        }

        public Criteria andOperIdIn(List<String> values) {
            addCriterion("oper_id in", values, "operId");
            return (Criteria) this;
        }

        public Criteria andOperIdNotIn(List<String> values) {
            addCriterion("oper_id not in", values, "operId");
            return (Criteria) this;
        }

        public Criteria andOperIdBetween(String value1, String value2) {
            addCriterion("oper_id between", value1, value2, "operId");
            return (Criteria) this;
        }

        public Criteria andOperIdNotBetween(String value1, String value2) {
            addCriterion("oper_id not between", value1, value2, "operId");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeIsNull() {
            addCriterion("offering_code is null");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeIsNotNull() {
            addCriterion("offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeEqualTo(String value) {
            addCriterion("offering_code =", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotEqualTo(String value) {
            addCriterion("offering_code <>", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThan(String value) {
            addCriterion("offering_code >", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("offering_code >=", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThan(String value) {
            addCriterion("offering_code <", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("offering_code <=", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLike(String value) {
            addCriterion("offering_code like", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotLike(String value) {
            addCriterion("offering_code not like", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeIn(List<String> values) {
            addCriterion("offering_code in", values, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotIn(List<String> values) {
            addCriterion("offering_code not in", values, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeBetween(String value1, String value2) {
            addCriterion("offering_code between", value1, value2, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("offering_code not between", value1, value2, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingNameIsNull() {
            addCriterion("offering_name is null");
            return (Criteria) this;
        }

        public Criteria andOfferingNameIsNotNull() {
            addCriterion("offering_name is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingNameEqualTo(String value) {
            addCriterion("offering_name =", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotEqualTo(String value) {
            addCriterion("offering_name <>", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThan(String value) {
            addCriterion("offering_name >", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThanOrEqualTo(String value) {
            addCriterion("offering_name >=", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThan(String value) {
            addCriterion("offering_name <", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThanOrEqualTo(String value) {
            addCriterion("offering_name <=", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameLike(String value) {
            addCriterion("offering_name like", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotLike(String value) {
            addCriterion("offering_name not like", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameIn(List<String> values) {
            addCriterion("offering_name in", values, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotIn(List<String> values) {
            addCriterion("offering_name not in", values, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameBetween(String value1, String value2) {
            addCriterion("offering_name between", value1, value2, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotBetween(String value1, String value2) {
            addCriterion("offering_name not between", value1, value2, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusIsNull() {
            addCriterion("offering_status is null");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusIsNotNull() {
            addCriterion("offering_status is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusEqualTo(String value) {
            addCriterion("offering_status =", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusNotEqualTo(String value) {
            addCriterion("offering_status <>", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusGreaterThan(String value) {
            addCriterion("offering_status >", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusGreaterThanOrEqualTo(String value) {
            addCriterion("offering_status >=", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusLessThan(String value) {
            addCriterion("offering_status <", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusLessThanOrEqualTo(String value) {
            addCriterion("offering_status <=", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusLike(String value) {
            addCriterion("offering_status like", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusNotLike(String value) {
            addCriterion("offering_status not like", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusIn(List<String> values) {
            addCriterion("offering_status in", values, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusNotIn(List<String> values) {
            addCriterion("offering_status not in", values, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusBetween(String value1, String value2) {
            addCriterion("offering_status between", value1, value2, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusNotBetween(String value1, String value2) {
            addCriterion("offering_status not between", value1, value2, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOperTypeIsNull() {
            addCriterion("oper_type is null");
            return (Criteria) this;
        }

        public Criteria andOperTypeIsNotNull() {
            addCriterion("oper_type is not null");
            return (Criteria) this;
        }

        public Criteria andOperTypeEqualTo(String value) {
            addCriterion("oper_type =", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperTypeNotEqualTo(String value) {
            addCriterion("oper_type <>", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperTypeGreaterThan(String value) {
            addCriterion("oper_type >", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperTypeGreaterThanOrEqualTo(String value) {
            addCriterion("oper_type >=", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperTypeLessThan(String value) {
            addCriterion("oper_type <", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperTypeLessThanOrEqualTo(String value) {
            addCriterion("oper_type <=", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperTypeLike(String value) {
            addCriterion("oper_type like", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeNotLike(String value) {
            addCriterion("oper_type not like", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeIn(List<String> values) {
            addCriterion("oper_type in", values, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeNotIn(List<String> values) {
            addCriterion("oper_type not in", values, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeBetween(String value1, String value2) {
            addCriterion("oper_type between", value1, value2, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeNotBetween(String value1, String value2) {
            addCriterion("oper_type not between", value1, value2, "operType");
            return (Criteria) this;
        }

        public Criteria andSaleObjectIsNull() {
            addCriterion("sale_object is null");
            return (Criteria) this;
        }

        public Criteria andSaleObjectIsNotNull() {
            addCriterion("sale_object is not null");
            return (Criteria) this;
        }

        public Criteria andSaleObjectEqualTo(String value) {
            addCriterion("sale_object =", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_object = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleObjectNotEqualTo(String value) {
            addCriterion("sale_object <>", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_object <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleObjectGreaterThan(String value) {
            addCriterion("sale_object >", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_object > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleObjectGreaterThanOrEqualTo(String value) {
            addCriterion("sale_object >=", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_object >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleObjectLessThan(String value) {
            addCriterion("sale_object <", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_object < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleObjectLessThanOrEqualTo(String value) {
            addCriterion("sale_object <=", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_object <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleObjectLike(String value) {
            addCriterion("sale_object like", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectNotLike(String value) {
            addCriterion("sale_object not like", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectIn(List<String> values) {
            addCriterion("sale_object in", values, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectNotIn(List<String> values) {
            addCriterion("sale_object not in", values, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectBetween(String value1, String value2) {
            addCriterion("sale_object between", value1, value2, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectNotBetween(String value1, String value2) {
            addCriterion("sale_object not between", value1, value2, "saleObject");
            return (Criteria) this;
        }

        public Criteria andUrlIsNull() {
            addCriterion("url is null");
            return (Criteria) this;
        }

        public Criteria andUrlIsNotNull() {
            addCriterion("url is not null");
            return (Criteria) this;
        }

        public Criteria andUrlEqualTo(String value) {
            addCriterion("url =", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("url = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUrlNotEqualTo(String value) {
            addCriterion("url <>", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("url <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThan(String value) {
            addCriterion("url >", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("url > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThanOrEqualTo(String value) {
            addCriterion("url >=", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("url >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUrlLessThan(String value) {
            addCriterion("url <", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("url < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUrlLessThanOrEqualTo(String value) {
            addCriterion("url <=", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("url <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUrlLike(String value) {
            addCriterion("url like", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotLike(String value) {
            addCriterion("url not like", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlIn(List<String> values) {
            addCriterion("url in", values, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotIn(List<String> values) {
            addCriterion("url not in", values, "url");
            return (Criteria) this;
        }

        public Criteria andUrlBetween(String value1, String value2) {
            addCriterion("url between", value1, value2, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotBetween(String value1, String value2) {
            addCriterion("url not between", value1, value2, "url");
            return (Criteria) this;
        }

        public Criteria andImgUrlIsNull() {
            addCriterion("img_url is null");
            return (Criteria) this;
        }

        public Criteria andImgUrlIsNotNull() {
            addCriterion("img_url is not null");
            return (Criteria) this;
        }

        public Criteria andImgUrlEqualTo(String value) {
            addCriterion("img_url =", value, "imgUrl");
            return (Criteria) this;
        }

        public Criteria andImgUrlEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("img_url = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImgUrlNotEqualTo(String value) {
            addCriterion("img_url <>", value, "imgUrl");
            return (Criteria) this;
        }

        public Criteria andImgUrlNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("img_url <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImgUrlGreaterThan(String value) {
            addCriterion("img_url >", value, "imgUrl");
            return (Criteria) this;
        }

        public Criteria andImgUrlGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("img_url > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("img_url >=", value, "imgUrl");
            return (Criteria) this;
        }

        public Criteria andImgUrlGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("img_url >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImgUrlLessThan(String value) {
            addCriterion("img_url <", value, "imgUrl");
            return (Criteria) this;
        }

        public Criteria andImgUrlLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("img_url < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImgUrlLessThanOrEqualTo(String value) {
            addCriterion("img_url <=", value, "imgUrl");
            return (Criteria) this;
        }

        public Criteria andImgUrlLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("img_url <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImgUrlLike(String value) {
            addCriterion("img_url like", value, "imgUrl");
            return (Criteria) this;
        }

        public Criteria andImgUrlNotLike(String value) {
            addCriterion("img_url not like", value, "imgUrl");
            return (Criteria) this;
        }

        public Criteria andImgUrlIn(List<String> values) {
            addCriterion("img_url in", values, "imgUrl");
            return (Criteria) this;
        }

        public Criteria andImgUrlNotIn(List<String> values) {
            addCriterion("img_url not in", values, "imgUrl");
            return (Criteria) this;
        }

        public Criteria andImgUrlBetween(String value1, String value2) {
            addCriterion("img_url between", value1, value2, "imgUrl");
            return (Criteria) this;
        }

        public Criteria andImgUrlNotBetween(String value1, String value2) {
            addCriterion("img_url not between", value1, value2, "imgUrl");
            return (Criteria) this;
        }

        public Criteria andTagIsNull() {
            addCriterion("tag is null");
            return (Criteria) this;
        }

        public Criteria andTagIsNotNull() {
            addCriterion("tag is not null");
            return (Criteria) this;
        }

        public Criteria andTagEqualTo(String value) {
            addCriterion("tag =", value, "tag");
            return (Criteria) this;
        }

        public Criteria andTagEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("tag = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTagNotEqualTo(String value) {
            addCriterion("tag <>", value, "tag");
            return (Criteria) this;
        }

        public Criteria andTagNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("tag <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTagGreaterThan(String value) {
            addCriterion("tag >", value, "tag");
            return (Criteria) this;
        }

        public Criteria andTagGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("tag > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTagGreaterThanOrEqualTo(String value) {
            addCriterion("tag >=", value, "tag");
            return (Criteria) this;
        }

        public Criteria andTagGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("tag >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTagLessThan(String value) {
            addCriterion("tag <", value, "tag");
            return (Criteria) this;
        }

        public Criteria andTagLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("tag < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTagLessThanOrEqualTo(String value) {
            addCriterion("tag <=", value, "tag");
            return (Criteria) this;
        }

        public Criteria andTagLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("tag <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTagLike(String value) {
            addCriterion("tag like", value, "tag");
            return (Criteria) this;
        }

        public Criteria andTagNotLike(String value) {
            addCriterion("tag not like", value, "tag");
            return (Criteria) this;
        }

        public Criteria andTagIn(List<String> values) {
            addCriterion("tag in", values, "tag");
            return (Criteria) this;
        }

        public Criteria andTagNotIn(List<String> values) {
            addCriterion("tag not in", values, "tag");
            return (Criteria) this;
        }

        public Criteria andTagBetween(String value1, String value2) {
            addCriterion("tag between", value1, value2, "tag");
            return (Criteria) this;
        }

        public Criteria andTagNotBetween(String value1, String value2) {
            addCriterion("tag not between", value1, value2, "tag");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIsNull() {
            addCriterion("delete_time is null");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIsNotNull() {
            addCriterion("delete_time is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeEqualTo(Date value) {
            addCriterion("delete_time =", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotEqualTo(Date value) {
            addCriterion("delete_time <>", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThan(Date value) {
            addCriterion("delete_time >", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("delete_time >=", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThan(Date value) {
            addCriterion("delete_time <", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanOrEqualTo(Date value) {
            addCriterion("delete_time <=", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIn(List<Date> values) {
            addCriterion("delete_time in", values, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotIn(List<Date> values) {
            addCriterion("delete_time not in", values, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeBetween(Date value1, Date value2) {
            addCriterion("delete_time between", value1, value2, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotBetween(Date value1, Date value2) {
            addCriterion("delete_time not between", value1, value2, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andListPlatformIsNull() {
            addCriterion("list_platform is null");
            return (Criteria) this;
        }

        public Criteria andListPlatformIsNotNull() {
            addCriterion("list_platform is not null");
            return (Criteria) this;
        }

        public Criteria andListPlatformEqualTo(String value) {
            addCriterion("list_platform =", value, "listPlatform");
            return (Criteria) this;
        }

        public Criteria andListPlatformEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("list_platform = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andListPlatformNotEqualTo(String value) {
            addCriterion("list_platform <>", value, "listPlatform");
            return (Criteria) this;
        }

        public Criteria andListPlatformNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("list_platform <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andListPlatformGreaterThan(String value) {
            addCriterion("list_platform >", value, "listPlatform");
            return (Criteria) this;
        }

        public Criteria andListPlatformGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("list_platform > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andListPlatformGreaterThanOrEqualTo(String value) {
            addCriterion("list_platform >=", value, "listPlatform");
            return (Criteria) this;
        }

        public Criteria andListPlatformGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("list_platform >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andListPlatformLessThan(String value) {
            addCriterion("list_platform <", value, "listPlatform");
            return (Criteria) this;
        }

        public Criteria andListPlatformLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("list_platform < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andListPlatformLessThanOrEqualTo(String value) {
            addCriterion("list_platform <=", value, "listPlatform");
            return (Criteria) this;
        }

        public Criteria andListPlatformLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("list_platform <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andListPlatformLike(String value) {
            addCriterion("list_platform like", value, "listPlatform");
            return (Criteria) this;
        }

        public Criteria andListPlatformNotLike(String value) {
            addCriterion("list_platform not like", value, "listPlatform");
            return (Criteria) this;
        }

        public Criteria andListPlatformIn(List<String> values) {
            addCriterion("list_platform in", values, "listPlatform");
            return (Criteria) this;
        }

        public Criteria andListPlatformNotIn(List<String> values) {
            addCriterion("list_platform not in", values, "listPlatform");
            return (Criteria) this;
        }

        public Criteria andListPlatformBetween(String value1, String value2) {
            addCriterion("list_platform between", value1, value2, "listPlatform");
            return (Criteria) this;
        }

        public Criteria andListPlatformNotBetween(String value1, String value2) {
            addCriterion("list_platform not between", value1, value2, "listPlatform");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeIsNull() {
            addCriterion("inventory_type is null");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeIsNotNull() {
            addCriterion("inventory_type is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeEqualTo(String value) {
            addCriterion("inventory_type =", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryTypeNotEqualTo(String value) {
            addCriterion("inventory_type <>", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryTypeGreaterThan(String value) {
            addCriterion("inventory_type >", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryTypeGreaterThanOrEqualTo(String value) {
            addCriterion("inventory_type >=", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryTypeLessThan(String value) {
            addCriterion("inventory_type <", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryTypeLessThanOrEqualTo(String value) {
            addCriterion("inventory_type <=", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("inventory_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryTypeLike(String value) {
            addCriterion("inventory_type like", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeNotLike(String value) {
            addCriterion("inventory_type not like", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeIn(List<String> values) {
            addCriterion("inventory_type in", values, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeNotIn(List<String> values) {
            addCriterion("inventory_type not in", values, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeBetween(String value1, String value2) {
            addCriterion("inventory_type between", value1, value2, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeNotBetween(String value1, String value2) {
            addCriterion("inventory_type not between", value1, value2, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andDictProductlinesIsNull() {
            addCriterion("dict_productLines is null");
            return (Criteria) this;
        }

        public Criteria andDictProductlinesIsNotNull() {
            addCriterion("dict_productLines is not null");
            return (Criteria) this;
        }

        public Criteria andDictProductlinesEqualTo(String value) {
            addCriterion("dict_productLines =", value, "dictProductlines");
            return (Criteria) this;
        }

        public Criteria andDictProductlinesEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("dict_productLines = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDictProductlinesNotEqualTo(String value) {
            addCriterion("dict_productLines <>", value, "dictProductlines");
            return (Criteria) this;
        }

        public Criteria andDictProductlinesNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("dict_productLines <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDictProductlinesGreaterThan(String value) {
            addCriterion("dict_productLines >", value, "dictProductlines");
            return (Criteria) this;
        }

        public Criteria andDictProductlinesGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("dict_productLines > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDictProductlinesGreaterThanOrEqualTo(String value) {
            addCriterion("dict_productLines >=", value, "dictProductlines");
            return (Criteria) this;
        }

        public Criteria andDictProductlinesGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("dict_productLines >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDictProductlinesLessThan(String value) {
            addCriterion("dict_productLines <", value, "dictProductlines");
            return (Criteria) this;
        }

        public Criteria andDictProductlinesLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("dict_productLines < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDictProductlinesLessThanOrEqualTo(String value) {
            addCriterion("dict_productLines <=", value, "dictProductlines");
            return (Criteria) this;
        }

        public Criteria andDictProductlinesLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("dict_productLines <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDictProductlinesLike(String value) {
            addCriterion("dict_productLines like", value, "dictProductlines");
            return (Criteria) this;
        }

        public Criteria andDictProductlinesNotLike(String value) {
            addCriterion("dict_productLines not like", value, "dictProductlines");
            return (Criteria) this;
        }

        public Criteria andDictProductlinesIn(List<String> values) {
            addCriterion("dict_productLines in", values, "dictProductlines");
            return (Criteria) this;
        }

        public Criteria andDictProductlinesNotIn(List<String> values) {
            addCriterion("dict_productLines not in", values, "dictProductlines");
            return (Criteria) this;
        }

        public Criteria andDictProductlinesBetween(String value1, String value2) {
            addCriterion("dict_productLines between", value1, value2, "dictProductlines");
            return (Criteria) this;
        }

        public Criteria andDictProductlinesNotBetween(String value1, String value2) {
            addCriterion("dict_productLines not between", value1, value2, "dictProductlines");
            return (Criteria) this;
        }

        public Criteria andProductKeywordsIsNull() {
            addCriterion("product_keywords is null");
            return (Criteria) this;
        }

        public Criteria andProductKeywordsIsNotNull() {
            addCriterion("product_keywords is not null");
            return (Criteria) this;
        }

        public Criteria andProductKeywordsEqualTo(String value) {
            addCriterion("product_keywords =", value, "productKeywords");
            return (Criteria) this;
        }

        public Criteria andProductKeywordsEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_keywords = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductKeywordsNotEqualTo(String value) {
            addCriterion("product_keywords <>", value, "productKeywords");
            return (Criteria) this;
        }

        public Criteria andProductKeywordsNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_keywords <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductKeywordsGreaterThan(String value) {
            addCriterion("product_keywords >", value, "productKeywords");
            return (Criteria) this;
        }

        public Criteria andProductKeywordsGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_keywords > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductKeywordsGreaterThanOrEqualTo(String value) {
            addCriterion("product_keywords >=", value, "productKeywords");
            return (Criteria) this;
        }

        public Criteria andProductKeywordsGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_keywords >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductKeywordsLessThan(String value) {
            addCriterion("product_keywords <", value, "productKeywords");
            return (Criteria) this;
        }

        public Criteria andProductKeywordsLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_keywords < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductKeywordsLessThanOrEqualTo(String value) {
            addCriterion("product_keywords <=", value, "productKeywords");
            return (Criteria) this;
        }

        public Criteria andProductKeywordsLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_keywords <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductKeywordsLike(String value) {
            addCriterion("product_keywords like", value, "productKeywords");
            return (Criteria) this;
        }

        public Criteria andProductKeywordsNotLike(String value) {
            addCriterion("product_keywords not like", value, "productKeywords");
            return (Criteria) this;
        }

        public Criteria andProductKeywordsIn(List<String> values) {
            addCriterion("product_keywords in", values, "productKeywords");
            return (Criteria) this;
        }

        public Criteria andProductKeywordsNotIn(List<String> values) {
            addCriterion("product_keywords not in", values, "productKeywords");
            return (Criteria) this;
        }

        public Criteria andProductKeywordsBetween(String value1, String value2) {
            addCriterion("product_keywords between", value1, value2, "productKeywords");
            return (Criteria) this;
        }

        public Criteria andProductKeywordsNotBetween(String value1, String value2) {
            addCriterion("product_keywords not between", value1, value2, "productKeywords");
            return (Criteria) this;
        }

        public Criteria andSecretlyListedIsNull() {
            addCriterion("secretly_listed is null");
            return (Criteria) this;
        }

        public Criteria andSecretlyListedIsNotNull() {
            addCriterion("secretly_listed is not null");
            return (Criteria) this;
        }

        public Criteria andSecretlyListedEqualTo(String value) {
            addCriterion("secretly_listed =", value, "secretlyListed");
            return (Criteria) this;
        }

        public Criteria andSecretlyListedEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("secretly_listed = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecretlyListedNotEqualTo(String value) {
            addCriterion("secretly_listed <>", value, "secretlyListed");
            return (Criteria) this;
        }

        public Criteria andSecretlyListedNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("secretly_listed <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecretlyListedGreaterThan(String value) {
            addCriterion("secretly_listed >", value, "secretlyListed");
            return (Criteria) this;
        }

        public Criteria andSecretlyListedGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("secretly_listed > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecretlyListedGreaterThanOrEqualTo(String value) {
            addCriterion("secretly_listed >=", value, "secretlyListed");
            return (Criteria) this;
        }

        public Criteria andSecretlyListedGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("secretly_listed >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecretlyListedLessThan(String value) {
            addCriterion("secretly_listed <", value, "secretlyListed");
            return (Criteria) this;
        }

        public Criteria andSecretlyListedLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("secretly_listed < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecretlyListedLessThanOrEqualTo(String value) {
            addCriterion("secretly_listed <=", value, "secretlyListed");
            return (Criteria) this;
        }

        public Criteria andSecretlyListedLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("secretly_listed <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecretlyListedLike(String value) {
            addCriterion("secretly_listed like", value, "secretlyListed");
            return (Criteria) this;
        }

        public Criteria andSecretlyListedNotLike(String value) {
            addCriterion("secretly_listed not like", value, "secretlyListed");
            return (Criteria) this;
        }

        public Criteria andSecretlyListedIn(List<String> values) {
            addCriterion("secretly_listed in", values, "secretlyListed");
            return (Criteria) this;
        }

        public Criteria andSecretlyListedNotIn(List<String> values) {
            addCriterion("secretly_listed not in", values, "secretlyListed");
            return (Criteria) this;
        }

        public Criteria andSecretlyListedBetween(String value1, String value2) {
            addCriterion("secretly_listed between", value1, value2, "secretlyListed");
            return (Criteria) this;
        }

        public Criteria andSecretlyListedNotBetween(String value1, String value2) {
            addCriterion("secretly_listed not between", value1, value2, "secretlyListed");
            return (Criteria) this;
        }

        public Criteria andProductDescriptionIsNull() {
            addCriterion("product_description is null");
            return (Criteria) this;
        }

        public Criteria andProductDescriptionIsNotNull() {
            addCriterion("product_description is not null");
            return (Criteria) this;
        }

        public Criteria andProductDescriptionEqualTo(String value) {
            addCriterion("product_description =", value, "productDescription");
            return (Criteria) this;
        }

        public Criteria andProductDescriptionEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_description = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDescriptionNotEqualTo(String value) {
            addCriterion("product_description <>", value, "productDescription");
            return (Criteria) this;
        }

        public Criteria andProductDescriptionNotEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_description <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDescriptionGreaterThan(String value) {
            addCriterion("product_description >", value, "productDescription");
            return (Criteria) this;
        }

        public Criteria andProductDescriptionGreaterThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_description > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("product_description >=", value, "productDescription");
            return (Criteria) this;
        }

        public Criteria andProductDescriptionGreaterThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_description >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDescriptionLessThan(String value) {
            addCriterion("product_description <", value, "productDescription");
            return (Criteria) this;
        }

        public Criteria andProductDescriptionLessThanColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_description < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDescriptionLessThanOrEqualTo(String value) {
            addCriterion("product_description <=", value, "productDescription");
            return (Criteria) this;
        }

        public Criteria andProductDescriptionLessThanOrEqualToColumn(SpuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_description <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDescriptionLike(String value) {
            addCriterion("product_description like", value, "productDescription");
            return (Criteria) this;
        }

        public Criteria andProductDescriptionNotLike(String value) {
            addCriterion("product_description not like", value, "productDescription");
            return (Criteria) this;
        }

        public Criteria andProductDescriptionIn(List<String> values) {
            addCriterion("product_description in", values, "productDescription");
            return (Criteria) this;
        }

        public Criteria andProductDescriptionNotIn(List<String> values) {
            addCriterion("product_description not in", values, "productDescription");
            return (Criteria) this;
        }

        public Criteria andProductDescriptionBetween(String value1, String value2) {
            addCriterion("product_description between", value1, value2, "productDescription");
            return (Criteria) this;
        }

        public Criteria andProductDescriptionNotBetween(String value1, String value2) {
            addCriterion("product_description not between", value1, value2, "productDescription");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andOperIdLikeInsensitive(String value) {
            addCriterion("upper(oper_id) like", value.toUpperCase(), "operId");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(offering_code) like", value.toUpperCase(), "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingNameLikeInsensitive(String value) {
            addCriterion("upper(offering_name) like", value.toUpperCase(), "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusLikeInsensitive(String value) {
            addCriterion("upper(offering_status) like", value.toUpperCase(), "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOperTypeLikeInsensitive(String value) {
            addCriterion("upper(oper_type) like", value.toUpperCase(), "operType");
            return (Criteria) this;
        }

        public Criteria andSaleObjectLikeInsensitive(String value) {
            addCriterion("upper(sale_object) like", value.toUpperCase(), "saleObject");
            return (Criteria) this;
        }

        public Criteria andUrlLikeInsensitive(String value) {
            addCriterion("upper(url) like", value.toUpperCase(), "url");
            return (Criteria) this;
        }

        public Criteria andImgUrlLikeInsensitive(String value) {
            addCriterion("upper(img_url) like", value.toUpperCase(), "imgUrl");
            return (Criteria) this;
        }

        public Criteria andTagLikeInsensitive(String value) {
            addCriterion("upper(tag) like", value.toUpperCase(), "tag");
            return (Criteria) this;
        }

        public Criteria andListPlatformLikeInsensitive(String value) {
            addCriterion("upper(list_platform) like", value.toUpperCase(), "listPlatform");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeLikeInsensitive(String value) {
            addCriterion("upper(inventory_type) like", value.toUpperCase(), "inventoryType");
            return (Criteria) this;
        }

        public Criteria andDictProductlinesLikeInsensitive(String value) {
            addCriterion("upper(dict_productLines) like", value.toUpperCase(), "dictProductlines");
            return (Criteria) this;
        }

        public Criteria andProductKeywordsLikeInsensitive(String value) {
            addCriterion("upper(product_keywords) like", value.toUpperCase(), "productKeywords");
            return (Criteria) this;
        }

        public Criteria andSecretlyListedLikeInsensitive(String value) {
            addCriterion("upper(secretly_listed) like", value.toUpperCase(), "secretlyListed");
            return (Criteria) this;
        }

        public Criteria andProductDescriptionLikeInsensitive(String value) {
            addCriterion("upper(product_description) like", value.toUpperCase(), "productDescription");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Tue Feb 11 14:50:21 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Tue Feb 11 14:50:21 CST 2025
         */
        private SpuOfferingInfoExample example;

        /**
         *
         * @mbg.generated Tue Feb 11 14:50:21 CST 2025
         */
        protected Criteria(SpuOfferingInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue Feb 11 14:50:21 CST 2025
         */
        public SpuOfferingInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue Feb 11 14:50:21 CST 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Feb 11 14:50:21 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Feb 11 14:50:21 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue Feb 11 14:50:21 CST 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Tue Feb 11 14:50:21 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue Feb 11 14:50:21 CST 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue Feb 11 14:50:21 CST 2025
         */
        void example(com.chinamobile.iot.sc.pojo.SpuOfferingInfoExample example);
    }
}