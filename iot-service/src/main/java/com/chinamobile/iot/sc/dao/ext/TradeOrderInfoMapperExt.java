package com.chinamobile.iot.sc.dao.ext;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.pojo.dto.FinancingBillDetailDTO;
import com.chinamobile.iot.sc.pojo.dto.TradeOrderDetailDTO;
import com.chinamobile.iot.sc.pojo.dto.TradeOrderInfoDTO;
import com.chinamobile.iot.sc.pojo.param.TradeOrderInfoParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/20
 * @description 贸易订单信息mapper扩展类
 */
public interface TradeOrderInfoMapperExt {

    /**
     * 分页获取贸易订单信息
     * @param page
     * @param tradeOrderInfoParam
     * @return
     */
    List<TradeOrderInfoDTO> listTradeOrderInfo(@Param(value = "page") Page page,
                                               @Param(value = "tradeOrderInfoParam") TradeOrderInfoParam tradeOrderInfoParam);

    /**
     * 获取贸易订单详情列表
     * @param tradeNo
     * @return
     */
    List<TradeOrderDetailDTO> listTradeOrderDetail(String tradeNo);

    /**
     * 获取财务台账详情列表
     * @param tradeNo
     * @return
     */
    List<FinancingBillDetailDTO> listFinancingBillDetail(String tradeNo);
}
