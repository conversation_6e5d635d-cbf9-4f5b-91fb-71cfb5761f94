package com.chinamobile.iot.sc.dao.ext;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.pojo.param.CardRelationImportInfoParam;
import com.chinamobile.iot.sc.pojo.vo.CardRelationImportInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/13
 * @description 卡+X终端导入批次相关信息mapper扩展类
 */
public interface CardRelationImportInfoMapperExt {

    /**
     * 获取卡+X终端导入相关信息
     * @param cardRelationImportInfoParam
     * @return
     */
    List<CardRelationImportInfoVO> listCardRelationImport(@Param(value = "cardRelationImportInfoParam") CardRelationImportInfoParam cardRelationImportInfoParam);

    /**
     * 分页获取卡+X终端导入相关信息
     * @param page
     * @param cardRelationImportInfoParam
     * @return
     */
    List<CardRelationImportInfoVO> listCardRelationImport(@Param(value = "page") Page page,
                                                          @Param(value = "cardRelationImportInfoParam") CardRelationImportInfoParam cardRelationImportInfoParam);
}
