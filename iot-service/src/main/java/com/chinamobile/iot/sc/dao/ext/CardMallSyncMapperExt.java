package com.chinamobile.iot.sc.dao.ext;

import com.chinamobile.iot.sc.pojo.dto.NullCardHandleDTO;
import com.chinamobile.iot.sc.pojo.param.UpdateCardMallOrderToNullParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/28
 * @description 商城同步的卡信息mapper扩展类
 */
public interface CardMallSyncMapperExt {

    /**
     * 更新商城同步的卡信息为空
     * @param updateCardMallOrderToNullParam
     */
    void updateCardMallOrderToNull(@Param(value = "updateCardMallOrderToNullParam") UpdateCardMallOrderToNullParam updateCardMallOrderToNullParam);

    /**
     * 获取存量空写卡的码号处理
     * @return
     */
    List<NullCardHandleDTO> listNullCardHandle();
}
