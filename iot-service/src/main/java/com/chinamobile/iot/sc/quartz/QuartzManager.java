package com.chinamobile.iot.sc.quartz;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @package: com.chinamobile.iot.sgs.quartz
 * @ClassName: QuartzManager
 * @description: Quartz定时任务管理类
 * @author: zyj
 * @create: 2021/8/18 11:28
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Slf4j
@Component
public class QuartzManager {
    @Resource
    private Scheduler scheduler;

    /**
     * 添加Job
     *
     * @param quartConf 执行任务配置
     * @param jobClass  job执行类
     * @return 执行结果  0-失败  1-成功  2-cron表达式不正确
     */
    public int saveQuartzJob(QuartzJobConf quartConf, Class<? extends Job> jobClass) {

        int bl = 0;
        String conf = JSON.toJSONString(quartConf);
        try {
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put("quartConf", conf);
            JobDetail jobDetail = JobBuilder.newJob(jobClass)
                    .withIdentity(quartConf.getJobName(), quartConf.getJobGroup())
                    .usingJobData(jobDataMap)
                    .build();
            if (jobDetail != null) {
                /**触发器  触发器名,触发器组 设置Cron表达式 开始执行时间 结束执行时间**/
                CronTrigger CronTrigger = TriggerBuilder.newTrigger()
                        .withIdentity(quartConf.getTriggerName(), quartConf.getTriggerGroup())
                        .withSchedule(CronScheduleBuilder.cronSchedule(quartConf.getTriggerCron()))
                        .forJob(jobDetail)
                        .build();
                //持久化到数据库
                log.info("持久化到数据库 start...");
                scheduler.scheduleJob(jobDetail, CronTrigger);
                log.info("持久化到数据库 ok...");
                // 启动
                if (!scheduler.isShutdown()) {
                    scheduler.start();
                    log.info("启动 ok...");
                }
                log.info("addJob success..." + quartConf.getJobName());
                bl = 1;
            }

        } catch (Exception e) {
            String errMsg = e.getMessage();
            if (errMsg.endsWith("will never fire.")) {
                bl = 2;
            }
            log.error("addJob 异常：" + e.getMessage());
            // throw new RuntimeException(e);
        }
        return bl;
    }

    /**
     * 检查是否包含了对应的任务避免删除错误
     *
     * @param jobId
     * @return
     */
    public boolean checkQuartzJobByJobId(String jobId, String groupId) {
        try {
            return scheduler.checkExists(JobKey.jobKey("JobName_" + jobId, "JobGroup_" + groupId));
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 移除JOB
     *
     * @return 执行结果
     * @quartzJob 执行类
     */
    public boolean removeQuartzJobByTaskId(QuartzJobConf quartzJob) {
        boolean bl = false;
        try {
            TriggerKey triggerKey = new TriggerKey(quartzJob.getTriggerName(), quartzJob.getTriggerGroup());
            scheduler.pauseTrigger(triggerKey);// 停止触发器
            scheduler.unscheduleJob(triggerKey);// 移除触发器
            JobKey jobKey = new JobKey(quartzJob.getJobName(), quartzJob.getJobGroup());
            scheduler.deleteJob(jobKey);// 删除任务
            log.info("removeJob success..." + quartzJob.getJobName());
            bl = true;
        } catch (Exception e) {
            //throw new RuntimeException(e);
            //这里不需要用error可能不存在该定时任务
            log.warn("removeJob 异常：" + e.getMessage());
        }
        return bl;
    }


}
