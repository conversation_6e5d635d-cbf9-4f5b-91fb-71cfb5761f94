package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.Order2cInfo;
import com.chinamobile.iot.sc.pojo.Order2cInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface Order2cInfoMapper {
    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    long countByExample(Order2cInfoExample example);

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    int deleteByExample(Order2cInfoExample example);

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    int deleteByPrimaryKey(String orderId);

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    int insert(Order2cInfo record);

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    int insertSelective(Order2cInfo record);

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    List<Order2cInfo> selectByExample(Order2cInfoExample example);

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    Order2cInfo selectByPrimaryKey(String orderId);

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    int updateByExampleSelective(@Param("record") Order2cInfo record, @Param("example") Order2cInfoExample example);

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    int updateByExample(@Param("record") Order2cInfo record, @Param("example") Order2cInfoExample example);

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    int updateByPrimaryKeySelective(Order2cInfo record);

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    int updateByPrimaryKey(Order2cInfo record);

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    int batchInsert(@Param("list") List<Order2cInfo> list);

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    int batchInsertSelective(@Param("list") List<Order2cInfo> list, @Param("selective") Order2cInfo.Column ... selective);
}