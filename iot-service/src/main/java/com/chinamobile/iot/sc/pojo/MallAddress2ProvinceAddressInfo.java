package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 商城地址编码与省测地址编码映射表
 *
 * <AUTHOR>
public class MallAddress2ProvinceAddressInfo implements Serializable {
    /**
     * 河南区县名称
     *
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    private String districtProvinceName;

    /**
     * 河南区县编码
     *
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    private String districtProvinceCode;

    /**
     * 商城区县编码
     *
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    private String districtMallCode;

    /**
     * 商城区县名称
     *
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    private String districtMallName;

    /**
     * 商城地市名称
     *
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    private String cityMallName;

    /**
     * 河南地市名称
     *
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    private String cityProvinceName;

    /**
     * 河南地市编码
     *
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    private String cityProvinceCode;

    /**
     * 商城地市编码
     *
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    private String cityMallCode;

    /**
     * 省份名称
     *
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    private String provinceName;

    /**
     * 河南省份编码
     *
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    private String provinceProvinceCode;

    /**
     * 商城省份编码
     *
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    private String provinceMallCode;

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mall_address_2_province_address_info.district_province_name
     *
     * @return the value of supply_chain..mall_address_2_province_address_info.district_province_name
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public String getDistrictProvinceName() {
        return districtProvinceName;
    }

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public MallAddress2ProvinceAddressInfo withDistrictProvinceName(String districtProvinceName) {
        this.setDistrictProvinceName(districtProvinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mall_address_2_province_address_info.district_province_name
     *
     * @param districtProvinceName the value for supply_chain..mall_address_2_province_address_info.district_province_name
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public void setDistrictProvinceName(String districtProvinceName) {
        this.districtProvinceName = districtProvinceName;
    }

    /**
     * This method returns the value of the database column supply_chain..mall_address_2_province_address_info.district_province_code
     *
     * @return the value of supply_chain..mall_address_2_province_address_info.district_province_code
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public String getDistrictProvinceCode() {
        return districtProvinceCode;
    }

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public MallAddress2ProvinceAddressInfo withDistrictProvinceCode(String districtProvinceCode) {
        this.setDistrictProvinceCode(districtProvinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mall_address_2_province_address_info.district_province_code
     *
     * @param districtProvinceCode the value for supply_chain..mall_address_2_province_address_info.district_province_code
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public void setDistrictProvinceCode(String districtProvinceCode) {
        this.districtProvinceCode = districtProvinceCode;
    }

    /**
     * This method returns the value of the database column supply_chain..mall_address_2_province_address_info.district_mall_code
     *
     * @return the value of supply_chain..mall_address_2_province_address_info.district_mall_code
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public String getDistrictMallCode() {
        return districtMallCode;
    }

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public MallAddress2ProvinceAddressInfo withDistrictMallCode(String districtMallCode) {
        this.setDistrictMallCode(districtMallCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mall_address_2_province_address_info.district_mall_code
     *
     * @param districtMallCode the value for supply_chain..mall_address_2_province_address_info.district_mall_code
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public void setDistrictMallCode(String districtMallCode) {
        this.districtMallCode = districtMallCode;
    }

    /**
     * This method returns the value of the database column supply_chain..mall_address_2_province_address_info.district_mall_name
     *
     * @return the value of supply_chain..mall_address_2_province_address_info.district_mall_name
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public String getDistrictMallName() {
        return districtMallName;
    }

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public MallAddress2ProvinceAddressInfo withDistrictMallName(String districtMallName) {
        this.setDistrictMallName(districtMallName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mall_address_2_province_address_info.district_mall_name
     *
     * @param districtMallName the value for supply_chain..mall_address_2_province_address_info.district_mall_name
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public void setDistrictMallName(String districtMallName) {
        this.districtMallName = districtMallName;
    }

    /**
     * This method returns the value of the database column supply_chain..mall_address_2_province_address_info.city_mall_name
     *
     * @return the value of supply_chain..mall_address_2_province_address_info.city_mall_name
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public String getCityMallName() {
        return cityMallName;
    }

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public MallAddress2ProvinceAddressInfo withCityMallName(String cityMallName) {
        this.setCityMallName(cityMallName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mall_address_2_province_address_info.city_mall_name
     *
     * @param cityMallName the value for supply_chain..mall_address_2_province_address_info.city_mall_name
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public void setCityMallName(String cityMallName) {
        this.cityMallName = cityMallName;
    }

    /**
     * This method returns the value of the database column supply_chain..mall_address_2_province_address_info.city_province_name
     *
     * @return the value of supply_chain..mall_address_2_province_address_info.city_province_name
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public String getCityProvinceName() {
        return cityProvinceName;
    }

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public MallAddress2ProvinceAddressInfo withCityProvinceName(String cityProvinceName) {
        this.setCityProvinceName(cityProvinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mall_address_2_province_address_info.city_province_name
     *
     * @param cityProvinceName the value for supply_chain..mall_address_2_province_address_info.city_province_name
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public void setCityProvinceName(String cityProvinceName) {
        this.cityProvinceName = cityProvinceName;
    }

    /**
     * This method returns the value of the database column supply_chain..mall_address_2_province_address_info.city_province_code
     *
     * @return the value of supply_chain..mall_address_2_province_address_info.city_province_code
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public String getCityProvinceCode() {
        return cityProvinceCode;
    }

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public MallAddress2ProvinceAddressInfo withCityProvinceCode(String cityProvinceCode) {
        this.setCityProvinceCode(cityProvinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mall_address_2_province_address_info.city_province_code
     *
     * @param cityProvinceCode the value for supply_chain..mall_address_2_province_address_info.city_province_code
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public void setCityProvinceCode(String cityProvinceCode) {
        this.cityProvinceCode = cityProvinceCode;
    }

    /**
     * This method returns the value of the database column supply_chain..mall_address_2_province_address_info.city_mall_code
     *
     * @return the value of supply_chain..mall_address_2_province_address_info.city_mall_code
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public String getCityMallCode() {
        return cityMallCode;
    }

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public MallAddress2ProvinceAddressInfo withCityMallCode(String cityMallCode) {
        this.setCityMallCode(cityMallCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mall_address_2_province_address_info.city_mall_code
     *
     * @param cityMallCode the value for supply_chain..mall_address_2_province_address_info.city_mall_code
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public void setCityMallCode(String cityMallCode) {
        this.cityMallCode = cityMallCode;
    }

    /**
     * This method returns the value of the database column supply_chain..mall_address_2_province_address_info.province_name
     *
     * @return the value of supply_chain..mall_address_2_province_address_info.province_name
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public MallAddress2ProvinceAddressInfo withProvinceName(String provinceName) {
        this.setProvinceName(provinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mall_address_2_province_address_info.province_name
     *
     * @param provinceName the value for supply_chain..mall_address_2_province_address_info.province_name
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    /**
     * This method returns the value of the database column supply_chain..mall_address_2_province_address_info.province_province_code
     *
     * @return the value of supply_chain..mall_address_2_province_address_info.province_province_code
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public String getProvinceProvinceCode() {
        return provinceProvinceCode;
    }

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public MallAddress2ProvinceAddressInfo withProvinceProvinceCode(String provinceProvinceCode) {
        this.setProvinceProvinceCode(provinceProvinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mall_address_2_province_address_info.province_province_code
     *
     * @param provinceProvinceCode the value for supply_chain..mall_address_2_province_address_info.province_province_code
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public void setProvinceProvinceCode(String provinceProvinceCode) {
        this.provinceProvinceCode = provinceProvinceCode;
    }

    /**
     * This method returns the value of the database column supply_chain..mall_address_2_province_address_info.province_mall_code
     *
     * @return the value of supply_chain..mall_address_2_province_address_info.province_mall_code
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public String getProvinceMallCode() {
        return provinceMallCode;
    }

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public MallAddress2ProvinceAddressInfo withProvinceMallCode(String provinceMallCode) {
        this.setProvinceMallCode(provinceMallCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mall_address_2_province_address_info.province_mall_code
     *
     * @param provinceMallCode the value for supply_chain..mall_address_2_province_address_info.province_mall_code
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public void setProvinceMallCode(String provinceMallCode) {
        this.provinceMallCode = provinceMallCode;
    }

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", districtProvinceName=").append(districtProvinceName);
        sb.append(", districtProvinceCode=").append(districtProvinceCode);
        sb.append(", districtMallCode=").append(districtMallCode);
        sb.append(", districtMallName=").append(districtMallName);
        sb.append(", cityMallName=").append(cityMallName);
        sb.append(", cityProvinceName=").append(cityProvinceName);
        sb.append(", cityProvinceCode=").append(cityProvinceCode);
        sb.append(", cityMallCode=").append(cityMallCode);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", provinceProvinceCode=").append(provinceProvinceCode);
        sb.append(", provinceMallCode=").append(provinceMallCode);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MallAddress2ProvinceAddressInfo other = (MallAddress2ProvinceAddressInfo) that;
        return (this.getDistrictProvinceName() == null ? other.getDistrictProvinceName() == null : this.getDistrictProvinceName().equals(other.getDistrictProvinceName()))
            && (this.getDistrictProvinceCode() == null ? other.getDistrictProvinceCode() == null : this.getDistrictProvinceCode().equals(other.getDistrictProvinceCode()))
            && (this.getDistrictMallCode() == null ? other.getDistrictMallCode() == null : this.getDistrictMallCode().equals(other.getDistrictMallCode()))
            && (this.getDistrictMallName() == null ? other.getDistrictMallName() == null : this.getDistrictMallName().equals(other.getDistrictMallName()))
            && (this.getCityMallName() == null ? other.getCityMallName() == null : this.getCityMallName().equals(other.getCityMallName()))
            && (this.getCityProvinceName() == null ? other.getCityProvinceName() == null : this.getCityProvinceName().equals(other.getCityProvinceName()))
            && (this.getCityProvinceCode() == null ? other.getCityProvinceCode() == null : this.getCityProvinceCode().equals(other.getCityProvinceCode()))
            && (this.getCityMallCode() == null ? other.getCityMallCode() == null : this.getCityMallCode().equals(other.getCityMallCode()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getProvinceProvinceCode() == null ? other.getProvinceProvinceCode() == null : this.getProvinceProvinceCode().equals(other.getProvinceProvinceCode()))
            && (this.getProvinceMallCode() == null ? other.getProvinceMallCode() == null : this.getProvinceMallCode().equals(other.getProvinceMallCode()));
    }

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getDistrictProvinceName() == null) ? 0 : getDistrictProvinceName().hashCode());
        result = prime * result + ((getDistrictProvinceCode() == null) ? 0 : getDistrictProvinceCode().hashCode());
        result = prime * result + ((getDistrictMallCode() == null) ? 0 : getDistrictMallCode().hashCode());
        result = prime * result + ((getDistrictMallName() == null) ? 0 : getDistrictMallName().hashCode());
        result = prime * result + ((getCityMallName() == null) ? 0 : getCityMallName().hashCode());
        result = prime * result + ((getCityProvinceName() == null) ? 0 : getCityProvinceName().hashCode());
        result = prime * result + ((getCityProvinceCode() == null) ? 0 : getCityProvinceCode().hashCode());
        result = prime * result + ((getCityMallCode() == null) ? 0 : getCityMallCode().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getProvinceProvinceCode() == null) ? 0 : getProvinceProvinceCode().hashCode());
        result = prime * result + ((getProvinceMallCode() == null) ? 0 : getProvinceMallCode().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Fri Jan 06 10:19:32 CST 2023
     */
    public enum Column {
        districtProvinceName("district_province_name", "districtProvinceName", "VARCHAR", false),
        districtProvinceCode("district_province_code", "districtProvinceCode", "VARCHAR", false),
        districtMallCode("district_mall_code", "districtMallCode", "VARCHAR", false),
        districtMallName("district_mall_name", "districtMallName", "VARCHAR", false),
        cityMallName("city_mall_name", "cityMallName", "VARCHAR", false),
        cityProvinceName("city_province_name", "cityProvinceName", "VARCHAR", false),
        cityProvinceCode("city_province_code", "cityProvinceCode", "VARCHAR", false),
        cityMallCode("city_mall_code", "cityMallCode", "VARCHAR", false),
        provinceName("province_name", "provinceName", "VARCHAR", false),
        provinceProvinceCode("province_province_code", "provinceProvinceCode", "VARCHAR", false),
        provinceMallCode("province_mall_code", "provinceMallCode", "VARCHAR", false);

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        private final String column;

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Fri Jan 06 10:19:32 CST 2023
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}