package com.chinamobile.iot.sc.dao.ext;

import com.chinamobile.iot.sc.pojo.dto.OrderCooperatorInfoHistoryByGroupDTO;
import com.chinamobile.iot.sc.pojo.dto.OrderCooperatorInfoHistoryDTO;
import com.chinamobile.iot.sc.pojo.param.OrderCooperatorInfoHistoryByGroupParam;
import com.chinamobile.iot.sc.pojo.param.OrderCooperatorInfoHistoryParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 * @description 订单和合作伙伴关系扩展类
 */
public interface OrderCooperatorRelationHistoryMapperExt {

    /**
     * 获取组装后的原子订单和合作伙伴历史列表
     *
     * @param orderCooperatorInfoHistoryByGroupParam
     * @return
     */
    List<OrderCooperatorInfoHistoryByGroupDTO> listCooperatorInfoHistoryByGroup(@Param(value = "orderCooperatorInfoHistoryByGroupParam") OrderCooperatorInfoHistoryByGroupParam orderCooperatorInfoHistoryByGroupParam);

    /**
     * 获取原子订单和合作伙伴历史列表
     *
     * @param orderCooperatorInfoHistoryParam
     * @return
     */
    List<OrderCooperatorInfoHistoryDTO> listCooperatorInfoHistory(@Param(value = "orderCooperatorInfoHistoryParam") OrderCooperatorInfoHistoryParam orderCooperatorInfoHistoryParam);

}
