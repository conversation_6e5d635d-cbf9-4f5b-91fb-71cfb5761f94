package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/22
 * @description 未通过的上架请求复制重新上架
 */
@Data
public class NewProductCopyOnlineParam {

    /**
     * 新产品引入申请表id
     */
    @NotEmpty(message = "新产品引入申请表id不能为空")
    private String newProductRequestId;

    /**
     * 商品套餐信息id
     */
    @NotEmpty(message = "商品套餐信息id不能为空")
    private String comboInfoId;
}
