package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.ProvinceMaterialInfo;
import com.chinamobile.iot.sc.pojo.ProvinceMaterialInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProvinceMaterialInfoMapper {
    /**
     *
     * @mbg.generated Mon Mar 27 14:42:50 CST 2023
     */
    long countByExample(ProvinceMaterialInfoExample example);

    /**
     *
     * @mbg.generated Mon Mar 27 14:42:50 CST 2023
     */
    int deleteByExample(ProvinceMaterialInfoExample example);

    /**
     *
     * @mbg.generated Mon Mar 27 14:42:50 CST 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Mar 27 14:42:50 CST 2023
     */
    int insert(ProvinceMaterialInfo record);

    /**
     *
     * @mbg.generated Mon Mar 27 14:42:50 CST 2023
     */
    int insertSelective(ProvinceMaterialInfo record);

    /**
     *
     * @mbg.generated Mon Mar 27 14:42:50 CST 2023
     */
    List<ProvinceMaterialInfo> selectByExample(ProvinceMaterialInfoExample example);

    /**
     *
     * @mbg.generated Mon Mar 27 14:42:50 CST 2023
     */
    ProvinceMaterialInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Mar 27 14:42:50 CST 2023
     */
    int updateByExampleSelective(@Param("record") ProvinceMaterialInfo record, @Param("example") ProvinceMaterialInfoExample example);

    /**
     *
     * @mbg.generated Mon Mar 27 14:42:50 CST 2023
     */
    int updateByExample(@Param("record") ProvinceMaterialInfo record, @Param("example") ProvinceMaterialInfoExample example);

    /**
     *
     * @mbg.generated Mon Mar 27 14:42:50 CST 2023
     */
    int updateByPrimaryKeySelective(ProvinceMaterialInfo record);

    /**
     *
     * @mbg.generated Mon Mar 27 14:42:50 CST 2023
     */
    int updateByPrimaryKey(ProvinceMaterialInfo record);

    /**
     *
     * @mbg.generated Mon Mar 27 14:42:50 CST 2023
     */
    int batchInsert(@Param("list") List<ProvinceMaterialInfo> list);

    /**
     *
     * @mbg.generated Mon Mar 27 14:42:50 CST 2023
     */
    int batchInsertSelective(@Param("list") List<ProvinceMaterialInfo> list, @Param("selective") ProvinceMaterialInfo.Column ... selective);
}