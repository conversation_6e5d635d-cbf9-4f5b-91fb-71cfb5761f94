package com.chinamobile.iot.sc.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/19
 * @description 导入保理不可用订单实体类
 */
@Data
@ExcelTarget(value = "baoliOrderNotUseImport")
public class ImportBaoliOrderNotUseDTO implements IExcelModel, Serializable {

    @Excel(name = "订单号")
    private String orderId;

    private String errorMsg;

    @Override
    public String getErrorMsg() {
        return errorMsg;
    }

    @Override
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
