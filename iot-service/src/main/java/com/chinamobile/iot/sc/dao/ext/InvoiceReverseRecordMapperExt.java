package com.chinamobile.iot.sc.dao.ext;

import com.chinamobile.iot.sc.pojo.mapper.InvoiceReverseRecDO;
import com.chinamobile.iot.sc.pojo.mapper.InvoiceReverseRecDetailDO;
import com.chinamobile.iot.sc.response.web.invoice.Data4UnRedFlush;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface InvoiceReverseRecordMapperExt {
    List<InvoiceReverseRecDO> page4InvoRevRec(@Param("userIdList") List<String> userIdList, String invoiceReverseId,
                                              String orderId, Integer status,String orderSeq,
                                              Integer pageIndex, Integer num);

    Long pageCount4InvoRevRec(@Param("userIdList") List<String> userIdList, String invoiceReverseId,
                              String orderId, Integer status,String orderSeq);

    List<Data4UnRedFlush> getUnRedFlushRec(@Param("userIdList") List<String> userIdList, Integer status);

    /**
     * 根据订单ID获取最新的发票冲红详情
     * @param orderId
     * @return
     */
    InvoiceReverseRecDetailDO getInvoiceReverseRecordByOrderId(String orderId);
}
