package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.ProductFlowInstanceAttachment;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceAttachmentExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProductFlowInstanceAttachmentMapper {
    /**
     *
     * @mbg.generated Thu Apr 10 10:13:18 CST 2025
     */
    long countByExample(ProductFlowInstanceAttachmentExample example);

    /**
     *
     * @mbg.generated Thu Apr 10 10:13:18 CST 2025
     */
    int deleteByExample(ProductFlowInstanceAttachmentExample example);

    /**
     *
     * @mbg.generated Thu Apr 10 10:13:18 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Apr 10 10:13:18 CST 2025
     */
    int insert(ProductFlowInstanceAttachment record);

    /**
     *
     * @mbg.generated Thu Apr 10 10:13:18 CST 2025
     */
    int insertSelective(ProductFlowInstanceAttachment record);

    /**
     *
     * @mbg.generated Thu Apr 10 10:13:18 CST 2025
     */
    List<ProductFlowInstanceAttachment> selectByExample(ProductFlowInstanceAttachmentExample example);

    /**
     *
     * @mbg.generated Thu Apr 10 10:13:18 CST 2025
     */
    ProductFlowInstanceAttachment selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Apr 10 10:13:18 CST 2025
     */
    int updateByExampleSelective(@Param("record") ProductFlowInstanceAttachment record, @Param("example") ProductFlowInstanceAttachmentExample example);

    /**
     *
     * @mbg.generated Thu Apr 10 10:13:18 CST 2025
     */
    int updateByExample(@Param("record") ProductFlowInstanceAttachment record, @Param("example") ProductFlowInstanceAttachmentExample example);

    /**
     *
     * @mbg.generated Thu Apr 10 10:13:18 CST 2025
     */
    int updateByPrimaryKeySelective(ProductFlowInstanceAttachment record);

    /**
     *
     * @mbg.generated Thu Apr 10 10:13:18 CST 2025
     */
    int updateByPrimaryKey(ProductFlowInstanceAttachment record);

    /**
     *
     * @mbg.generated Thu Apr 10 10:13:18 CST 2025
     */
    int batchInsert(@Param("list") List<ProductFlowInstanceAttachment> list);

    /**
     *
     * @mbg.generated Thu Apr 10 10:13:18 CST 2025
     */
    int batchInsertSelective(@Param("list") List<ProductFlowInstanceAttachment> list, @Param("selective") ProductFlowInstanceAttachment.Column ... selective);
}