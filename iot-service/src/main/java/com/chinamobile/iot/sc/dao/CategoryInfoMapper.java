package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.CategoryInfo;
import com.chinamobile.iot.sc.pojo.CategoryInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CategoryInfoMapper {
    long countByExample(CategoryInfoExample example);

    int deleteByExample(CategoryInfoExample example);

    int deleteByPrimaryKey(String id);

    int insert(CategoryInfo record);

    int insertSelective(CategoryInfo record);

    List<CategoryInfo> selectByExample(CategoryInfoExample example);

    CategoryInfo selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") CategoryInfo record, @Param("example") CategoryInfoExample example);

    int updateByExample(@Param("record") CategoryInfo record, @Param("example") CategoryInfoExample example);

    int updateByPrimaryKeySelective(CategoryInfo record);

    int updateByPrimaryKey(CategoryInfo record);

    int batchInsert(@Param("list") List<CategoryInfo> list);

    int batchInsertSelective(@Param("list") List<CategoryInfo> list, @Param("selective") CategoryInfo.Column ... selective);
}