package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProductFlowInstanceDirectoryExample {
    /**
     * Corresponding to the database table supply_chain..product_flow_instance_directory
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    protected String orderByClause;

    /**
     * Corresponding to the database table supply_chain..product_flow_instance_directory
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    protected boolean distinct;

    /**
     * Corresponding to the database table supply_chain..product_flow_instance_directory
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public ProductFlowInstanceDirectoryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public ProductFlowInstanceDirectoryExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public ProductFlowInstanceDirectoryExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public static Criteria newAndCreateCriteria() {
        ProductFlowInstanceDirectoryExample example = new ProductFlowInstanceDirectoryExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public ProductFlowInstanceDirectoryExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public ProductFlowInstanceDirectoryExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     * Corresponding to the database table supply_chain..product_flow_instance_directory
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdIsNull() {
            addCriterion("flow_instance_id is null");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdIsNotNull() {
            addCriterion("flow_instance_id is not null");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdEqualTo(String value) {
            addCriterion("flow_instance_id =", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("flow_instance_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotEqualTo(String value) {
            addCriterion("flow_instance_id <>", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("flow_instance_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThan(String value) {
            addCriterion("flow_instance_id >", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("flow_instance_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("flow_instance_id >=", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("flow_instance_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThan(String value) {
            addCriterion("flow_instance_id <", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("flow_instance_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThanOrEqualTo(String value) {
            addCriterion("flow_instance_id <=", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("flow_instance_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLike(String value) {
            addCriterion("flow_instance_id like", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotLike(String value) {
            addCriterion("flow_instance_id not like", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdIn(List<String> values) {
            addCriterion("flow_instance_id in", values, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotIn(List<String> values) {
            addCriterion("flow_instance_id not in", values, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdBetween(String value1, String value2) {
            addCriterion("flow_instance_id between", value1, value2, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotBetween(String value1, String value2) {
            addCriterion("flow_instance_id not between", value1, value2, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdIsNull() {
            addCriterion("first_directory_id is null");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdIsNotNull() {
            addCriterion("first_directory_id is not null");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdEqualTo(String value) {
            addCriterion("first_directory_id =", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("first_directory_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdNotEqualTo(String value) {
            addCriterion("first_directory_id <>", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdNotEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("first_directory_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdGreaterThan(String value) {
            addCriterion("first_directory_id >", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdGreaterThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("first_directory_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("first_directory_id >=", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdGreaterThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("first_directory_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLessThan(String value) {
            addCriterion("first_directory_id <", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLessThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("first_directory_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLessThanOrEqualTo(String value) {
            addCriterion("first_directory_id <=", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLessThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("first_directory_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLike(String value) {
            addCriterion("first_directory_id like", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdNotLike(String value) {
            addCriterion("first_directory_id not like", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdIn(List<String> values) {
            addCriterion("first_directory_id in", values, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdNotIn(List<String> values) {
            addCriterion("first_directory_id not in", values, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdBetween(String value1, String value2) {
            addCriterion("first_directory_id between", value1, value2, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdNotBetween(String value1, String value2) {
            addCriterion("first_directory_id not between", value1, value2, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameIsNull() {
            addCriterion("first_directory_name is null");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameIsNotNull() {
            addCriterion("first_directory_name is not null");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameEqualTo(String value) {
            addCriterion("first_directory_name =", value, "firstDirectoryName");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("first_directory_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameNotEqualTo(String value) {
            addCriterion("first_directory_name <>", value, "firstDirectoryName");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameNotEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("first_directory_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameGreaterThan(String value) {
            addCriterion("first_directory_name >", value, "firstDirectoryName");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameGreaterThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("first_directory_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("first_directory_name >=", value, "firstDirectoryName");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameGreaterThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("first_directory_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameLessThan(String value) {
            addCriterion("first_directory_name <", value, "firstDirectoryName");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameLessThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("first_directory_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameLessThanOrEqualTo(String value) {
            addCriterion("first_directory_name <=", value, "firstDirectoryName");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameLessThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("first_directory_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameLike(String value) {
            addCriterion("first_directory_name like", value, "firstDirectoryName");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameNotLike(String value) {
            addCriterion("first_directory_name not like", value, "firstDirectoryName");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameIn(List<String> values) {
            addCriterion("first_directory_name in", values, "firstDirectoryName");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameNotIn(List<String> values) {
            addCriterion("first_directory_name not in", values, "firstDirectoryName");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameBetween(String value1, String value2) {
            addCriterion("first_directory_name between", value1, value2, "firstDirectoryName");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameNotBetween(String value1, String value2) {
            addCriterion("first_directory_name not between", value1, value2, "firstDirectoryName");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdIsNull() {
            addCriterion("second_directory_id is null");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdIsNotNull() {
            addCriterion("second_directory_id is not null");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdEqualTo(String value) {
            addCriterion("second_directory_id =", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("second_directory_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdNotEqualTo(String value) {
            addCriterion("second_directory_id <>", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdNotEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("second_directory_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdGreaterThan(String value) {
            addCriterion("second_directory_id >", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdGreaterThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("second_directory_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("second_directory_id >=", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdGreaterThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("second_directory_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLessThan(String value) {
            addCriterion("second_directory_id <", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLessThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("second_directory_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLessThanOrEqualTo(String value) {
            addCriterion("second_directory_id <=", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLessThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("second_directory_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLike(String value) {
            addCriterion("second_directory_id like", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdNotLike(String value) {
            addCriterion("second_directory_id not like", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdIn(List<String> values) {
            addCriterion("second_directory_id in", values, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdNotIn(List<String> values) {
            addCriterion("second_directory_id not in", values, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdBetween(String value1, String value2) {
            addCriterion("second_directory_id between", value1, value2, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdNotBetween(String value1, String value2) {
            addCriterion("second_directory_id not between", value1, value2, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameIsNull() {
            addCriterion("second_directory_name is null");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameIsNotNull() {
            addCriterion("second_directory_name is not null");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameEqualTo(String value) {
            addCriterion("second_directory_name =", value, "secondDirectoryName");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("second_directory_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameNotEqualTo(String value) {
            addCriterion("second_directory_name <>", value, "secondDirectoryName");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameNotEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("second_directory_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameGreaterThan(String value) {
            addCriterion("second_directory_name >", value, "secondDirectoryName");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameGreaterThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("second_directory_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("second_directory_name >=", value, "secondDirectoryName");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameGreaterThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("second_directory_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameLessThan(String value) {
            addCriterion("second_directory_name <", value, "secondDirectoryName");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameLessThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("second_directory_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameLessThanOrEqualTo(String value) {
            addCriterion("second_directory_name <=", value, "secondDirectoryName");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameLessThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("second_directory_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameLike(String value) {
            addCriterion("second_directory_name like", value, "secondDirectoryName");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameNotLike(String value) {
            addCriterion("second_directory_name not like", value, "secondDirectoryName");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameIn(List<String> values) {
            addCriterion("second_directory_name in", values, "secondDirectoryName");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameNotIn(List<String> values) {
            addCriterion("second_directory_name not in", values, "secondDirectoryName");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameBetween(String value1, String value2) {
            addCriterion("second_directory_name between", value1, value2, "secondDirectoryName");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameNotBetween(String value1, String value2) {
            addCriterion("second_directory_name not between", value1, value2, "secondDirectoryName");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdIsNull() {
            addCriterion("third_directory_id is null");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdIsNotNull() {
            addCriterion("third_directory_id is not null");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdEqualTo(String value) {
            addCriterion("third_directory_id =", value, "thirdDirectoryId");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("third_directory_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdNotEqualTo(String value) {
            addCriterion("third_directory_id <>", value, "thirdDirectoryId");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdNotEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("third_directory_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdGreaterThan(String value) {
            addCriterion("third_directory_id >", value, "thirdDirectoryId");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdGreaterThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("third_directory_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("third_directory_id >=", value, "thirdDirectoryId");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdGreaterThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("third_directory_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdLessThan(String value) {
            addCriterion("third_directory_id <", value, "thirdDirectoryId");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdLessThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("third_directory_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdLessThanOrEqualTo(String value) {
            addCriterion("third_directory_id <=", value, "thirdDirectoryId");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdLessThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("third_directory_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdLike(String value) {
            addCriterion("third_directory_id like", value, "thirdDirectoryId");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdNotLike(String value) {
            addCriterion("third_directory_id not like", value, "thirdDirectoryId");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdIn(List<String> values) {
            addCriterion("third_directory_id in", values, "thirdDirectoryId");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdNotIn(List<String> values) {
            addCriterion("third_directory_id not in", values, "thirdDirectoryId");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdBetween(String value1, String value2) {
            addCriterion("third_directory_id between", value1, value2, "thirdDirectoryId");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdNotBetween(String value1, String value2) {
            addCriterion("third_directory_id not between", value1, value2, "thirdDirectoryId");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameIsNull() {
            addCriterion("third_directory_name is null");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameIsNotNull() {
            addCriterion("third_directory_name is not null");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameEqualTo(String value) {
            addCriterion("third_directory_name =", value, "thirdDirectoryName");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("third_directory_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameNotEqualTo(String value) {
            addCriterion("third_directory_name <>", value, "thirdDirectoryName");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameNotEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("third_directory_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameGreaterThan(String value) {
            addCriterion("third_directory_name >", value, "thirdDirectoryName");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameGreaterThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("third_directory_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("third_directory_name >=", value, "thirdDirectoryName");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameGreaterThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("third_directory_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameLessThan(String value) {
            addCriterion("third_directory_name <", value, "thirdDirectoryName");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameLessThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("third_directory_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameLessThanOrEqualTo(String value) {
            addCriterion("third_directory_name <=", value, "thirdDirectoryName");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameLessThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("third_directory_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameLike(String value) {
            addCriterion("third_directory_name like", value, "thirdDirectoryName");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameNotLike(String value) {
            addCriterion("third_directory_name not like", value, "thirdDirectoryName");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameIn(List<String> values) {
            addCriterion("third_directory_name in", values, "thirdDirectoryName");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameNotIn(List<String> values) {
            addCriterion("third_directory_name not in", values, "thirdDirectoryName");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameBetween(String value1, String value2) {
            addCriterion("third_directory_name between", value1, value2, "thirdDirectoryName");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameNotBetween(String value1, String value2) {
            addCriterion("third_directory_name not between", value1, value2, "thirdDirectoryName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(ProductFlowInstanceDirectory.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLikeInsensitive(String value) {
            addCriterion("upper(flow_instance_id) like", value.toUpperCase(), "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLikeInsensitive(String value) {
            addCriterion("upper(first_directory_id) like", value.toUpperCase(), "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryNameLikeInsensitive(String value) {
            addCriterion("upper(first_directory_name) like", value.toUpperCase(), "firstDirectoryName");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLikeInsensitive(String value) {
            addCriterion("upper(second_directory_id) like", value.toUpperCase(), "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryNameLikeInsensitive(String value) {
            addCriterion("upper(second_directory_name) like", value.toUpperCase(), "secondDirectoryName");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryIdLikeInsensitive(String value) {
            addCriterion("upper(third_directory_id) like", value.toUpperCase(), "thirdDirectoryId");
            return (Criteria) this;
        }

        public Criteria andThirdDirectoryNameLikeInsensitive(String value) {
            addCriterion("upper(third_directory_name) like", value.toUpperCase(), "thirdDirectoryName");
            return (Criteria) this;
        }
    }

    /**
     * Corresponding to the database table supply_chain..product_flow_instance_directory
     *
     * @mbg.generated do_not_delete_during_merge Tue Apr 22 16:41:11 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         * Corresponding to the database table supply_chain..product_flow_instance_directory
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        private ProductFlowInstanceDirectoryExample example;

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        protected Criteria(ProductFlowInstanceDirectoryExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        public ProductFlowInstanceDirectoryExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue Apr 22 16:41:11 CST 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     * Corresponding to the database table supply_chain..product_flow_instance_directory
     *
     * @mbg.generated Tue Apr 22 16:41:11 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue Apr 22 16:41:11 CST 2025
         */
        void example(com.chinamobile.iot.sc.pojo.ProductFlowInstanceDirectoryExample example);
    }
}