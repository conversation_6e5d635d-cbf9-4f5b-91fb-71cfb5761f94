package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.List;

public class ContractProvinceInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public ContractProvinceInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public ContractProvinceInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    public ContractProvinceInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    public static Criteria newAndCreateCriteria() {
        ContractProvinceInfoExample example = new ContractProvinceInfoExample();
        return example.createCriteria();
    }

    public ContractProvinceInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    public ContractProvinceInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andMallCodeIsNull() {
            addCriterion("mall_code is null");
            return (Criteria) this;
        }

        public Criteria andMallCodeIsNotNull() {
            addCriterion("mall_code is not null");
            return (Criteria) this;
        }

        public Criteria andMallCodeEqualTo(String value) {
            addCriterion("mall_code =", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("mall_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallCodeNotEqualTo(String value) {
            addCriterion("mall_code <>", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeNotEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("mall_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallCodeGreaterThan(String value) {
            addCriterion("mall_code >", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeGreaterThanColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("mall_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallCodeGreaterThanOrEqualTo(String value) {
            addCriterion("mall_code >=", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeGreaterThanOrEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("mall_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallCodeLessThan(String value) {
            addCriterion("mall_code <", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeLessThanColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("mall_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallCodeLessThanOrEqualTo(String value) {
            addCriterion("mall_code <=", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeLessThanOrEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("mall_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallCodeLike(String value) {
            addCriterion("mall_code like", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeNotLike(String value) {
            addCriterion("mall_code not like", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeIn(List<String> values) {
            addCriterion("mall_code in", values, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeNotIn(List<String> values) {
            addCriterion("mall_code not in", values, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeBetween(String value1, String value2) {
            addCriterion("mall_code between", value1, value2, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeNotBetween(String value1, String value2) {
            addCriterion("mall_code not between", value1, value2, "mallCode");
            return (Criteria) this;
        }

        public Criteria andK3CodeIsNull() {
            addCriterion("k3_code is null");
            return (Criteria) this;
        }

        public Criteria andK3CodeIsNotNull() {
            addCriterion("k3_code is not null");
            return (Criteria) this;
        }

        public Criteria andK3CodeEqualTo(String value) {
            addCriterion("k3_code =", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("k3_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CodeNotEqualTo(String value) {
            addCriterion("k3_code <>", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeNotEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("k3_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CodeGreaterThan(String value) {
            addCriterion("k3_code >", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeGreaterThanColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("k3_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CodeGreaterThanOrEqualTo(String value) {
            addCriterion("k3_code >=", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeGreaterThanOrEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("k3_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CodeLessThan(String value) {
            addCriterion("k3_code <", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeLessThanColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("k3_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CodeLessThanOrEqualTo(String value) {
            addCriterion("k3_code <=", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeLessThanOrEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("k3_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CodeLike(String value) {
            addCriterion("k3_code like", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeNotLike(String value) {
            addCriterion("k3_code not like", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeIn(List<String> values) {
            addCriterion("k3_code in", values, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeNotIn(List<String> values) {
            addCriterion("k3_code not in", values, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeBetween(String value1, String value2) {
            addCriterion("k3_code between", value1, value2, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeNotBetween(String value1, String value2) {
            addCriterion("k3_code not between", value1, value2, "k3Code");
            return (Criteria) this;
        }

        public Criteria andMallNameIsNull() {
            addCriterion("mall_name is null");
            return (Criteria) this;
        }

        public Criteria andMallNameIsNotNull() {
            addCriterion("mall_name is not null");
            return (Criteria) this;
        }

        public Criteria andMallNameEqualTo(String value) {
            addCriterion("mall_name =", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("mall_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallNameNotEqualTo(String value) {
            addCriterion("mall_name <>", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameNotEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("mall_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallNameGreaterThan(String value) {
            addCriterion("mall_name >", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameGreaterThanColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("mall_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallNameGreaterThanOrEqualTo(String value) {
            addCriterion("mall_name >=", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameGreaterThanOrEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("mall_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallNameLessThan(String value) {
            addCriterion("mall_name <", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameLessThanColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("mall_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallNameLessThanOrEqualTo(String value) {
            addCriterion("mall_name <=", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameLessThanOrEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("mall_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallNameLike(String value) {
            addCriterion("mall_name like", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameNotLike(String value) {
            addCriterion("mall_name not like", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameIn(List<String> values) {
            addCriterion("mall_name in", values, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameNotIn(List<String> values) {
            addCriterion("mall_name not in", values, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameBetween(String value1, String value2) {
            addCriterion("mall_name between", value1, value2, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameNotBetween(String value1, String value2) {
            addCriterion("mall_name not between", value1, value2, "mallName");
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeIsNull() {
            addCriterion("capital_mall_code is null");
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeIsNotNull() {
            addCriterion("capital_mall_code is not null");
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeEqualTo(String value) {
            addCriterion("capital_mall_code =", value, "capitalMallCode");
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_mall_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeNotEqualTo(String value) {
            addCriterion("capital_mall_code <>", value, "capitalMallCode");
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeNotEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_mall_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeGreaterThan(String value) {
            addCriterion("capital_mall_code >", value, "capitalMallCode");
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeGreaterThanColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_mall_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeGreaterThanOrEqualTo(String value) {
            addCriterion("capital_mall_code >=", value, "capitalMallCode");
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeGreaterThanOrEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_mall_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeLessThan(String value) {
            addCriterion("capital_mall_code <", value, "capitalMallCode");
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeLessThanColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_mall_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeLessThanOrEqualTo(String value) {
            addCriterion("capital_mall_code <=", value, "capitalMallCode");
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeLessThanOrEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_mall_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeLike(String value) {
            addCriterion("capital_mall_code like", value, "capitalMallCode");
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeNotLike(String value) {
            addCriterion("capital_mall_code not like", value, "capitalMallCode");
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeIn(List<String> values) {
            addCriterion("capital_mall_code in", values, "capitalMallCode");
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeNotIn(List<String> values) {
            addCriterion("capital_mall_code not in", values, "capitalMallCode");
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeBetween(String value1, String value2) {
            addCriterion("capital_mall_code between", value1, value2, "capitalMallCode");
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeNotBetween(String value1, String value2) {
            addCriterion("capital_mall_code not between", value1, value2, "capitalMallCode");
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameIsNull() {
            addCriterion("capital_mall_name is null");
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameIsNotNull() {
            addCriterion("capital_mall_name is not null");
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameEqualTo(String value) {
            addCriterion("capital_mall_name =", value, "capitalMallName");
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_mall_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameNotEqualTo(String value) {
            addCriterion("capital_mall_name <>", value, "capitalMallName");
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameNotEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_mall_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameGreaterThan(String value) {
            addCriterion("capital_mall_name >", value, "capitalMallName");
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameGreaterThanColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_mall_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameGreaterThanOrEqualTo(String value) {
            addCriterion("capital_mall_name >=", value, "capitalMallName");
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameGreaterThanOrEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_mall_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameLessThan(String value) {
            addCriterion("capital_mall_name <", value, "capitalMallName");
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameLessThanColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_mall_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameLessThanOrEqualTo(String value) {
            addCriterion("capital_mall_name <=", value, "capitalMallName");
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameLessThanOrEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_mall_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameLike(String value) {
            addCriterion("capital_mall_name like", value, "capitalMallName");
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameNotLike(String value) {
            addCriterion("capital_mall_name not like", value, "capitalMallName");
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameIn(List<String> values) {
            addCriterion("capital_mall_name in", values, "capitalMallName");
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameNotIn(List<String> values) {
            addCriterion("capital_mall_name not in", values, "capitalMallName");
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameBetween(String value1, String value2) {
            addCriterion("capital_mall_name between", value1, value2, "capitalMallName");
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameNotBetween(String value1, String value2) {
            addCriterion("capital_mall_name not between", value1, value2, "capitalMallName");
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeIsNull() {
            addCriterion("capital_k3_code is null");
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeIsNotNull() {
            addCriterion("capital_k3_code is not null");
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeEqualTo(String value) {
            addCriterion("capital_k3_code =", value, "capitalK3Code");
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_k3_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeNotEqualTo(String value) {
            addCriterion("capital_k3_code <>", value, "capitalK3Code");
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeNotEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_k3_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeGreaterThan(String value) {
            addCriterion("capital_k3_code >", value, "capitalK3Code");
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeGreaterThanColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_k3_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeGreaterThanOrEqualTo(String value) {
            addCriterion("capital_k3_code >=", value, "capitalK3Code");
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeGreaterThanOrEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_k3_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeLessThan(String value) {
            addCriterion("capital_k3_code <", value, "capitalK3Code");
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeLessThanColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_k3_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeLessThanOrEqualTo(String value) {
            addCriterion("capital_k3_code <=", value, "capitalK3Code");
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeLessThanOrEqualToColumn(ContractProvinceInfo.Column column) {
            addCriterion(new StringBuilder("capital_k3_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeLike(String value) {
            addCriterion("capital_k3_code like", value, "capitalK3Code");
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeNotLike(String value) {
            addCriterion("capital_k3_code not like", value, "capitalK3Code");
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeIn(List<String> values) {
            addCriterion("capital_k3_code in", values, "capitalK3Code");
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeNotIn(List<String> values) {
            addCriterion("capital_k3_code not in", values, "capitalK3Code");
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeBetween(String value1, String value2) {
            addCriterion("capital_k3_code between", value1, value2, "capitalK3Code");
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeNotBetween(String value1, String value2) {
            addCriterion("capital_k3_code not between", value1, value2, "capitalK3Code");
            return (Criteria) this;
        }

        public Criteria andMallCodeLikeInsensitive(String value) {
            addCriterion("upper(mall_code) like", value.toUpperCase(), "mallCode");
            return (Criteria) this;
        }

        public Criteria andK3CodeLikeInsensitive(String value) {
            addCriterion("upper(k3_code) like", value.toUpperCase(), "k3Code");
            return (Criteria) this;
        }

        public Criteria andMallNameLikeInsensitive(String value) {
            addCriterion("upper(mall_name) like", value.toUpperCase(), "mallName");
            return (Criteria) this;
        }

        public Criteria andCapitalMallCodeLikeInsensitive(String value) {
            addCriterion("upper(capital_mall_code) like", value.toUpperCase(), "capitalMallCode");
            return (Criteria) this;
        }

        public Criteria andCapitalMallNameLikeInsensitive(String value) {
            addCriterion("upper(capital_mall_name) like", value.toUpperCase(), "capitalMallName");
            return (Criteria) this;
        }

        public Criteria andCapitalK3CodeLikeInsensitive(String value) {
            addCriterion("upper(capital_k3_code) like", value.toUpperCase(), "capitalK3Code");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        private ContractProvinceInfoExample example;

        protected Criteria(ContractProvinceInfoExample example) {
            super();
            this.example = example;
        }

        public ContractProvinceInfoExample example() {
            return this.example;
        }

        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            Criteria add(Criteria add);
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        void example(com.chinamobile.iot.sc.pojo.ContractProvinceInfoExample example);
    }
}