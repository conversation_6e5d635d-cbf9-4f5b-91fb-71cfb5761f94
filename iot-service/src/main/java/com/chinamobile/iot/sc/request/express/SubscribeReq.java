package com.chinamobile.iot.sc.request.express;

import lombok.Data;

/**
 * <AUTHOR> daiguojun
 * @date : 2023/09/25 14:27
 * @description: 快递100订阅参数
 **/
@Data
public class SubscribeReq {

    /**
     * 快递公司的编码
     */
    private String company;

    /**
     * 订阅的快递单号
     */
    private String number;

    /**
     * 授权码
     */
    private String key;

    /**
     * 其他参数组合成的json对象
     */
    private Parameters parameters;

    @Data
    public static class Parameters {

        /**
         * 回调接口的地址，默认仅支持http，如需兼容https请联系快递100技术人员处理
         * */
        private String callbackurl;

        /**
         * 添加此字段表示开通行政区域解析功能。空：关闭（默认），1：开通行政区域解析功能以及物流轨迹增加物流状态名称
         * 4: 开通行政解析功能以及物流轨迹增加物流高级状态名称、状态值并且返回出发、目的及当前城市信息
         * */
        private String resultv2;

        /**
         * 签名用随机字符串，可不填
         * */
        private String salt;

        /**
         * 收、寄件人的电话号码（手机和固定电话均可，只能填写一个，顺丰速运、顺丰快运必填，其他快递公司选填。如座机号码有分机号，分机号无需上传。）
         * */
        private String phone;
    }

}
