package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.K3syncStatisCity;
import com.chinamobile.iot.sc.pojo.K3syncStatisCityExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface K3syncStatisCityMapper {
    /**
     *
     * @mbg.generated Tue Apr 25 14:19:28 CST 2023
     */
    long countByExample(K3syncStatisCityExample example);

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:28 CST 2023
     */
    int deleteByExample(K3syncStatisCityExample example);

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:28 CST 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:28 CST 2023
     */
    int insert(K3syncStatisCity record);

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:28 CST 2023
     */
    int insertSelective(K3syncStatisCity record);

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:28 CST 2023
     */
    List<K3syncStatisCity> selectByExample(K3syncStatisCityExample example);

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:28 CST 2023
     */
    K3syncStatisCity selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:28 CST 2023
     */
    int updateByExampleSelective(@Param("record") K3syncStatisCity record, @Param("example") K3syncStatisCityExample example);

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:28 CST 2023
     */
    int updateByExample(@Param("record") K3syncStatisCity record, @Param("example") K3syncStatisCityExample example);

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:28 CST 2023
     */
    int updateByPrimaryKeySelective(K3syncStatisCity record);

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:28 CST 2023
     */
    int updateByPrimaryKey(K3syncStatisCity record);

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:28 CST 2023
     */
    int batchInsert(@Param("list") List<K3syncStatisCity> list);

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:28 CST 2023
     */
    int batchInsertSelective(@Param("list") List<K3syncStatisCity> list, @Param("selective") K3syncStatisCity.Column ... selective);
}