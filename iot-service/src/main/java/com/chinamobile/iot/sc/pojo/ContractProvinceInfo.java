package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

public class ContractProvinceInfo implements Serializable {
    private String mallCode;

    private String k3Code;

    private String mallName;

    private String capitalMallCode;

    private String capitalMallName;

    private String capitalK3Code;

    private static final long serialVersionUID = 1L;

    public String getMallCode() {
        return mallCode;
    }

    public ContractProvinceInfo withMallCode(String mallCode) {
        this.setMallCode(mallCode);
        return this;
    }

    public void setMallCode(String mallCode) {
        this.mallCode = mallCode == null ? null : mallCode.trim();
    }

    public String getK3Code() {
        return k3Code;
    }

    public ContractProvinceInfo withK3Code(String k3Code) {
        this.setK3Code(k3Code);
        return this;
    }

    public void setK3Code(String k3Code) {
        this.k3Code = k3Code == null ? null : k3Code.trim();
    }

    public String getMallName() {
        return mallName;
    }

    public ContractProvinceInfo withMallName(String mallName) {
        this.setMallName(mallName);
        return this;
    }

    public void setMallName(String mallName) {
        this.mallName = mallName == null ? null : mallName.trim();
    }

    public String getCapitalMallCode() {
        return capitalMallCode;
    }

    public ContractProvinceInfo withCapitalMallCode(String capitalMallCode) {
        this.setCapitalMallCode(capitalMallCode);
        return this;
    }

    public void setCapitalMallCode(String capitalMallCode) {
        this.capitalMallCode = capitalMallCode == null ? null : capitalMallCode.trim();
    }

    public String getCapitalMallName() {
        return capitalMallName;
    }

    public ContractProvinceInfo withCapitalMallName(String capitalMallName) {
        this.setCapitalMallName(capitalMallName);
        return this;
    }

    public void setCapitalMallName(String capitalMallName) {
        this.capitalMallName = capitalMallName == null ? null : capitalMallName.trim();
    }

    public String getCapitalK3Code() {
        return capitalK3Code;
    }

    public ContractProvinceInfo withCapitalK3Code(String capitalK3Code) {
        this.setCapitalK3Code(capitalK3Code);
        return this;
    }

    public void setCapitalK3Code(String capitalK3Code) {
        this.capitalK3Code = capitalK3Code == null ? null : capitalK3Code.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", mallCode=").append(mallCode);
        sb.append(", k3Code=").append(k3Code);
        sb.append(", mallName=").append(mallName);
        sb.append(", capitalMallCode=").append(capitalMallCode);
        sb.append(", capitalMallName=").append(capitalMallName);
        sb.append(", capitalK3Code=").append(capitalK3Code);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ContractProvinceInfo other = (ContractProvinceInfo) that;
        return (this.getMallCode() == null ? other.getMallCode() == null : this.getMallCode().equals(other.getMallCode()))
            && (this.getK3Code() == null ? other.getK3Code() == null : this.getK3Code().equals(other.getK3Code()))
            && (this.getMallName() == null ? other.getMallName() == null : this.getMallName().equals(other.getMallName()))
            && (this.getCapitalMallCode() == null ? other.getCapitalMallCode() == null : this.getCapitalMallCode().equals(other.getCapitalMallCode()))
            && (this.getCapitalMallName() == null ? other.getCapitalMallName() == null : this.getCapitalMallName().equals(other.getCapitalMallName()))
            && (this.getCapitalK3Code() == null ? other.getCapitalK3Code() == null : this.getCapitalK3Code().equals(other.getCapitalK3Code()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getMallCode() == null) ? 0 : getMallCode().hashCode());
        result = prime * result + ((getK3Code() == null) ? 0 : getK3Code().hashCode());
        result = prime * result + ((getMallName() == null) ? 0 : getMallName().hashCode());
        result = prime * result + ((getCapitalMallCode() == null) ? 0 : getCapitalMallCode().hashCode());
        result = prime * result + ((getCapitalMallName() == null) ? 0 : getCapitalMallName().hashCode());
        result = prime * result + ((getCapitalK3Code() == null) ? 0 : getCapitalK3Code().hashCode());
        return result;
    }

    public enum Column {
        mallCode("mall_code", "mallCode", "VARCHAR", false),
        k3Code("k3_code", "k3Code", "VARCHAR", false),
        mallName("mall_name", "mallName", "VARCHAR", false),
        capitalMallCode("capital_mall_code", "capitalMallCode", "VARCHAR", false),
        capitalMallName("capital_mall_name", "capitalMallName", "VARCHAR", false),
        capitalK3Code("capital_k3_code", "capitalK3Code", "VARCHAR", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}