package com.chinamobile.iot.sc.quartz.job;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.quartz.QuartzJobConf;
import com.chinamobile.iot.sc.quartz.QuartzManager;
import lombok.extern.slf4j.Slf4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> xie<PERSON>oh<PERSON>
 * @date : 2022/5/9 17:37
 * @description: 发送开票冲红短信定时Job
 **/
@Slf4j
@Component
@DisallowConcurrentExecution
public class SendInvoiceRushRedJob implements Job {

    @Autowired
    private SmsFeignClient smsFeignClient;
    @Autowired
    private QuartzManager quartzManager;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {

        QuartzJobConf jobConf = JSON.parseObject(context.getJobDetail().getJobDataMap().getString("quartConf"), QuartzJobConf.class);
        log.info("开始执行开票冲红通知短信，订单号：{}", jobConf.getOrderId());
        //订单48小时，发送短信
        Msg4Request msg4Request = new Msg4Request();
        msg4Request.setMobiles(jobConf.getPhone());
        msg4Request.setTemplateId(jobConf.getTemplateId());
        Map<String, String> msgMap = new HashMap<>();
        msgMap.put("orderId", jobConf.getOrderId());
        msg4Request.setMessage(msgMap);
        smsFeignClient.asySendMessage(msg4Request);
        quartzManager.removeQuartzJobByTaskId(jobConf);
        log.info("执行完成开票冲红通知短信，订单号：{}", jobConf.getOrderId());

    }
}
