package com.chinamobile.iot.sc.dao.ext;

import com.chinamobile.iot.sc.pojo.dto.AtomDetailInventoryDTO;
import com.chinamobile.iot.sc.pojo.dto.InventoryDetailCountDTO;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfo;
import com.chinamobile.iot.sc.pojo.param.DkcardxInventoryDetailInfoParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/22
 * @description 卡+X终端库存原子绑定信息mapper扩展类
 */
public interface DkcardxInventoryAtomInfoMapperExt {

    /**
     * 获取原子商品绑定的库存个数列表
     * @return
     */
    List<AtomDetailInventoryDTO> listAtomDetailInventory();

}
