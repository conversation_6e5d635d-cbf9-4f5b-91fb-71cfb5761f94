package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class NewProductRequestOnlineOfflineAfterSaleInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public NewProductRequestOnlineOfflineAfterSaleInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public NewProductRequestOnlineOfflineAfterSaleInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    public NewProductRequestOnlineOfflineAfterSaleInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    public static Criteria newAndCreateCriteria() {
        NewProductRequestOnlineOfflineAfterSaleInfoExample example = new NewProductRequestOnlineOfflineAfterSaleInfoExample();
        return example.createCriteria();
    }

    public NewProductRequestOnlineOfflineAfterSaleInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    public NewProductRequestOnlineOfflineAfterSaleInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdIsNull() {
            addCriterion("new_product_request_id is null");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdIsNotNull() {
            addCriterion("new_product_request_id is not null");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdEqualTo(String value) {
            addCriterion("new_product_request_id =", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("new_product_request_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdNotEqualTo(String value) {
            addCriterion("new_product_request_id <>", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("new_product_request_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdGreaterThan(String value) {
            addCriterion("new_product_request_id >", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("new_product_request_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdGreaterThanOrEqualTo(String value) {
            addCriterion("new_product_request_id >=", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("new_product_request_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdLessThan(String value) {
            addCriterion("new_product_request_id <", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("new_product_request_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdLessThanOrEqualTo(String value) {
            addCriterion("new_product_request_id <=", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("new_product_request_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdLike(String value) {
            addCriterion("new_product_request_id like", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdNotLike(String value) {
            addCriterion("new_product_request_id not like", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdIn(List<String> values) {
            addCriterion("new_product_request_id in", values, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdNotIn(List<String> values) {
            addCriterion("new_product_request_id not in", values, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdBetween(String value1, String value2) {
            addCriterion("new_product_request_id between", value1, value2, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdNotBetween(String value1, String value2) {
            addCriterion("new_product_request_id not between", value1, value2, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andComboInfoIdIsNull() {
            addCriterion("combo_info_id is null");
            return (Criteria) this;
        }

        public Criteria andComboInfoIdIsNotNull() {
            addCriterion("combo_info_id is not null");
            return (Criteria) this;
        }

        public Criteria andComboInfoIdEqualTo(String value) {
            addCriterion("combo_info_id =", value, "comboInfoId");
            return (Criteria) this;
        }

        public Criteria andComboInfoIdEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("combo_info_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andComboInfoIdNotEqualTo(String value) {
            addCriterion("combo_info_id <>", value, "comboInfoId");
            return (Criteria) this;
        }

        public Criteria andComboInfoIdNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("combo_info_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andComboInfoIdGreaterThan(String value) {
            addCriterion("combo_info_id >", value, "comboInfoId");
            return (Criteria) this;
        }

        public Criteria andComboInfoIdGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("combo_info_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andComboInfoIdGreaterThanOrEqualTo(String value) {
            addCriterion("combo_info_id >=", value, "comboInfoId");
            return (Criteria) this;
        }

        public Criteria andComboInfoIdGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("combo_info_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andComboInfoIdLessThan(String value) {
            addCriterion("combo_info_id <", value, "comboInfoId");
            return (Criteria) this;
        }

        public Criteria andComboInfoIdLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("combo_info_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andComboInfoIdLessThanOrEqualTo(String value) {
            addCriterion("combo_info_id <=", value, "comboInfoId");
            return (Criteria) this;
        }

        public Criteria andComboInfoIdLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("combo_info_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andComboInfoIdLike(String value) {
            addCriterion("combo_info_id like", value, "comboInfoId");
            return (Criteria) this;
        }

        public Criteria andComboInfoIdNotLike(String value) {
            addCriterion("combo_info_id not like", value, "comboInfoId");
            return (Criteria) this;
        }

        public Criteria andComboInfoIdIn(List<String> values) {
            addCriterion("combo_info_id in", values, "comboInfoId");
            return (Criteria) this;
        }

        public Criteria andComboInfoIdNotIn(List<String> values) {
            addCriterion("combo_info_id not in", values, "comboInfoId");
            return (Criteria) this;
        }

        public Criteria andComboInfoIdBetween(String value1, String value2) {
            addCriterion("combo_info_id between", value1, value2, "comboInfoId");
            return (Criteria) this;
        }

        public Criteria andComboInfoIdNotBetween(String value1, String value2) {
            addCriterion("combo_info_id not between", value1, value2, "comboInfoId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdIsNull() {
            addCriterion("store_order_handler_user_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdIsNotNull() {
            addCriterion("store_order_handler_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdEqualTo(String value) {
            addCriterion("store_order_handler_user_id =", value, "storeOrderHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdNotEqualTo(String value) {
            addCriterion("store_order_handler_user_id <>", value, "storeOrderHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdGreaterThan(String value) {
            addCriterion("store_order_handler_user_id >", value, "storeOrderHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("store_order_handler_user_id >=", value, "storeOrderHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdLessThan(String value) {
            addCriterion("store_order_handler_user_id <", value, "storeOrderHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdLessThanOrEqualTo(String value) {
            addCriterion("store_order_handler_user_id <=", value, "storeOrderHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdLike(String value) {
            addCriterion("store_order_handler_user_id like", value, "storeOrderHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdNotLike(String value) {
            addCriterion("store_order_handler_user_id not like", value, "storeOrderHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdIn(List<String> values) {
            addCriterion("store_order_handler_user_id in", values, "storeOrderHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdNotIn(List<String> values) {
            addCriterion("store_order_handler_user_id not in", values, "storeOrderHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdBetween(String value1, String value2) {
            addCriterion("store_order_handler_user_id between", value1, value2, "storeOrderHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdNotBetween(String value1, String value2) {
            addCriterion("store_order_handler_user_id not between", value1, value2, "storeOrderHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameIsNull() {
            addCriterion("store_order_handler_user_name is null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameIsNotNull() {
            addCriterion("store_order_handler_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameEqualTo(String value) {
            addCriterion("store_order_handler_user_name =", value, "storeOrderHandlerUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameNotEqualTo(String value) {
            addCriterion("store_order_handler_user_name <>", value, "storeOrderHandlerUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameGreaterThan(String value) {
            addCriterion("store_order_handler_user_name >", value, "storeOrderHandlerUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("store_order_handler_user_name >=", value, "storeOrderHandlerUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameLessThan(String value) {
            addCriterion("store_order_handler_user_name <", value, "storeOrderHandlerUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameLessThanOrEqualTo(String value) {
            addCriterion("store_order_handler_user_name <=", value, "storeOrderHandlerUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameLike(String value) {
            addCriterion("store_order_handler_user_name like", value, "storeOrderHandlerUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameNotLike(String value) {
            addCriterion("store_order_handler_user_name not like", value, "storeOrderHandlerUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameIn(List<String> values) {
            addCriterion("store_order_handler_user_name in", values, "storeOrderHandlerUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameNotIn(List<String> values) {
            addCriterion("store_order_handler_user_name not in", values, "storeOrderHandlerUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameBetween(String value1, String value2) {
            addCriterion("store_order_handler_user_name between", value1, value2, "storeOrderHandlerUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameNotBetween(String value1, String value2) {
            addCriterion("store_order_handler_user_name not between", value1, value2, "storeOrderHandlerUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneIsNull() {
            addCriterion("store_order_handler_user_phone is null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneIsNotNull() {
            addCriterion("store_order_handler_user_phone is not null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneEqualTo(String value) {
            addCriterion("store_order_handler_user_phone =", value, "storeOrderHandlerUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneNotEqualTo(String value) {
            addCriterion("store_order_handler_user_phone <>", value, "storeOrderHandlerUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneGreaterThan(String value) {
            addCriterion("store_order_handler_user_phone >", value, "storeOrderHandlerUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("store_order_handler_user_phone >=", value, "storeOrderHandlerUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneLessThan(String value) {
            addCriterion("store_order_handler_user_phone <", value, "storeOrderHandlerUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneLessThanOrEqualTo(String value) {
            addCriterion("store_order_handler_user_phone <=", value, "storeOrderHandlerUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneLike(String value) {
            addCriterion("store_order_handler_user_phone like", value, "storeOrderHandlerUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneNotLike(String value) {
            addCriterion("store_order_handler_user_phone not like", value, "storeOrderHandlerUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneIn(List<String> values) {
            addCriterion("store_order_handler_user_phone in", values, "storeOrderHandlerUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneNotIn(List<String> values) {
            addCriterion("store_order_handler_user_phone not in", values, "storeOrderHandlerUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneBetween(String value1, String value2) {
            addCriterion("store_order_handler_user_phone between", value1, value2, "storeOrderHandlerUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneNotBetween(String value1, String value2) {
            addCriterion("store_order_handler_user_phone not between", value1, value2, "storeOrderHandlerUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailIsNull() {
            addCriterion("store_order_handler_user_email is null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailIsNotNull() {
            addCriterion("store_order_handler_user_email is not null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailEqualTo(String value) {
            addCriterion("store_order_handler_user_email =", value, "storeOrderHandlerUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_email = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailNotEqualTo(String value) {
            addCriterion("store_order_handler_user_email <>", value, "storeOrderHandlerUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_email <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailGreaterThan(String value) {
            addCriterion("store_order_handler_user_email >", value, "storeOrderHandlerUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_email > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailGreaterThanOrEqualTo(String value) {
            addCriterion("store_order_handler_user_email >=", value, "storeOrderHandlerUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_email >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailLessThan(String value) {
            addCriterion("store_order_handler_user_email <", value, "storeOrderHandlerUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_email < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailLessThanOrEqualTo(String value) {
            addCriterion("store_order_handler_user_email <=", value, "storeOrderHandlerUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_user_email <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailLike(String value) {
            addCriterion("store_order_handler_user_email like", value, "storeOrderHandlerUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailNotLike(String value) {
            addCriterion("store_order_handler_user_email not like", value, "storeOrderHandlerUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailIn(List<String> values) {
            addCriterion("store_order_handler_user_email in", values, "storeOrderHandlerUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailNotIn(List<String> values) {
            addCriterion("store_order_handler_user_email not in", values, "storeOrderHandlerUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailBetween(String value1, String value2) {
            addCriterion("store_order_handler_user_email between", value1, value2, "storeOrderHandlerUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailNotBetween(String value1, String value2) {
            addCriterion("store_order_handler_user_email not between", value1, value2, "storeOrderHandlerUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdIsNull() {
            addCriterion("store_order_handler_sub_user_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdIsNotNull() {
            addCriterion("store_order_handler_sub_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_id =", value, "storeOrderHandlerSubUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdNotEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_id <>", value, "storeOrderHandlerSubUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdGreaterThan(String value) {
            addCriterion("store_order_handler_sub_user_id >", value, "storeOrderHandlerSubUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_id >=", value, "storeOrderHandlerSubUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdLessThan(String value) {
            addCriterion("store_order_handler_sub_user_id <", value, "storeOrderHandlerSubUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdLessThanOrEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_id <=", value, "storeOrderHandlerSubUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdLike(String value) {
            addCriterion("store_order_handler_sub_user_id like", value, "storeOrderHandlerSubUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdNotLike(String value) {
            addCriterion("store_order_handler_sub_user_id not like", value, "storeOrderHandlerSubUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdIn(List<String> values) {
            addCriterion("store_order_handler_sub_user_id in", values, "storeOrderHandlerSubUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdNotIn(List<String> values) {
            addCriterion("store_order_handler_sub_user_id not in", values, "storeOrderHandlerSubUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdBetween(String value1, String value2) {
            addCriterion("store_order_handler_sub_user_id between", value1, value2, "storeOrderHandlerSubUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdNotBetween(String value1, String value2) {
            addCriterion("store_order_handler_sub_user_id not between", value1, value2, "storeOrderHandlerSubUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameIsNull() {
            addCriterion("store_order_handler_sub_user_name is null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameIsNotNull() {
            addCriterion("store_order_handler_sub_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_name =", value, "storeOrderHandlerSubUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameNotEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_name <>", value, "storeOrderHandlerSubUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameGreaterThan(String value) {
            addCriterion("store_order_handler_sub_user_name >", value, "storeOrderHandlerSubUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_name >=", value, "storeOrderHandlerSubUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameLessThan(String value) {
            addCriterion("store_order_handler_sub_user_name <", value, "storeOrderHandlerSubUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameLessThanOrEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_name <=", value, "storeOrderHandlerSubUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameLike(String value) {
            addCriterion("store_order_handler_sub_user_name like", value, "storeOrderHandlerSubUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameNotLike(String value) {
            addCriterion("store_order_handler_sub_user_name not like", value, "storeOrderHandlerSubUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameIn(List<String> values) {
            addCriterion("store_order_handler_sub_user_name in", values, "storeOrderHandlerSubUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameNotIn(List<String> values) {
            addCriterion("store_order_handler_sub_user_name not in", values, "storeOrderHandlerSubUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameBetween(String value1, String value2) {
            addCriterion("store_order_handler_sub_user_name between", value1, value2, "storeOrderHandlerSubUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameNotBetween(String value1, String value2) {
            addCriterion("store_order_handler_sub_user_name not between", value1, value2, "storeOrderHandlerSubUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneIsNull() {
            addCriterion("store_order_handler_sub_user_phone is null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneIsNotNull() {
            addCriterion("store_order_handler_sub_user_phone is not null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_phone =", value, "storeOrderHandlerSubUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneNotEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_phone <>", value, "storeOrderHandlerSubUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneGreaterThan(String value) {
            addCriterion("store_order_handler_sub_user_phone >", value, "storeOrderHandlerSubUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_phone >=", value, "storeOrderHandlerSubUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneLessThan(String value) {
            addCriterion("store_order_handler_sub_user_phone <", value, "storeOrderHandlerSubUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneLessThanOrEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_phone <=", value, "storeOrderHandlerSubUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneLike(String value) {
            addCriterion("store_order_handler_sub_user_phone like", value, "storeOrderHandlerSubUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneNotLike(String value) {
            addCriterion("store_order_handler_sub_user_phone not like", value, "storeOrderHandlerSubUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneIn(List<String> values) {
            addCriterion("store_order_handler_sub_user_phone in", values, "storeOrderHandlerSubUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneNotIn(List<String> values) {
            addCriterion("store_order_handler_sub_user_phone not in", values, "storeOrderHandlerSubUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneBetween(String value1, String value2) {
            addCriterion("store_order_handler_sub_user_phone between", value1, value2, "storeOrderHandlerSubUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneNotBetween(String value1, String value2) {
            addCriterion("store_order_handler_sub_user_phone not between", value1, value2, "storeOrderHandlerSubUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailIsNull() {
            addCriterion("store_order_handler_sub_user_email is null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailIsNotNull() {
            addCriterion("store_order_handler_sub_user_email is not null");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_email =", value, "storeOrderHandlerSubUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_email = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailNotEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_email <>", value, "storeOrderHandlerSubUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_email <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailGreaterThan(String value) {
            addCriterion("store_order_handler_sub_user_email >", value, "storeOrderHandlerSubUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_email > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailGreaterThanOrEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_email >=", value, "storeOrderHandlerSubUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_email >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailLessThan(String value) {
            addCriterion("store_order_handler_sub_user_email <", value, "storeOrderHandlerSubUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_email < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailLessThanOrEqualTo(String value) {
            addCriterion("store_order_handler_sub_user_email <=", value, "storeOrderHandlerSubUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("store_order_handler_sub_user_email <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailLike(String value) {
            addCriterion("store_order_handler_sub_user_email like", value, "storeOrderHandlerSubUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailNotLike(String value) {
            addCriterion("store_order_handler_sub_user_email not like", value, "storeOrderHandlerSubUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailIn(List<String> values) {
            addCriterion("store_order_handler_sub_user_email in", values, "storeOrderHandlerSubUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailNotIn(List<String> values) {
            addCriterion("store_order_handler_sub_user_email not in", values, "storeOrderHandlerSubUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailBetween(String value1, String value2) {
            addCriterion("store_order_handler_sub_user_email between", value1, value2, "storeOrderHandlerSubUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailNotBetween(String value1, String value2) {
            addCriterion("store_order_handler_sub_user_email not between", value1, value2, "storeOrderHandlerSubUserEmail");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameIsNull() {
            addCriterion("pre_sale_manager_user_name is null");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameIsNotNull() {
            addCriterion("pre_sale_manager_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameEqualTo(String value) {
            addCriterion("pre_sale_manager_user_name =", value, "preSaleManagerUserName");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameNotEqualTo(String value) {
            addCriterion("pre_sale_manager_user_name <>", value, "preSaleManagerUserName");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameGreaterThan(String value) {
            addCriterion("pre_sale_manager_user_name >", value, "preSaleManagerUserName");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("pre_sale_manager_user_name >=", value, "preSaleManagerUserName");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameLessThan(String value) {
            addCriterion("pre_sale_manager_user_name <", value, "preSaleManagerUserName");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameLessThanOrEqualTo(String value) {
            addCriterion("pre_sale_manager_user_name <=", value, "preSaleManagerUserName");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameLike(String value) {
            addCriterion("pre_sale_manager_user_name like", value, "preSaleManagerUserName");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameNotLike(String value) {
            addCriterion("pre_sale_manager_user_name not like", value, "preSaleManagerUserName");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameIn(List<String> values) {
            addCriterion("pre_sale_manager_user_name in", values, "preSaleManagerUserName");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameNotIn(List<String> values) {
            addCriterion("pre_sale_manager_user_name not in", values, "preSaleManagerUserName");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameBetween(String value1, String value2) {
            addCriterion("pre_sale_manager_user_name between", value1, value2, "preSaleManagerUserName");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameNotBetween(String value1, String value2) {
            addCriterion("pre_sale_manager_user_name not between", value1, value2, "preSaleManagerUserName");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneIsNull() {
            addCriterion("pre_sale_manager_user_phone is null");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneIsNotNull() {
            addCriterion("pre_sale_manager_user_phone is not null");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneEqualTo(String value) {
            addCriterion("pre_sale_manager_user_phone =", value, "preSaleManagerUserPhone");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneNotEqualTo(String value) {
            addCriterion("pre_sale_manager_user_phone <>", value, "preSaleManagerUserPhone");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneGreaterThan(String value) {
            addCriterion("pre_sale_manager_user_phone >", value, "preSaleManagerUserPhone");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("pre_sale_manager_user_phone >=", value, "preSaleManagerUserPhone");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneLessThan(String value) {
            addCriterion("pre_sale_manager_user_phone <", value, "preSaleManagerUserPhone");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneLessThanOrEqualTo(String value) {
            addCriterion("pre_sale_manager_user_phone <=", value, "preSaleManagerUserPhone");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneLike(String value) {
            addCriterion("pre_sale_manager_user_phone like", value, "preSaleManagerUserPhone");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneNotLike(String value) {
            addCriterion("pre_sale_manager_user_phone not like", value, "preSaleManagerUserPhone");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneIn(List<String> values) {
            addCriterion("pre_sale_manager_user_phone in", values, "preSaleManagerUserPhone");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneNotIn(List<String> values) {
            addCriterion("pre_sale_manager_user_phone not in", values, "preSaleManagerUserPhone");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneBetween(String value1, String value2) {
            addCriterion("pre_sale_manager_user_phone between", value1, value2, "preSaleManagerUserPhone");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneNotBetween(String value1, String value2) {
            addCriterion("pre_sale_manager_user_phone not between", value1, value2, "preSaleManagerUserPhone");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailIsNull() {
            addCriterion("pre_sale_manager_user_email is null");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailIsNotNull() {
            addCriterion("pre_sale_manager_user_email is not null");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailEqualTo(String value) {
            addCriterion("pre_sale_manager_user_email =", value, "preSaleManagerUserEmail");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_email = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailNotEqualTo(String value) {
            addCriterion("pre_sale_manager_user_email <>", value, "preSaleManagerUserEmail");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_email <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailGreaterThan(String value) {
            addCriterion("pre_sale_manager_user_email >", value, "preSaleManagerUserEmail");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_email > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailGreaterThanOrEqualTo(String value) {
            addCriterion("pre_sale_manager_user_email >=", value, "preSaleManagerUserEmail");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_email >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailLessThan(String value) {
            addCriterion("pre_sale_manager_user_email <", value, "preSaleManagerUserEmail");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_email < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailLessThanOrEqualTo(String value) {
            addCriterion("pre_sale_manager_user_email <=", value, "preSaleManagerUserEmail");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("pre_sale_manager_user_email <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailLike(String value) {
            addCriterion("pre_sale_manager_user_email like", value, "preSaleManagerUserEmail");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailNotLike(String value) {
            addCriterion("pre_sale_manager_user_email not like", value, "preSaleManagerUserEmail");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailIn(List<String> values) {
            addCriterion("pre_sale_manager_user_email in", values, "preSaleManagerUserEmail");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailNotIn(List<String> values) {
            addCriterion("pre_sale_manager_user_email not in", values, "preSaleManagerUserEmail");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailBetween(String value1, String value2) {
            addCriterion("pre_sale_manager_user_email between", value1, value2, "preSaleManagerUserEmail");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailNotBetween(String value1, String value2) {
            addCriterion("pre_sale_manager_user_email not between", value1, value2, "preSaleManagerUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameIsNull() {
            addCriterion("product_ship_user_name is null");
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameIsNotNull() {
            addCriterion("product_ship_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameEqualTo(String value) {
            addCriterion("product_ship_user_name =", value, "productShipUserName");
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameNotEqualTo(String value) {
            addCriterion("product_ship_user_name <>", value, "productShipUserName");
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameGreaterThan(String value) {
            addCriterion("product_ship_user_name >", value, "productShipUserName");
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_ship_user_name >=", value, "productShipUserName");
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameLessThan(String value) {
            addCriterion("product_ship_user_name <", value, "productShipUserName");
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameLessThanOrEqualTo(String value) {
            addCriterion("product_ship_user_name <=", value, "productShipUserName");
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameLike(String value) {
            addCriterion("product_ship_user_name like", value, "productShipUserName");
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameNotLike(String value) {
            addCriterion("product_ship_user_name not like", value, "productShipUserName");
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameIn(List<String> values) {
            addCriterion("product_ship_user_name in", values, "productShipUserName");
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameNotIn(List<String> values) {
            addCriterion("product_ship_user_name not in", values, "productShipUserName");
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameBetween(String value1, String value2) {
            addCriterion("product_ship_user_name between", value1, value2, "productShipUserName");
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameNotBetween(String value1, String value2) {
            addCriterion("product_ship_user_name not between", value1, value2, "productShipUserName");
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneIsNull() {
            addCriterion("product_ship_user_phone is null");
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneIsNotNull() {
            addCriterion("product_ship_user_phone is not null");
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneEqualTo(String value) {
            addCriterion("product_ship_user_phone =", value, "productShipUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneNotEqualTo(String value) {
            addCriterion("product_ship_user_phone <>", value, "productShipUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneGreaterThan(String value) {
            addCriterion("product_ship_user_phone >", value, "productShipUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("product_ship_user_phone >=", value, "productShipUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneLessThan(String value) {
            addCriterion("product_ship_user_phone <", value, "productShipUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneLessThanOrEqualTo(String value) {
            addCriterion("product_ship_user_phone <=", value, "productShipUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneLike(String value) {
            addCriterion("product_ship_user_phone like", value, "productShipUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneNotLike(String value) {
            addCriterion("product_ship_user_phone not like", value, "productShipUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneIn(List<String> values) {
            addCriterion("product_ship_user_phone in", values, "productShipUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneNotIn(List<String> values) {
            addCriterion("product_ship_user_phone not in", values, "productShipUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneBetween(String value1, String value2) {
            addCriterion("product_ship_user_phone between", value1, value2, "productShipUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneNotBetween(String value1, String value2) {
            addCriterion("product_ship_user_phone not between", value1, value2, "productShipUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailIsNull() {
            addCriterion("product_ship_user_email is null");
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailIsNotNull() {
            addCriterion("product_ship_user_email is not null");
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailEqualTo(String value) {
            addCriterion("product_ship_user_email =", value, "productShipUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_email = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailNotEqualTo(String value) {
            addCriterion("product_ship_user_email <>", value, "productShipUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_email <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailGreaterThan(String value) {
            addCriterion("product_ship_user_email >", value, "productShipUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_email > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailGreaterThanOrEqualTo(String value) {
            addCriterion("product_ship_user_email >=", value, "productShipUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_email >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailLessThan(String value) {
            addCriterion("product_ship_user_email <", value, "productShipUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_email < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailLessThanOrEqualTo(String value) {
            addCriterion("product_ship_user_email <=", value, "productShipUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_user_email <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailLike(String value) {
            addCriterion("product_ship_user_email like", value, "productShipUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailNotLike(String value) {
            addCriterion("product_ship_user_email not like", value, "productShipUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailIn(List<String> values) {
            addCriterion("product_ship_user_email in", values, "productShipUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailNotIn(List<String> values) {
            addCriterion("product_ship_user_email not in", values, "productShipUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailBetween(String value1, String value2) {
            addCriterion("product_ship_user_email between", value1, value2, "productShipUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailNotBetween(String value1, String value2) {
            addCriterion("product_ship_user_email not between", value1, value2, "productShipUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameIsNull() {
            addCriterion("product_install_user_name is null");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameIsNotNull() {
            addCriterion("product_install_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameEqualTo(String value) {
            addCriterion("product_install_user_name =", value, "productInstallUserName");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameNotEqualTo(String value) {
            addCriterion("product_install_user_name <>", value, "productInstallUserName");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameGreaterThan(String value) {
            addCriterion("product_install_user_name >", value, "productInstallUserName");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_install_user_name >=", value, "productInstallUserName");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameLessThan(String value) {
            addCriterion("product_install_user_name <", value, "productInstallUserName");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameLessThanOrEqualTo(String value) {
            addCriterion("product_install_user_name <=", value, "productInstallUserName");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameLike(String value) {
            addCriterion("product_install_user_name like", value, "productInstallUserName");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameNotLike(String value) {
            addCriterion("product_install_user_name not like", value, "productInstallUserName");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameIn(List<String> values) {
            addCriterion("product_install_user_name in", values, "productInstallUserName");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameNotIn(List<String> values) {
            addCriterion("product_install_user_name not in", values, "productInstallUserName");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameBetween(String value1, String value2) {
            addCriterion("product_install_user_name between", value1, value2, "productInstallUserName");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameNotBetween(String value1, String value2) {
            addCriterion("product_install_user_name not between", value1, value2, "productInstallUserName");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneIsNull() {
            addCriterion("product_install_user_phone is null");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneIsNotNull() {
            addCriterion("product_install_user_phone is not null");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneEqualTo(String value) {
            addCriterion("product_install_user_phone =", value, "productInstallUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneNotEqualTo(String value) {
            addCriterion("product_install_user_phone <>", value, "productInstallUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneGreaterThan(String value) {
            addCriterion("product_install_user_phone >", value, "productInstallUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("product_install_user_phone >=", value, "productInstallUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneLessThan(String value) {
            addCriterion("product_install_user_phone <", value, "productInstallUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneLessThanOrEqualTo(String value) {
            addCriterion("product_install_user_phone <=", value, "productInstallUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneLike(String value) {
            addCriterion("product_install_user_phone like", value, "productInstallUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneNotLike(String value) {
            addCriterion("product_install_user_phone not like", value, "productInstallUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneIn(List<String> values) {
            addCriterion("product_install_user_phone in", values, "productInstallUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneNotIn(List<String> values) {
            addCriterion("product_install_user_phone not in", values, "productInstallUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneBetween(String value1, String value2) {
            addCriterion("product_install_user_phone between", value1, value2, "productInstallUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneNotBetween(String value1, String value2) {
            addCriterion("product_install_user_phone not between", value1, value2, "productInstallUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailIsNull() {
            addCriterion("product_install_user_email is null");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailIsNotNull() {
            addCriterion("product_install_user_email is not null");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailEqualTo(String value) {
            addCriterion("product_install_user_email =", value, "productInstallUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_email = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailNotEqualTo(String value) {
            addCriterion("product_install_user_email <>", value, "productInstallUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_email <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailGreaterThan(String value) {
            addCriterion("product_install_user_email >", value, "productInstallUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_email > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailGreaterThanOrEqualTo(String value) {
            addCriterion("product_install_user_email >=", value, "productInstallUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_email >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailLessThan(String value) {
            addCriterion("product_install_user_email <", value, "productInstallUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_email < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailLessThanOrEqualTo(String value) {
            addCriterion("product_install_user_email <=", value, "productInstallUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_install_user_email <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailLike(String value) {
            addCriterion("product_install_user_email like", value, "productInstallUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailNotLike(String value) {
            addCriterion("product_install_user_email not like", value, "productInstallUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailIn(List<String> values) {
            addCriterion("product_install_user_email in", values, "productInstallUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailNotIn(List<String> values) {
            addCriterion("product_install_user_email not in", values, "productInstallUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailBetween(String value1, String value2) {
            addCriterion("product_install_user_email between", value1, value2, "productInstallUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailNotBetween(String value1, String value2) {
            addCriterion("product_install_user_email not between", value1, value2, "productInstallUserEmail");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameIsNull() {
            addCriterion("things_card_user_name is null");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameIsNotNull() {
            addCriterion("things_card_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameEqualTo(String value) {
            addCriterion("things_card_user_name =", value, "thingsCardUserName");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameNotEqualTo(String value) {
            addCriterion("things_card_user_name <>", value, "thingsCardUserName");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameGreaterThan(String value) {
            addCriterion("things_card_user_name >", value, "thingsCardUserName");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("things_card_user_name >=", value, "thingsCardUserName");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameLessThan(String value) {
            addCriterion("things_card_user_name <", value, "thingsCardUserName");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameLessThanOrEqualTo(String value) {
            addCriterion("things_card_user_name <=", value, "thingsCardUserName");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameLike(String value) {
            addCriterion("things_card_user_name like", value, "thingsCardUserName");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameNotLike(String value) {
            addCriterion("things_card_user_name not like", value, "thingsCardUserName");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameIn(List<String> values) {
            addCriterion("things_card_user_name in", values, "thingsCardUserName");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameNotIn(List<String> values) {
            addCriterion("things_card_user_name not in", values, "thingsCardUserName");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameBetween(String value1, String value2) {
            addCriterion("things_card_user_name between", value1, value2, "thingsCardUserName");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameNotBetween(String value1, String value2) {
            addCriterion("things_card_user_name not between", value1, value2, "thingsCardUserName");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneIsNull() {
            addCriterion("things_card_user_phone is null");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneIsNotNull() {
            addCriterion("things_card_user_phone is not null");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneEqualTo(String value) {
            addCriterion("things_card_user_phone =", value, "thingsCardUserPhone");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneNotEqualTo(String value) {
            addCriterion("things_card_user_phone <>", value, "thingsCardUserPhone");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneGreaterThan(String value) {
            addCriterion("things_card_user_phone >", value, "thingsCardUserPhone");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("things_card_user_phone >=", value, "thingsCardUserPhone");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneLessThan(String value) {
            addCriterion("things_card_user_phone <", value, "thingsCardUserPhone");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneLessThanOrEqualTo(String value) {
            addCriterion("things_card_user_phone <=", value, "thingsCardUserPhone");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneLike(String value) {
            addCriterion("things_card_user_phone like", value, "thingsCardUserPhone");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneNotLike(String value) {
            addCriterion("things_card_user_phone not like", value, "thingsCardUserPhone");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneIn(List<String> values) {
            addCriterion("things_card_user_phone in", values, "thingsCardUserPhone");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneNotIn(List<String> values) {
            addCriterion("things_card_user_phone not in", values, "thingsCardUserPhone");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneBetween(String value1, String value2) {
            addCriterion("things_card_user_phone between", value1, value2, "thingsCardUserPhone");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneNotBetween(String value1, String value2) {
            addCriterion("things_card_user_phone not between", value1, value2, "thingsCardUserPhone");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailIsNull() {
            addCriterion("things_card_user_email is null");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailIsNotNull() {
            addCriterion("things_card_user_email is not null");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailEqualTo(String value) {
            addCriterion("things_card_user_email =", value, "thingsCardUserEmail");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_email = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailNotEqualTo(String value) {
            addCriterion("things_card_user_email <>", value, "thingsCardUserEmail");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_email <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailGreaterThan(String value) {
            addCriterion("things_card_user_email >", value, "thingsCardUserEmail");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_email > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailGreaterThanOrEqualTo(String value) {
            addCriterion("things_card_user_email >=", value, "thingsCardUserEmail");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_email >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailLessThan(String value) {
            addCriterion("things_card_user_email <", value, "thingsCardUserEmail");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_email < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailLessThanOrEqualTo(String value) {
            addCriterion("things_card_user_email <=", value, "thingsCardUserEmail");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("things_card_user_email <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailLike(String value) {
            addCriterion("things_card_user_email like", value, "thingsCardUserEmail");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailNotLike(String value) {
            addCriterion("things_card_user_email not like", value, "thingsCardUserEmail");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailIn(List<String> values) {
            addCriterion("things_card_user_email in", values, "thingsCardUserEmail");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailNotIn(List<String> values) {
            addCriterion("things_card_user_email not in", values, "thingsCardUserEmail");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailBetween(String value1, String value2) {
            addCriterion("things_card_user_email between", value1, value2, "thingsCardUserEmail");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailNotBetween(String value1, String value2) {
            addCriterion("things_card_user_email not between", value1, value2, "thingsCardUserEmail");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameIsNull() {
            addCriterion("software_authority_user_name is null");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameIsNotNull() {
            addCriterion("software_authority_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameEqualTo(String value) {
            addCriterion("software_authority_user_name =", value, "softwareAuthorityUserName");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameNotEqualTo(String value) {
            addCriterion("software_authority_user_name <>", value, "softwareAuthorityUserName");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameGreaterThan(String value) {
            addCriterion("software_authority_user_name >", value, "softwareAuthorityUserName");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("software_authority_user_name >=", value, "softwareAuthorityUserName");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameLessThan(String value) {
            addCriterion("software_authority_user_name <", value, "softwareAuthorityUserName");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameLessThanOrEqualTo(String value) {
            addCriterion("software_authority_user_name <=", value, "softwareAuthorityUserName");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameLike(String value) {
            addCriterion("software_authority_user_name like", value, "softwareAuthorityUserName");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameNotLike(String value) {
            addCriterion("software_authority_user_name not like", value, "softwareAuthorityUserName");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameIn(List<String> values) {
            addCriterion("software_authority_user_name in", values, "softwareAuthorityUserName");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameNotIn(List<String> values) {
            addCriterion("software_authority_user_name not in", values, "softwareAuthorityUserName");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameBetween(String value1, String value2) {
            addCriterion("software_authority_user_name between", value1, value2, "softwareAuthorityUserName");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameNotBetween(String value1, String value2) {
            addCriterion("software_authority_user_name not between", value1, value2, "softwareAuthorityUserName");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneIsNull() {
            addCriterion("software_authority_user_phone is null");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneIsNotNull() {
            addCriterion("software_authority_user_phone is not null");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneEqualTo(String value) {
            addCriterion("software_authority_user_phone =", value, "softwareAuthorityUserPhone");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneNotEqualTo(String value) {
            addCriterion("software_authority_user_phone <>", value, "softwareAuthorityUserPhone");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneGreaterThan(String value) {
            addCriterion("software_authority_user_phone >", value, "softwareAuthorityUserPhone");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("software_authority_user_phone >=", value, "softwareAuthorityUserPhone");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneLessThan(String value) {
            addCriterion("software_authority_user_phone <", value, "softwareAuthorityUserPhone");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneLessThanOrEqualTo(String value) {
            addCriterion("software_authority_user_phone <=", value, "softwareAuthorityUserPhone");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneLike(String value) {
            addCriterion("software_authority_user_phone like", value, "softwareAuthorityUserPhone");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneNotLike(String value) {
            addCriterion("software_authority_user_phone not like", value, "softwareAuthorityUserPhone");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneIn(List<String> values) {
            addCriterion("software_authority_user_phone in", values, "softwareAuthorityUserPhone");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneNotIn(List<String> values) {
            addCriterion("software_authority_user_phone not in", values, "softwareAuthorityUserPhone");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneBetween(String value1, String value2) {
            addCriterion("software_authority_user_phone between", value1, value2, "softwareAuthorityUserPhone");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneNotBetween(String value1, String value2) {
            addCriterion("software_authority_user_phone not between", value1, value2, "softwareAuthorityUserPhone");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailIsNull() {
            addCriterion("software_authority_user_email is null");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailIsNotNull() {
            addCriterion("software_authority_user_email is not null");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailEqualTo(String value) {
            addCriterion("software_authority_user_email =", value, "softwareAuthorityUserEmail");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_email = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailNotEqualTo(String value) {
            addCriterion("software_authority_user_email <>", value, "softwareAuthorityUserEmail");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_email <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailGreaterThan(String value) {
            addCriterion("software_authority_user_email >", value, "softwareAuthorityUserEmail");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_email > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailGreaterThanOrEqualTo(String value) {
            addCriterion("software_authority_user_email >=", value, "softwareAuthorityUserEmail");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_email >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailLessThan(String value) {
            addCriterion("software_authority_user_email <", value, "softwareAuthorityUserEmail");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_email < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailLessThanOrEqualTo(String value) {
            addCriterion("software_authority_user_email <=", value, "softwareAuthorityUserEmail");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("software_authority_user_email <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailLike(String value) {
            addCriterion("software_authority_user_email like", value, "softwareAuthorityUserEmail");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailNotLike(String value) {
            addCriterion("software_authority_user_email not like", value, "softwareAuthorityUserEmail");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailIn(List<String> values) {
            addCriterion("software_authority_user_email in", values, "softwareAuthorityUserEmail");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailNotIn(List<String> values) {
            addCriterion("software_authority_user_email not in", values, "softwareAuthorityUserEmail");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailBetween(String value1, String value2) {
            addCriterion("software_authority_user_email between", value1, value2, "softwareAuthorityUserEmail");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailNotBetween(String value1, String value2) {
            addCriterion("software_authority_user_email not between", value1, value2, "softwareAuthorityUserEmail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameIsNull() {
            addCriterion("after_sale_user_name is null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameIsNotNull() {
            addCriterion("after_sale_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameEqualTo(String value) {
            addCriterion("after_sale_user_name =", value, "afterSaleUserName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameNotEqualTo(String value) {
            addCriterion("after_sale_user_name <>", value, "afterSaleUserName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameGreaterThan(String value) {
            addCriterion("after_sale_user_name >", value, "afterSaleUserName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("after_sale_user_name >=", value, "afterSaleUserName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameLessThan(String value) {
            addCriterion("after_sale_user_name <", value, "afterSaleUserName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameLessThanOrEqualTo(String value) {
            addCriterion("after_sale_user_name <=", value, "afterSaleUserName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameLike(String value) {
            addCriterion("after_sale_user_name like", value, "afterSaleUserName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameNotLike(String value) {
            addCriterion("after_sale_user_name not like", value, "afterSaleUserName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameIn(List<String> values) {
            addCriterion("after_sale_user_name in", values, "afterSaleUserName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameNotIn(List<String> values) {
            addCriterion("after_sale_user_name not in", values, "afterSaleUserName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameBetween(String value1, String value2) {
            addCriterion("after_sale_user_name between", value1, value2, "afterSaleUserName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameNotBetween(String value1, String value2) {
            addCriterion("after_sale_user_name not between", value1, value2, "afterSaleUserName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneIsNull() {
            addCriterion("after_sale_user_phone is null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneIsNotNull() {
            addCriterion("after_sale_user_phone is not null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneEqualTo(String value) {
            addCriterion("after_sale_user_phone =", value, "afterSaleUserPhone");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneNotEqualTo(String value) {
            addCriterion("after_sale_user_phone <>", value, "afterSaleUserPhone");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneGreaterThan(String value) {
            addCriterion("after_sale_user_phone >", value, "afterSaleUserPhone");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("after_sale_user_phone >=", value, "afterSaleUserPhone");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneLessThan(String value) {
            addCriterion("after_sale_user_phone <", value, "afterSaleUserPhone");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneLessThanOrEqualTo(String value) {
            addCriterion("after_sale_user_phone <=", value, "afterSaleUserPhone");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneLike(String value) {
            addCriterion("after_sale_user_phone like", value, "afterSaleUserPhone");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneNotLike(String value) {
            addCriterion("after_sale_user_phone not like", value, "afterSaleUserPhone");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneIn(List<String> values) {
            addCriterion("after_sale_user_phone in", values, "afterSaleUserPhone");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneNotIn(List<String> values) {
            addCriterion("after_sale_user_phone not in", values, "afterSaleUserPhone");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneBetween(String value1, String value2) {
            addCriterion("after_sale_user_phone between", value1, value2, "afterSaleUserPhone");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneNotBetween(String value1, String value2) {
            addCriterion("after_sale_user_phone not between", value1, value2, "afterSaleUserPhone");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailIsNull() {
            addCriterion("after_sale_user_email is null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailIsNotNull() {
            addCriterion("after_sale_user_email is not null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailEqualTo(String value) {
            addCriterion("after_sale_user_email =", value, "afterSaleUserEmail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_email = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailNotEqualTo(String value) {
            addCriterion("after_sale_user_email <>", value, "afterSaleUserEmail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_email <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailGreaterThan(String value) {
            addCriterion("after_sale_user_email >", value, "afterSaleUserEmail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_email > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailGreaterThanOrEqualTo(String value) {
            addCriterion("after_sale_user_email >=", value, "afterSaleUserEmail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_email >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailLessThan(String value) {
            addCriterion("after_sale_user_email <", value, "afterSaleUserEmail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_email < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailLessThanOrEqualTo(String value) {
            addCriterion("after_sale_user_email <=", value, "afterSaleUserEmail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_user_email <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailLike(String value) {
            addCriterion("after_sale_user_email like", value, "afterSaleUserEmail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailNotLike(String value) {
            addCriterion("after_sale_user_email not like", value, "afterSaleUserEmail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailIn(List<String> values) {
            addCriterion("after_sale_user_email in", values, "afterSaleUserEmail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailNotIn(List<String> values) {
            addCriterion("after_sale_user_email not in", values, "afterSaleUserEmail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailBetween(String value1, String value2) {
            addCriterion("after_sale_user_email between", value1, value2, "afterSaleUserEmail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailNotBetween(String value1, String value2) {
            addCriterion("after_sale_user_email not between", value1, value2, "afterSaleUserEmail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailIsNull() {
            addCriterion("after_sale_detail is null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailIsNotNull() {
            addCriterion("after_sale_detail is not null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailEqualTo(String value) {
            addCriterion("after_sale_detail =", value, "afterSaleDetail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_detail = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailNotEqualTo(String value) {
            addCriterion("after_sale_detail <>", value, "afterSaleDetail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_detail <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailGreaterThan(String value) {
            addCriterion("after_sale_detail >", value, "afterSaleDetail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_detail > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailGreaterThanOrEqualTo(String value) {
            addCriterion("after_sale_detail >=", value, "afterSaleDetail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_detail >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailLessThan(String value) {
            addCriterion("after_sale_detail <", value, "afterSaleDetail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_detail < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailLessThanOrEqualTo(String value) {
            addCriterion("after_sale_detail <=", value, "afterSaleDetail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("after_sale_detail <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailLike(String value) {
            addCriterion("after_sale_detail like", value, "afterSaleDetail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailNotLike(String value) {
            addCriterion("after_sale_detail not like", value, "afterSaleDetail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailIn(List<String> values) {
            addCriterion("after_sale_detail in", values, "afterSaleDetail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailNotIn(List<String> values) {
            addCriterion("after_sale_detail not in", values, "afterSaleDetail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailBetween(String value1, String value2) {
            addCriterion("after_sale_detail between", value1, value2, "afterSaleDetail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailNotBetween(String value1, String value2) {
            addCriterion("after_sale_detail not between", value1, value2, "afterSaleDetail");
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoIsNull() {
            addCriterion("product_maintain_info is null");
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoIsNotNull() {
            addCriterion("product_maintain_info is not null");
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoEqualTo(String value) {
            addCriterion("product_maintain_info =", value, "productMaintainInfo");
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_maintain_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoNotEqualTo(String value) {
            addCriterion("product_maintain_info <>", value, "productMaintainInfo");
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_maintain_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoGreaterThan(String value) {
            addCriterion("product_maintain_info >", value, "productMaintainInfo");
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_maintain_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoGreaterThanOrEqualTo(String value) {
            addCriterion("product_maintain_info >=", value, "productMaintainInfo");
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_maintain_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoLessThan(String value) {
            addCriterion("product_maintain_info <", value, "productMaintainInfo");
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_maintain_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoLessThanOrEqualTo(String value) {
            addCriterion("product_maintain_info <=", value, "productMaintainInfo");
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_maintain_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoLike(String value) {
            addCriterion("product_maintain_info like", value, "productMaintainInfo");
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoNotLike(String value) {
            addCriterion("product_maintain_info not like", value, "productMaintainInfo");
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoIn(List<String> values) {
            addCriterion("product_maintain_info in", values, "productMaintainInfo");
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoNotIn(List<String> values) {
            addCriterion("product_maintain_info not in", values, "productMaintainInfo");
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoBetween(String value1, String value2) {
            addCriterion("product_maintain_info between", value1, value2, "productMaintainInfo");
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoNotBetween(String value1, String value2) {
            addCriterion("product_maintain_info not between", value1, value2, "productMaintainInfo");
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoIsNull() {
            addCriterion("product_sale_return_info is null");
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoIsNotNull() {
            addCriterion("product_sale_return_info is not null");
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoEqualTo(String value) {
            addCriterion("product_sale_return_info =", value, "productSaleReturnInfo");
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_sale_return_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoNotEqualTo(String value) {
            addCriterion("product_sale_return_info <>", value, "productSaleReturnInfo");
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_sale_return_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoGreaterThan(String value) {
            addCriterion("product_sale_return_info >", value, "productSaleReturnInfo");
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_sale_return_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoGreaterThanOrEqualTo(String value) {
            addCriterion("product_sale_return_info >=", value, "productSaleReturnInfo");
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_sale_return_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoLessThan(String value) {
            addCriterion("product_sale_return_info <", value, "productSaleReturnInfo");
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_sale_return_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoLessThanOrEqualTo(String value) {
            addCriterion("product_sale_return_info <=", value, "productSaleReturnInfo");
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("product_sale_return_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoLike(String value) {
            addCriterion("product_sale_return_info like", value, "productSaleReturnInfo");
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoNotLike(String value) {
            addCriterion("product_sale_return_info not like", value, "productSaleReturnInfo");
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoIn(List<String> values) {
            addCriterion("product_sale_return_info in", values, "productSaleReturnInfo");
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoNotIn(List<String> values) {
            addCriterion("product_sale_return_info not in", values, "productSaleReturnInfo");
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoBetween(String value1, String value2) {
            addCriterion("product_sale_return_info between", value1, value2, "productSaleReturnInfo");
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoNotBetween(String value1, String value2) {
            addCriterion("product_sale_return_info not between", value1, value2, "productSaleReturnInfo");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(NewProductRequestOnlineOfflineAfterSaleInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdLikeInsensitive(String value) {
            addCriterion("upper(new_product_request_id) like", value.toUpperCase(), "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andComboInfoIdLikeInsensitive(String value) {
            addCriterion("upper(combo_info_id) like", value.toUpperCase(), "comboInfoId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserIdLikeInsensitive(String value) {
            addCriterion("upper(store_order_handler_user_id) like", value.toUpperCase(), "storeOrderHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserNameLikeInsensitive(String value) {
            addCriterion("upper(store_order_handler_user_name) like", value.toUpperCase(), "storeOrderHandlerUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserPhoneLikeInsensitive(String value) {
            addCriterion("upper(store_order_handler_user_phone) like", value.toUpperCase(), "storeOrderHandlerUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerUserEmailLikeInsensitive(String value) {
            addCriterion("upper(store_order_handler_user_email) like", value.toUpperCase(), "storeOrderHandlerUserEmail");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserIdLikeInsensitive(String value) {
            addCriterion("upper(store_order_handler_sub_user_id) like", value.toUpperCase(), "storeOrderHandlerSubUserId");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserNameLikeInsensitive(String value) {
            addCriterion("upper(store_order_handler_sub_user_name) like", value.toUpperCase(), "storeOrderHandlerSubUserName");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserPhoneLikeInsensitive(String value) {
            addCriterion("upper(store_order_handler_sub_user_phone) like", value.toUpperCase(), "storeOrderHandlerSubUserPhone");
            return (Criteria) this;
        }

        public Criteria andStoreOrderHandlerSubUserEmailLikeInsensitive(String value) {
            addCriterion("upper(store_order_handler_sub_user_email) like", value.toUpperCase(), "storeOrderHandlerSubUserEmail");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserNameLikeInsensitive(String value) {
            addCriterion("upper(pre_sale_manager_user_name) like", value.toUpperCase(), "preSaleManagerUserName");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserPhoneLikeInsensitive(String value) {
            addCriterion("upper(pre_sale_manager_user_phone) like", value.toUpperCase(), "preSaleManagerUserPhone");
            return (Criteria) this;
        }

        public Criteria andPreSaleManagerUserEmailLikeInsensitive(String value) {
            addCriterion("upper(pre_sale_manager_user_email) like", value.toUpperCase(), "preSaleManagerUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductShipUserNameLikeInsensitive(String value) {
            addCriterion("upper(product_ship_user_name) like", value.toUpperCase(), "productShipUserName");
            return (Criteria) this;
        }

        public Criteria andProductShipUserPhoneLikeInsensitive(String value) {
            addCriterion("upper(product_ship_user_phone) like", value.toUpperCase(), "productShipUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductShipUserEmailLikeInsensitive(String value) {
            addCriterion("upper(product_ship_user_email) like", value.toUpperCase(), "productShipUserEmail");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserNameLikeInsensitive(String value) {
            addCriterion("upper(product_install_user_name) like", value.toUpperCase(), "productInstallUserName");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserPhoneLikeInsensitive(String value) {
            addCriterion("upper(product_install_user_phone) like", value.toUpperCase(), "productInstallUserPhone");
            return (Criteria) this;
        }

        public Criteria andProductInstallUserEmailLikeInsensitive(String value) {
            addCriterion("upper(product_install_user_email) like", value.toUpperCase(), "productInstallUserEmail");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserNameLikeInsensitive(String value) {
            addCriterion("upper(things_card_user_name) like", value.toUpperCase(), "thingsCardUserName");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserPhoneLikeInsensitive(String value) {
            addCriterion("upper(things_card_user_phone) like", value.toUpperCase(), "thingsCardUserPhone");
            return (Criteria) this;
        }

        public Criteria andThingsCardUserEmailLikeInsensitive(String value) {
            addCriterion("upper(things_card_user_email) like", value.toUpperCase(), "thingsCardUserEmail");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserNameLikeInsensitive(String value) {
            addCriterion("upper(software_authority_user_name) like", value.toUpperCase(), "softwareAuthorityUserName");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserPhoneLikeInsensitive(String value) {
            addCriterion("upper(software_authority_user_phone) like", value.toUpperCase(), "softwareAuthorityUserPhone");
            return (Criteria) this;
        }

        public Criteria andSoftwareAuthorityUserEmailLikeInsensitive(String value) {
            addCriterion("upper(software_authority_user_email) like", value.toUpperCase(), "softwareAuthorityUserEmail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserNameLikeInsensitive(String value) {
            addCriterion("upper(after_sale_user_name) like", value.toUpperCase(), "afterSaleUserName");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserPhoneLikeInsensitive(String value) {
            addCriterion("upper(after_sale_user_phone) like", value.toUpperCase(), "afterSaleUserPhone");
            return (Criteria) this;
        }

        public Criteria andAfterSaleUserEmailLikeInsensitive(String value) {
            addCriterion("upper(after_sale_user_email) like", value.toUpperCase(), "afterSaleUserEmail");
            return (Criteria) this;
        }

        public Criteria andAfterSaleDetailLikeInsensitive(String value) {
            addCriterion("upper(after_sale_detail) like", value.toUpperCase(), "afterSaleDetail");
            return (Criteria) this;
        }

        public Criteria andProductMaintainInfoLikeInsensitive(String value) {
            addCriterion("upper(product_maintain_info) like", value.toUpperCase(), "productMaintainInfo");
            return (Criteria) this;
        }

        public Criteria andProductSaleReturnInfoLikeInsensitive(String value) {
            addCriterion("upper(product_sale_return_info) like", value.toUpperCase(), "productSaleReturnInfo");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        private NewProductRequestOnlineOfflineAfterSaleInfoExample example;

        protected Criteria(NewProductRequestOnlineOfflineAfterSaleInfoExample example) {
            super();
            this.example = example;
        }

        public NewProductRequestOnlineOfflineAfterSaleInfoExample example() {
            return this.example;
        }

        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            Criteria add(Criteria add);
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        void example(com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineAfterSaleInfoExample example);
    }
}