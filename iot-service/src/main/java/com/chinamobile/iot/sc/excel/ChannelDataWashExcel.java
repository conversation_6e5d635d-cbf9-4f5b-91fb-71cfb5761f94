package com.chinamobile.iot.sc.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2025/4/30 14:31
 * @description:
 **/
@Data
public class ChannelDataWashExcel {

    @ExcelProperty(value = "订单编号",index = 0)
    private String orderId;

    @ExcelProperty(value = "渠道商全称",index = 1)
    private String agentNameWash;


    @ExcelProperty(value = "渠道商编码",index = 2)
    private String agentNumberWash;

    @ExcelProperty(value = "渠道商类别",index = 3)
    private String agentCategoryWash;

    @ExcelProperty(value = "渠道商标签",index = 4)
    private String agentLabelWash;


}
