package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * spu销售标签历史表
 *
 * <AUTHOR>
public class SpuSaleLabelHistory implements Serializable {
    /**
     *
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    private String id;

    /**
     * spu编码
     *
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    private String spuCode;

    /**
     * 标签
     *
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    private String label;

    /**
     * 0-主标签,1-副标签
     *
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    private String type;

    /**
     *
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    private Date updateTime;

    /**
     *
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    private String spuOfferingVersion;

    /**
     *
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    private String spuId;

    /**
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..spu_sale_label_history.id
     *
     * @return the value of supply_chain..spu_sale_label_history.id
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public SpuSaleLabelHistory withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_sale_label_history.id
     *
     * @param id the value for supply_chain..spu_sale_label_history.id
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_sale_label_history.spu_code
     *
     * @return the value of supply_chain..spu_sale_label_history.spu_code
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public String getSpuCode() {
        return spuCode;
    }

    /**
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public SpuSaleLabelHistory withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_sale_label_history.spu_code
     *
     * @param spuCode the value for supply_chain..spu_sale_label_history.spu_code
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_sale_label_history.label
     *
     * @return the value of supply_chain..spu_sale_label_history.label
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public String getLabel() {
        return label;
    }

    /**
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public SpuSaleLabelHistory withLabel(String label) {
        this.setLabel(label);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_sale_label_history.label
     *
     * @param label the value for supply_chain..spu_sale_label_history.label
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public void setLabel(String label) {
        this.label = label;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_sale_label_history.type
     *
     * @return the value of supply_chain..spu_sale_label_history.type
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public String getType() {
        return type;
    }

    /**
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public SpuSaleLabelHistory withType(String type) {
        this.setType(type);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_sale_label_history.type
     *
     * @param type the value for supply_chain..spu_sale_label_history.type
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_sale_label_history.create_time
     *
     * @return the value of supply_chain..spu_sale_label_history.create_time
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public SpuSaleLabelHistory withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_sale_label_history.create_time
     *
     * @param createTime the value for supply_chain..spu_sale_label_history.create_time
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_sale_label_history.update_time
     *
     * @return the value of supply_chain..spu_sale_label_history.update_time
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public SpuSaleLabelHistory withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_sale_label_history.update_time
     *
     * @param updateTime the value for supply_chain..spu_sale_label_history.update_time
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_sale_label_history.spu_offering_version
     *
     * @return the value of supply_chain..spu_sale_label_history.spu_offering_version
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public String getSpuOfferingVersion() {
        return spuOfferingVersion;
    }

    /**
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public SpuSaleLabelHistory withSpuOfferingVersion(String spuOfferingVersion) {
        this.setSpuOfferingVersion(spuOfferingVersion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_sale_label_history.spu_offering_version
     *
     * @param spuOfferingVersion the value for supply_chain..spu_sale_label_history.spu_offering_version
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public void setSpuOfferingVersion(String spuOfferingVersion) {
        this.spuOfferingVersion = spuOfferingVersion;
    }

    /**
     * This method returns the value of the database column supply_chain..spu_sale_label_history.spu_id
     *
     * @return the value of supply_chain..spu_sale_label_history.spu_id
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public String getSpuId() {
        return spuId;
    }

    /**
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public SpuSaleLabelHistory withSpuId(String spuId) {
        this.setSpuId(spuId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..spu_sale_label_history.spu_id
     *
     * @param spuId the value for supply_chain..spu_sale_label_history.spu_id
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public void setSpuId(String spuId) {
        this.spuId = spuId;
    }

    /**
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", label=").append(label);
        sb.append(", type=").append(type);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", spuOfferingVersion=").append(spuOfferingVersion);
        sb.append(", spuId=").append(spuId);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SpuSaleLabelHistory other = (SpuSaleLabelHistory) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getLabel() == null ? other.getLabel() == null : this.getLabel().equals(other.getLabel()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getSpuOfferingVersion() == null ? other.getSpuOfferingVersion() == null : this.getSpuOfferingVersion().equals(other.getSpuOfferingVersion()))
            && (this.getSpuId() == null ? other.getSpuId() == null : this.getSpuId().equals(other.getSpuId()));
    }

    /**
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getLabel() == null) ? 0 : getLabel().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getSpuOfferingVersion() == null) ? 0 : getSpuOfferingVersion().hashCode());
        result = prime * result + ((getSpuId() == null) ? 0 : getSpuId().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Feb 26 17:21:05 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        label("label", "label", "VARCHAR", false),
        type("type", "type", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        spuOfferingVersion("spu_offering_version", "spuOfferingVersion", "VARCHAR", false),
        spuId("spu_id", "spuId", "VARCHAR", false);

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Feb 26 17:21:05 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}