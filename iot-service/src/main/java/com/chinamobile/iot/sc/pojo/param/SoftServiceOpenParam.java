package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class SoftServiceOpenParam {
    /**
     * 订单id
     */
    @NotEmpty(message = "订单id不能为空")
    private String orderId;
    /**
     * 原子订单id
     */

    private String atomOrderId;
    /**
     * spu编码
     */
    @NotEmpty(message = "spuOfferingCode不能为空")
    private String spuOfferingCode;
    /**
     * sku编码
     */
    @NotEmpty(message = "skuOfferingCode不能为空")
    private String skuOfferingCode;
    /**
     * 原子编码
     */
    @NotEmpty(message = "atomOfferingCode不能为空")
    private String atomOfferingCode;
    /**
     * 操作类型 01：订购，03：退订
     */
    private String operateType;
    /**
     * 退款数量
     */
    private String specialAfterRefundsNumber;

}
