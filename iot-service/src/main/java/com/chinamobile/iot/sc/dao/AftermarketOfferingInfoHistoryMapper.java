package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.AftermarketOfferingInfoHistory;
import com.chinamobile.iot.sc.pojo.AftermarketOfferingInfoHistoryExample;
import com.chinamobile.iot.sc.pojo.AftermarketOfferingInfoHistoryKey;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AftermarketOfferingInfoHistoryMapper {
    /**
     *
     * @mbg.generated Tue Mar 12 15:24:08 CST 2024
     */
    long countByExample(AftermarketOfferingInfoHistoryExample example);

    /**
     *
     * @mbg.generated Tue Mar 12 15:24:08 CST 2024
     */
    int deleteByExample(AftermarketOfferingInfoHistoryExample example);

    /**
     *
     * @mbg.generated Tue Mar 12 15:24:08 CST 2024
     */
    int deleteByPrimaryKey(AftermarketOfferingInfoHistoryKey key);

    /**
     *
     * @mbg.generated Tue Mar 12 15:24:08 CST 2024
     */
    int insert(AftermarketOfferingInfoHistory record);

    /**
     *
     * @mbg.generated Tue Mar 12 15:24:08 CST 2024
     */
    int insertSelective(AftermarketOfferingInfoHistory record);

    /**
     *
     * @mbg.generated Tue Mar 12 15:24:08 CST 2024
     */
    List<AftermarketOfferingInfoHistory> selectByExample(AftermarketOfferingInfoHistoryExample example);

    /**
     *
     * @mbg.generated Tue Mar 12 15:24:08 CST 2024
     */
    AftermarketOfferingInfoHistory selectByPrimaryKey(AftermarketOfferingInfoHistoryKey key);

    /**
     *
     * @mbg.generated Tue Mar 12 15:24:08 CST 2024
     */
    int updateByExampleSelective(@Param("record") AftermarketOfferingInfoHistory record, @Param("example") AftermarketOfferingInfoHistoryExample example);

    /**
     *
     * @mbg.generated Tue Mar 12 15:24:08 CST 2024
     */
    int updateByExample(@Param("record") AftermarketOfferingInfoHistory record, @Param("example") AftermarketOfferingInfoHistoryExample example);

    /**
     *
     * @mbg.generated Tue Mar 12 15:24:08 CST 2024
     */
    int updateByPrimaryKeySelective(AftermarketOfferingInfoHistory record);

    /**
     *
     * @mbg.generated Tue Mar 12 15:24:08 CST 2024
     */
    int updateByPrimaryKey(AftermarketOfferingInfoHistory record);

    /**
     *
     * @mbg.generated Tue Mar 12 15:24:08 CST 2024
     */
    int batchInsert(@Param("list") List<AftermarketOfferingInfoHistory> list);

    /**
     *
     * @mbg.generated Tue Mar 12 15:24:08 CST 2024
     */
    int batchInsertSelective(@Param("list") List<AftermarketOfferingInfoHistory> list, @Param("selective") AftermarketOfferingInfoHistory.Column ... selective);
}