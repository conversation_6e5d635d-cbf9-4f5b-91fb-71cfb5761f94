package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/18
 * @description 改变订单保理状态参数
 */
@Data
public class OrderBaoliStatusChangeParam {

    /**
     * 原子订单id
     */
//    @NotEmpty(message = "原子订单id不能为空")
    private String orderAtomInfoId;

    @NotEmpty(message = "订单id不能为空")
    private String orderId;

}
