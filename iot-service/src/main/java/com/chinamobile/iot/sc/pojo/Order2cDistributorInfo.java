package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 订单分销员信息表
 *
 * <AUTHOR>
public class Order2cDistributorInfo implements Serializable {
    /**
     *
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    private String id;

    /**
     * 订单编号
     *
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    private String orderId;

    /**
     * 分销员级别 1：一级分销员 2：二级分销员
     *
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    private String distributorLevel;

    /**
     * 分销员电话,IOT加密
     *
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    private String distributorPhone;

    /**
     * 与分销员手机号（distributorPhone）搭配使用，传分销员的UserID信息
     *
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    private String distributorUserId;

    /**
     * 分销员推荐码
     *
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    private String distributorShareCode;

    /**
     *
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    private Date createTime;

    /**
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..order_2c_distributor_info.id
     *
     * @return the value of supply_chain..order_2c_distributor_info.id
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public Order2cDistributorInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_distributor_info.id
     *
     * @param id the value for supply_chain..order_2c_distributor_info.id
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_distributor_info.order_id
     *
     * @return the value of supply_chain..order_2c_distributor_info.order_id
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public Order2cDistributorInfo withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_distributor_info.order_id
     *
     * @param orderId the value for supply_chain..order_2c_distributor_info.order_id
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_distributor_info.distributor_level
     *
     * @return the value of supply_chain..order_2c_distributor_info.distributor_level
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public String getDistributorLevel() {
        return distributorLevel;
    }

    /**
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public Order2cDistributorInfo withDistributorLevel(String distributorLevel) {
        this.setDistributorLevel(distributorLevel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_distributor_info.distributor_level
     *
     * @param distributorLevel the value for supply_chain..order_2c_distributor_info.distributor_level
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public void setDistributorLevel(String distributorLevel) {
        this.distributorLevel = distributorLevel;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_distributor_info.distributor_phone
     *
     * @return the value of supply_chain..order_2c_distributor_info.distributor_phone
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public String getDistributorPhone() {
        return distributorPhone;
    }

    /**
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public Order2cDistributorInfo withDistributorPhone(String distributorPhone) {
        this.setDistributorPhone(distributorPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_distributor_info.distributor_phone
     *
     * @param distributorPhone the value for supply_chain..order_2c_distributor_info.distributor_phone
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public void setDistributorPhone(String distributorPhone) {
        this.distributorPhone = distributorPhone;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_distributor_info.distributor_user_id
     *
     * @return the value of supply_chain..order_2c_distributor_info.distributor_user_id
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public String getDistributorUserId() {
        return distributorUserId;
    }

    /**
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public Order2cDistributorInfo withDistributorUserId(String distributorUserId) {
        this.setDistributorUserId(distributorUserId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_distributor_info.distributor_user_id
     *
     * @param distributorUserId the value for supply_chain..order_2c_distributor_info.distributor_user_id
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public void setDistributorUserId(String distributorUserId) {
        this.distributorUserId = distributorUserId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_distributor_info.distributor_share_code
     *
     * @return the value of supply_chain..order_2c_distributor_info.distributor_share_code
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public String getDistributorShareCode() {
        return distributorShareCode;
    }

    /**
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public Order2cDistributorInfo withDistributorShareCode(String distributorShareCode) {
        this.setDistributorShareCode(distributorShareCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_distributor_info.distributor_share_code
     *
     * @param distributorShareCode the value for supply_chain..order_2c_distributor_info.distributor_share_code
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public void setDistributorShareCode(String distributorShareCode) {
        this.distributorShareCode = distributorShareCode;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_distributor_info.create_time
     *
     * @return the value of supply_chain..order_2c_distributor_info.create_time
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public Order2cDistributorInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_distributor_info.create_time
     *
     * @param createTime the value for supply_chain..order_2c_distributor_info.create_time
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", distributorLevel=").append(distributorLevel);
        sb.append(", distributorPhone=").append(distributorPhone);
        sb.append(", distributorUserId=").append(distributorUserId);
        sb.append(", distributorShareCode=").append(distributorShareCode);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Order2cDistributorInfo other = (Order2cDistributorInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getDistributorLevel() == null ? other.getDistributorLevel() == null : this.getDistributorLevel().equals(other.getDistributorLevel()))
            && (this.getDistributorPhone() == null ? other.getDistributorPhone() == null : this.getDistributorPhone().equals(other.getDistributorPhone()))
            && (this.getDistributorUserId() == null ? other.getDistributorUserId() == null : this.getDistributorUserId().equals(other.getDistributorUserId()))
            && (this.getDistributorShareCode() == null ? other.getDistributorShareCode() == null : this.getDistributorShareCode().equals(other.getDistributorShareCode()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    /**
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getDistributorLevel() == null) ? 0 : getDistributorLevel().hashCode());
        result = prime * result + ((getDistributorPhone() == null) ? 0 : getDistributorPhone().hashCode());
        result = prime * result + ((getDistributorUserId() == null) ? 0 : getDistributorUserId().hashCode());
        result = prime * result + ((getDistributorShareCode() == null) ? 0 : getDistributorShareCode().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Fri Sep 27 09:47:09 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        distributorLevel("distributor_level", "distributorLevel", "VARCHAR", false),
        distributorPhone("distributor_phone", "distributorPhone", "VARCHAR", false),
        distributorUserId("distributor_user_id", "distributorUserId", "VARCHAR", false),
        distributorShareCode("distributor_share_code", "distributorShareCode", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Fri Sep 27 09:47:09 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}