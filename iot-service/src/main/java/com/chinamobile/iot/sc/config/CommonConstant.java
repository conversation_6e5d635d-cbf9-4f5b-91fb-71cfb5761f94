package com.chinamobile.iot.sc.config;

import java.util.ArrayList;
import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2022/4/20 17:10
 */
public class CommonConstant {

    //用于过滤掉名称不含“移动”的PROVINCE
    public static final String MOBILE = "移动";
    public static final String SEPARATOR = "-";
    public static final String SLASH = "/";
    public static final String OTHER = "其他";
    //适配前端的名称
    public static final String NEIMENG = "内蒙";
    public static final String NEIMENGGU = "内蒙古";
    public static final String ALL_PLATFORM = "全网平台";
    public static final String NATIONWIDE = "全国";

    //存储省信息到redis的key, filed = code, value=省名称
    public static final String REDIS_PROVINCE_KEY = "province_cache";
    //存储区县信息到redis的key, filed = code, value=区县名称
    public static final String REDIS_REGION_KEY = "region";
    //存储地市信息到redis的key, filed = code, value=地市名称
    public static final String REDIS_LOCATION_KEY = "location";
    //存储市和对应省编码到redis的key
    public static final String REDIS_LOCATION_BEID_KEY = "locationBeId";
    //限制发送websocket的频率
    public static final String WEBSOCKET_REDIS_KEY = "websocket_data";
    //导入外部电商订单的business_code
    public static final String EXT_ORDER_BUSINESS_CODE = "SyncOSExtOrderInfo";
    //直辖市的省份编码，用于处理省份选择直辖市的时候，其实需要查询的是对应区县的数据而非地市
    public static final List<String> PROVINCITAL_CITY_LIST = new ArrayList<String>(){{add("100");add("210");add("220");add("230");}};

    public static Integer orderExportExcelExpireDays = 3;

    //保存onelink的请求公共token
    public static final String ONELINK_TOKEN_KEY = "oneLinkToken";
    //保存oneLink获取认证链接/结果推送的 流水号 和 商城流水号 的映射map
    public static final String REDIS_ONELINK_MALL_SEQ_KEY = "onelink_mall_seq";
    //保存商城流水号和手机号映射map
    public static final String REDIS_MALL_SEQ_PHONE = "mall_sql_phone";
    //产品流程割接的附件目录
    public static final String PRODUCT_FLOW_ATTACHMENT_PATH = "productFlow/";
    //订单附件附录
    public static final String ORDER_ATTACHMENT_PATH = "order/";

    //公告弹窗的key前缀,  后缀是 userId，value = 公告id列表； 控制一个用户每条公告每天只弹窗一次
    public static final String REDIS_ANNOUNCEMENT_POPUP = "announcementPopup_";

    //公告已读的key前缀,  后缀是 userId，value = 公告id列表； 保存每个用户已读的公告
    public static final String REDIS_ANNOUNCEMENT_READ = "announcementRead_";
}
