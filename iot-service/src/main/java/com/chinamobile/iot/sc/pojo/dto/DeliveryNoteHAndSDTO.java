package com.chinamobile.iot.sc.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * created by l<PERSON>xiang on 2024/9/23 14:32
 * 联合销售/合同履约 货单导出列表部分实体类，无设备型号列
 */
@Data
public class DeliveryNoteHAndSDTO {

    @Excel(name = "商品名称",width = 50,needMerge = true)
    private String skuName;

    @Excel(name = "数量",width = 50,needMerge = true)
    private String skuQuantity;

    @Excel(name = "备注",width = 50,needMerge = true)
    private String remark;
}
