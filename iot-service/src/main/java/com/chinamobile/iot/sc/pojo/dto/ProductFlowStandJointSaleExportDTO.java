package com.chinamobile.iot.sc.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2023/3/12 11:24
 * 导出流程主商品类
 */
@Data
public class ProductFlowStandJointSaleExportDTO {
    /**
     * 商品SPU信息
     */
    /**
     * 商品类目
     */
    private String shelfCatagoryId;
    /**
     * 商品组/销售商品名称
     */
    @Excel(name = "商品上架产品类目")
    private String shelfCatagoryName;

    /**
     * 一级导航目录
     */
//    @Excel(name = "一级导航目录")
    private String firstDirectoryId;
    /**
     * 一级导航目录名称
     */
    @Excel(name = "一级导航目录")
    private String firstDirectoryName;
    /**
     * 二级导航目录
     */
//    @Excel(name = "二级导航目录")
    private String secondDirectoryId;
    /**
     * 二级导航目录名称
     */
    @Excel(name = "二级导航目录")
    private String secondDirectoryName;
    /**
     * 三级导航目录
     */
//    @Excel(name = "三级导航目录")
    private String thirdDirectoryId;
    /**
     * 三级导航目录名称
     */
    @Excel(name = "三级导航目录")
    private String thirdDirectoryName;
//    /**
//     * 销售对象
//     */
//    @Excel(name = "销售对象")
//    private String saleObject;

    /**
     * 服务商
     */
    @Excel(name = "服务商")
    private String spuServiceProvider;
    /**
     * 产品管理部门
     */
    @Excel(name = "产品管理部门")
    private String manageDepartment;
    /**
     * 产品经理信息
     */
    @Excel(name = "产品经理信息")
    private String manager;
    /**
     * 销售标签
     */
    @Excel(name = "销售标签")
    private String saleTag;
    /**
     * 商品名称（SPU）
     */
    @Excel(name = "商品名称（SPU")
    private String spuName;
    /**
     * 商品简介
     */
    @Excel(name = "商品简介")
    private String productDesc;
    /**
     *商品应用场景
     */
    @Excel(name = "商品应用场景")
    private String applicationArea;
    /**
     *是否隐秘上架
     */
    @Excel(name = "是否隐秘上架")
    private String isHiddenShelf;
    /**
     *映射检索词
     */
    @Excel(name = "映射检索词")
    private String searchWord;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String spuRemark;



    /**
     * 商品规格SKU信息
     */
    /**
     * 规格名称（sku）
     */
    @Excel(name = "规格名称（sku）")
    private String skuName;
    /**
     * 规格简称（sku）
     */
    @Excel(name = "规格简称（sku）")
    private String skuShortName;
    /**
     * 起订量（默认为1）
     */
    @Excel(name = "起订量（默认为1）")
    private Integer minPurchaseNum;
    /**
     * 套餐销售产品名称
     */
    @Excel(name = "套餐销售产品名称")
    private String keyCompomentName;
    /**
     * 商品规格套餐销售产品及服务内容
     */
    @Excel(name = "商品规格套餐销售产品及服务内容")
    private String keyComponentServiceInfo;
    /**
     * 商品规格销售价
     */
    @Excel(name = "商品规格销售价")
    private Long salePrice;
    /**
     * 配送范围
     */
    @Excel(name = "配送范围")
    private String deliveryRange;
    /**
     * 游客/合作伙伴可见
     */
    @Excel(name = "游客可见")
    private String touristPartnerVisible;
    /**
     * 发布订购范围
     */
    @Excel(name = "发布订购范围")
    private String saleProvinceCity;

    /**
     * 是否发酬金
     */
    @Excel(name = "是否发酬金")
    private String hasRemuneration;
    /**
     * 酬金比例
     */
    @Excel(name = "酬金比例")
    private String remunerationPercent;
    /**
     * 标准服务名称
     */
    @Excel(name = "标准服务名称")
    private String standardProductName;
    /**
     * 产品部门  config表
     */
    @Excel(name = "产品部门")
    private String productDepartment;
    /**
     * 产品属性
     */
    @Excel(name = "产品属性")
    private String standardProductAttribute;
    /**
     * 合作厂商名
     */
    @Excel(name = "合作厂商名")
    private String cooperateCompany;
    /**
     * 商城订单处理人（主）
     */
    @Excel(name = "商城订单处理人（主）")
    private String orderMasterHandler;
    /**
     * 商城订单处理人（次）
     */
    @Excel(name = "商城订单处理人（次）")
    private String orderSlaveHandler;
    @Excel(name = "备注")
    private String skuRemark;

    /**
     * （合同履约类）硬件原子商品信息,软件功能费原子商品信息
     */
    /**
     * 硬件原子商品名称
     */
    @Excel(name = "硬件原子商品名称")
    private String atomName;
    /**
     * 硬件原子商品型号
     */
    @Excel(name = "硬件原子商品型号")
    private String model;
    /**
     * 硬件原子商品数量
     */
    @Excel(name = "硬件原子商品数量")
    private Integer atomQuantity;

    /**
     * 颜色
     */
    @Excel(name = "颜色")
    private String color;
    /**
     * 硬件原子商品终端物料编码
     */
    @Excel(name = "硬件原子商品终端物料编码")
    private String materialNum;
    /**
     * 硬件原子商品计量单位
     */
    @Excel(name = "硬件原子商品计量单位")
    private String unit;
    /**
     * 硬件原子商品结算价
     */
    @Excel(name = "硬件原子商品结算价")
    private Long settlePrice;
    /**
     * 硬件原子商品销售单价
     */
    @Excel(name = "硬件原子商品销售单价")
    private Long hardwarePrice;
    /**
     * 软件原子商品名称
     */
    @Excel(name = "软件原子商品名称")
    private String softAtomName;
    /**
     * 软件原子销售价格
     */
    @Excel(name = "软件原子销售价格")
    private Long softPrice;
    @Excel(name = "库存数")
    private Integer inventory;
    /**
     * 备注
     */
    @Excel(name = "备注")
    private String atomRemark;


    /**
     * 各环节接口人信息
     */
    /**
     * 售前市场经理
     */
    @Excel(name = "售前市场经理")
    private String beforeSaleManager;
    /**
     * 商品发货接口人
     */
    @Excel(name = "商品发货接口人")
    private String sendContactPerson;
    /**
     * 商品安装接口人
     */
    @Excel(name = "商品安装接口人")
    private String installContactPerson;
    /**
     * 物联卡套餐接口人
     */
    @Excel(name = "物联卡套餐接口人")
    private String iotPackageContactPerson;
    /**
     * 商品软件权限开通接口人
     */
    @Excel(name = "商品软件权限开通接口人")
    private String softAuthContactPerson;
    /**
     * 商品售后接口人
     */
    @Excel(name = "商品售后接口人")
    private String afterSaleContactPerson;
    /**
     * 质保/售后细则
     */
    @Excel(name = "质保/售后细则")
    private String afterMarketRule;
    /**
     *  商品维修联系信息与地址
     */
    @Excel(name = "商品维修联系信息与地址")
    private String repairContactInfo;
    /**
     *  商品退货联系信息与地址
     */
    @Excel(name = "商品退货联系信息与地址")
    private String returnContactInfo;


    /**
     * 商品套餐详细信息
     */


    /**
     * 商品厂商信息
     */
    @Excel(name = "商品厂商信息")
    private String productCompanyInfo;
    /**
     * 商品通信方式
     */
    @Excel(name = "商品通信方式")
    private String productCommunicationMethod;
    /**
     * 物联卡套餐说明
     */
    @Excel(name = "物联卡套餐说明")
    private String iotPackageInfo;
    /**
     * 硬件商品发货清单
     */
    @Excel(name = "硬件商品发货清单")
    private String hardwareSendList;
    /**
     * 商品参数信息
     */
    @Excel(name = "商品参数信息")
    private String productParamInfo;
    /**
     * 商品发货地址
     */
    @Excel(name = "商品发货地址")
    private String productSendAddress;
    /**
     * 硬件商品发货默认快递
     */
    @Excel(name = "硬件商品发货默认快递")
    private String hardwareExpress;
    /**
     *  商品发货时间信息
     */
    @Excel(name = "商品发货时间信息")
    private String productSendTimeInfo;
    /**
     *  商品使用条件
     */
    @Excel(name = "商品使用条件")
    private String productUseCondition;
    /**
     *  软件平台名称及介绍
     */
    @Excel(name = "软件平台名称及介绍")
    private String softPlatformInfo;
    /**
     * 软件平台下载方式及地址
     */
    @Excel(name = "软件平台下载方式及地址")
    private String softPlatformDownloadInfo;
    /**
     * APP、小程序名称及介绍
     */
    @Excel(name = "APP、小程序名称及介绍")
    private String appInfo;
    /**
     *  APP、小程序下载方式及地址
     */
    @Excel(name = "APP、小程序下载方式及地址")
    private String appDownloadInfo;
    /**
     *  安装服务说明
     */
    @Excel(name = "安装服务说明")
    private String installInfo;


    /**
     *  商城上架信息
     */
    @Excel(name = "商城链接")
    private String url;
    @Excel(name = "商品编码（SPU")
    private String spuCode;
    @Excel(name = "商品编码（SKU)")
    private String skuCode;
    @Excel(name = "备注")
    private String configRemark;


}
