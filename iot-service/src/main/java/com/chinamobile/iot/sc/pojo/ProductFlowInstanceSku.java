package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 流程实例sku信息表
 *
 * <AUTHOR>
public class ProductFlowInstanceSku implements Serializable {
    /**
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String id;

    /**
     * 流程实例id
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String flowInstanceId;

    /**
     * 流程id
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String flowId;

    /**
     * spu编码
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String spuCode;

    /**
     * sku编码
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String skuCode;

    /**
     * sku名称
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String skuName;

    /**
     * 商品规格简称
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String skuShortName;

    /**
     * 核心部件名称
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String keyCompomentName;

    /**
     * 核心部件及服务内容
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String keyComponentServiceInfo;

    /**
     * 销售价格，单位厘
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private Long salePrice;

    /**
     * 销售最低价，单位厘
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private Long saleMinPrice;

    /**
     * 销售最高价，单位厘
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private Long saleMaxPrice;

    /**
     * 是否允许价格区间外销售
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String saleOutOfPriceRange;

    /**
     * 商品规格发布订购范围。填写格式：
1全国
2省份,省份-地市（以英文,隔开）。
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String saleProvinceCity;

    /**
     * 配送范围：
     * 1. 全国：客户订购时，收货地址可选全国所有省份。
     * 2. 商品发布省：客户订购时，收货地址仅可选商品发布订购省、地市。
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String deliveryRange;

    /**
     * 游客/合作伙伴 是否可见
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String touristPartnerVisible;

    /**
     * 标准服务产品名称
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String standardProductName;

    /**
     * 产品属性，枚举值：自研、生态
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String standardProductAttribute;

    /**
     * 服务供应商
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String skuServiceProvider;

    /**
     * 产品管理部门
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String manageDepartment;

    /**
     * 标准服务产品经理
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String standardProductManager;

    /**
     * 接单账号
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String receiveOrderAccount;

    /**
     * 交付账号
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String deliverAccount;

    /**
     * 售后账号
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String aftermarketAccount;

    /**
     * 规格信息备注
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String skuRemark;

    /**
     * 省公司价格，单位厘
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private Long provincePrice;

    /**
     * 商品套餐销售（实际销售产品）名称
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String productPackageSaleName;

    /**
     * 商品规格套餐销售产品（实际销售产品）及服务内容
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String productPackageServiceContent;

    /**
     * 发货接口人
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String sendContactPerson;

    /**
     * 是否发酬金
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String hasRemuneration;

    /**
     * 酬金比例
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String remunerationPercent;

    /**
     * 合作厂商名
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String cooperateCompany;

    /**
     * 商城订单处理人(主)
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String orderMasterHandler;

    /**
     * 商城订单处理人(次)
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private String orderSlaveHandler;

    /**
     * 起订量
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private Integer minPurchaseNum;

    /**
     * 上下架状态 1-上架中 2-上架取消 3-已上架 4-下架中 5-下架取消 6-已下架
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private Integer shelfStatus;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.id
     *
     * @return the value of supply_chain..product_flow_instance_sku.id
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.id
     *
     * @param id the value for supply_chain..product_flow_instance_sku.id
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.flow_instance_id
     *
     * @return the value of supply_chain..product_flow_instance_sku.flow_instance_id
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getFlowInstanceId() {
        return flowInstanceId;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withFlowInstanceId(String flowInstanceId) {
        this.setFlowInstanceId(flowInstanceId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.flow_instance_id
     *
     * @param flowInstanceId the value for supply_chain..product_flow_instance_sku.flow_instance_id
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setFlowInstanceId(String flowInstanceId) {
        this.flowInstanceId = flowInstanceId;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.flow_id
     *
     * @return the value of supply_chain..product_flow_instance_sku.flow_id
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getFlowId() {
        return flowId;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withFlowId(String flowId) {
        this.setFlowId(flowId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.flow_id
     *
     * @param flowId the value for supply_chain..product_flow_instance_sku.flow_id
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setFlowId(String flowId) {
        this.flowId = flowId;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.spu_code
     *
     * @return the value of supply_chain..product_flow_instance_sku.spu_code
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getSpuCode() {
        return spuCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.spu_code
     *
     * @param spuCode the value for supply_chain..product_flow_instance_sku.spu_code
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.sku_code
     *
     * @return the value of supply_chain..product_flow_instance_sku.sku_code
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getSkuCode() {
        return skuCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withSkuCode(String skuCode) {
        this.setSkuCode(skuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.sku_code
     *
     * @param skuCode the value for supply_chain..product_flow_instance_sku.sku_code
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.sku_name
     *
     * @return the value of supply_chain..product_flow_instance_sku.sku_name
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getSkuName() {
        return skuName;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withSkuName(String skuName) {
        this.setSkuName(skuName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.sku_name
     *
     * @param skuName the value for supply_chain..product_flow_instance_sku.sku_name
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.sku_short_name
     *
     * @return the value of supply_chain..product_flow_instance_sku.sku_short_name
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getSkuShortName() {
        return skuShortName;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withSkuShortName(String skuShortName) {
        this.setSkuShortName(skuShortName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.sku_short_name
     *
     * @param skuShortName the value for supply_chain..product_flow_instance_sku.sku_short_name
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setSkuShortName(String skuShortName) {
        this.skuShortName = skuShortName;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.key_compoment_name
     *
     * @return the value of supply_chain..product_flow_instance_sku.key_compoment_name
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getKeyCompomentName() {
        return keyCompomentName;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withKeyCompomentName(String keyCompomentName) {
        this.setKeyCompomentName(keyCompomentName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.key_compoment_name
     *
     * @param keyCompomentName the value for supply_chain..product_flow_instance_sku.key_compoment_name
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setKeyCompomentName(String keyCompomentName) {
        this.keyCompomentName = keyCompomentName;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.key_component_service_info
     *
     * @return the value of supply_chain..product_flow_instance_sku.key_component_service_info
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getKeyComponentServiceInfo() {
        return keyComponentServiceInfo;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withKeyComponentServiceInfo(String keyComponentServiceInfo) {
        this.setKeyComponentServiceInfo(keyComponentServiceInfo);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.key_component_service_info
     *
     * @param keyComponentServiceInfo the value for supply_chain..product_flow_instance_sku.key_component_service_info
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setKeyComponentServiceInfo(String keyComponentServiceInfo) {
        this.keyComponentServiceInfo = keyComponentServiceInfo;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.sale_price
     *
     * @return the value of supply_chain..product_flow_instance_sku.sale_price
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public Long getSalePrice() {
        return salePrice;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withSalePrice(Long salePrice) {
        this.setSalePrice(salePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.sale_price
     *
     * @param salePrice the value for supply_chain..product_flow_instance_sku.sale_price
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setSalePrice(Long salePrice) {
        this.salePrice = salePrice;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.sale_min_price
     *
     * @return the value of supply_chain..product_flow_instance_sku.sale_min_price
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public Long getSaleMinPrice() {
        return saleMinPrice;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withSaleMinPrice(Long saleMinPrice) {
        this.setSaleMinPrice(saleMinPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.sale_min_price
     *
     * @param saleMinPrice the value for supply_chain..product_flow_instance_sku.sale_min_price
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setSaleMinPrice(Long saleMinPrice) {
        this.saleMinPrice = saleMinPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.sale_max_price
     *
     * @return the value of supply_chain..product_flow_instance_sku.sale_max_price
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public Long getSaleMaxPrice() {
        return saleMaxPrice;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withSaleMaxPrice(Long saleMaxPrice) {
        this.setSaleMaxPrice(saleMaxPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.sale_max_price
     *
     * @param saleMaxPrice the value for supply_chain..product_flow_instance_sku.sale_max_price
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setSaleMaxPrice(Long saleMaxPrice) {
        this.saleMaxPrice = saleMaxPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.sale_out_of_price_range
     *
     * @return the value of supply_chain..product_flow_instance_sku.sale_out_of_price_range
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getSaleOutOfPriceRange() {
        return saleOutOfPriceRange;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withSaleOutOfPriceRange(String saleOutOfPriceRange) {
        this.setSaleOutOfPriceRange(saleOutOfPriceRange);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.sale_out_of_price_range
     *
     * @param saleOutOfPriceRange the value for supply_chain..product_flow_instance_sku.sale_out_of_price_range
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setSaleOutOfPriceRange(String saleOutOfPriceRange) {
        this.saleOutOfPriceRange = saleOutOfPriceRange;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.sale_province_city
     *
     * @return the value of supply_chain..product_flow_instance_sku.sale_province_city
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getSaleProvinceCity() {
        return saleProvinceCity;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withSaleProvinceCity(String saleProvinceCity) {
        this.setSaleProvinceCity(saleProvinceCity);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.sale_province_city
     *
     * @param saleProvinceCity the value for supply_chain..product_flow_instance_sku.sale_province_city
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setSaleProvinceCity(String saleProvinceCity) {
        this.saleProvinceCity = saleProvinceCity;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.delivery_range
     *
     * @return the value of supply_chain..product_flow_instance_sku.delivery_range
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getDeliveryRange() {
        return deliveryRange;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withDeliveryRange(String deliveryRange) {
        this.setDeliveryRange(deliveryRange);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.delivery_range
     *
     * @param deliveryRange the value for supply_chain..product_flow_instance_sku.delivery_range
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setDeliveryRange(String deliveryRange) {
        this.deliveryRange = deliveryRange;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.tourist_partner_visible
     *
     * @return the value of supply_chain..product_flow_instance_sku.tourist_partner_visible
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getTouristPartnerVisible() {
        return touristPartnerVisible;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withTouristPartnerVisible(String touristPartnerVisible) {
        this.setTouristPartnerVisible(touristPartnerVisible);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.tourist_partner_visible
     *
     * @param touristPartnerVisible the value for supply_chain..product_flow_instance_sku.tourist_partner_visible
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setTouristPartnerVisible(String touristPartnerVisible) {
        this.touristPartnerVisible = touristPartnerVisible;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.standard_product_name
     *
     * @return the value of supply_chain..product_flow_instance_sku.standard_product_name
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getStandardProductName() {
        return standardProductName;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withStandardProductName(String standardProductName) {
        this.setStandardProductName(standardProductName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.standard_product_name
     *
     * @param standardProductName the value for supply_chain..product_flow_instance_sku.standard_product_name
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setStandardProductName(String standardProductName) {
        this.standardProductName = standardProductName;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.standard_product_attribute
     *
     * @return the value of supply_chain..product_flow_instance_sku.standard_product_attribute
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getStandardProductAttribute() {
        return standardProductAttribute;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withStandardProductAttribute(String standardProductAttribute) {
        this.setStandardProductAttribute(standardProductAttribute);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.standard_product_attribute
     *
     * @param standardProductAttribute the value for supply_chain..product_flow_instance_sku.standard_product_attribute
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setStandardProductAttribute(String standardProductAttribute) {
        this.standardProductAttribute = standardProductAttribute;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.sku_service_provider
     *
     * @return the value of supply_chain..product_flow_instance_sku.sku_service_provider
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getSkuServiceProvider() {
        return skuServiceProvider;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withSkuServiceProvider(String skuServiceProvider) {
        this.setSkuServiceProvider(skuServiceProvider);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.sku_service_provider
     *
     * @param skuServiceProvider the value for supply_chain..product_flow_instance_sku.sku_service_provider
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setSkuServiceProvider(String skuServiceProvider) {
        this.skuServiceProvider = skuServiceProvider;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.manage_department
     *
     * @return the value of supply_chain..product_flow_instance_sku.manage_department
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getManageDepartment() {
        return manageDepartment;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withManageDepartment(String manageDepartment) {
        this.setManageDepartment(manageDepartment);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.manage_department
     *
     * @param manageDepartment the value for supply_chain..product_flow_instance_sku.manage_department
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setManageDepartment(String manageDepartment) {
        this.manageDepartment = manageDepartment;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.standard_product_manager
     *
     * @return the value of supply_chain..product_flow_instance_sku.standard_product_manager
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getStandardProductManager() {
        return standardProductManager;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withStandardProductManager(String standardProductManager) {
        this.setStandardProductManager(standardProductManager);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.standard_product_manager
     *
     * @param standardProductManager the value for supply_chain..product_flow_instance_sku.standard_product_manager
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setStandardProductManager(String standardProductManager) {
        this.standardProductManager = standardProductManager;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.receive_order_account
     *
     * @return the value of supply_chain..product_flow_instance_sku.receive_order_account
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getReceiveOrderAccount() {
        return receiveOrderAccount;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withReceiveOrderAccount(String receiveOrderAccount) {
        this.setReceiveOrderAccount(receiveOrderAccount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.receive_order_account
     *
     * @param receiveOrderAccount the value for supply_chain..product_flow_instance_sku.receive_order_account
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setReceiveOrderAccount(String receiveOrderAccount) {
        this.receiveOrderAccount = receiveOrderAccount;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.deliver_account
     *
     * @return the value of supply_chain..product_flow_instance_sku.deliver_account
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getDeliverAccount() {
        return deliverAccount;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withDeliverAccount(String deliverAccount) {
        this.setDeliverAccount(deliverAccount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.deliver_account
     *
     * @param deliverAccount the value for supply_chain..product_flow_instance_sku.deliver_account
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setDeliverAccount(String deliverAccount) {
        this.deliverAccount = deliverAccount;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.aftermarket_account
     *
     * @return the value of supply_chain..product_flow_instance_sku.aftermarket_account
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getAftermarketAccount() {
        return aftermarketAccount;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withAftermarketAccount(String aftermarketAccount) {
        this.setAftermarketAccount(aftermarketAccount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.aftermarket_account
     *
     * @param aftermarketAccount the value for supply_chain..product_flow_instance_sku.aftermarket_account
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setAftermarketAccount(String aftermarketAccount) {
        this.aftermarketAccount = aftermarketAccount;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.sku_remark
     *
     * @return the value of supply_chain..product_flow_instance_sku.sku_remark
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getSkuRemark() {
        return skuRemark;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withSkuRemark(String skuRemark) {
        this.setSkuRemark(skuRemark);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.sku_remark
     *
     * @param skuRemark the value for supply_chain..product_flow_instance_sku.sku_remark
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setSkuRemark(String skuRemark) {
        this.skuRemark = skuRemark;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.province_price
     *
     * @return the value of supply_chain..product_flow_instance_sku.province_price
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public Long getProvincePrice() {
        return provincePrice;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withProvincePrice(Long provincePrice) {
        this.setProvincePrice(provincePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.province_price
     *
     * @param provincePrice the value for supply_chain..product_flow_instance_sku.province_price
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setProvincePrice(Long provincePrice) {
        this.provincePrice = provincePrice;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.product_package_sale_name
     *
     * @return the value of supply_chain..product_flow_instance_sku.product_package_sale_name
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getProductPackageSaleName() {
        return productPackageSaleName;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withProductPackageSaleName(String productPackageSaleName) {
        this.setProductPackageSaleName(productPackageSaleName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.product_package_sale_name
     *
     * @param productPackageSaleName the value for supply_chain..product_flow_instance_sku.product_package_sale_name
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setProductPackageSaleName(String productPackageSaleName) {
        this.productPackageSaleName = productPackageSaleName;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.product_package_service_content
     *
     * @return the value of supply_chain..product_flow_instance_sku.product_package_service_content
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getProductPackageServiceContent() {
        return productPackageServiceContent;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withProductPackageServiceContent(String productPackageServiceContent) {
        this.setProductPackageServiceContent(productPackageServiceContent);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.product_package_service_content
     *
     * @param productPackageServiceContent the value for supply_chain..product_flow_instance_sku.product_package_service_content
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setProductPackageServiceContent(String productPackageServiceContent) {
        this.productPackageServiceContent = productPackageServiceContent;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.send_contact_person
     *
     * @return the value of supply_chain..product_flow_instance_sku.send_contact_person
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getSendContactPerson() {
        return sendContactPerson;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withSendContactPerson(String sendContactPerson) {
        this.setSendContactPerson(sendContactPerson);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.send_contact_person
     *
     * @param sendContactPerson the value for supply_chain..product_flow_instance_sku.send_contact_person
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setSendContactPerson(String sendContactPerson) {
        this.sendContactPerson = sendContactPerson;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.has_remuneration
     *
     * @return the value of supply_chain..product_flow_instance_sku.has_remuneration
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getHasRemuneration() {
        return hasRemuneration;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withHasRemuneration(String hasRemuneration) {
        this.setHasRemuneration(hasRemuneration);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.has_remuneration
     *
     * @param hasRemuneration the value for supply_chain..product_flow_instance_sku.has_remuneration
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setHasRemuneration(String hasRemuneration) {
        this.hasRemuneration = hasRemuneration;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.remuneration_percent
     *
     * @return the value of supply_chain..product_flow_instance_sku.remuneration_percent
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getRemunerationPercent() {
        return remunerationPercent;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withRemunerationPercent(String remunerationPercent) {
        this.setRemunerationPercent(remunerationPercent);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.remuneration_percent
     *
     * @param remunerationPercent the value for supply_chain..product_flow_instance_sku.remuneration_percent
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setRemunerationPercent(String remunerationPercent) {
        this.remunerationPercent = remunerationPercent;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.cooperate_company
     *
     * @return the value of supply_chain..product_flow_instance_sku.cooperate_company
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getCooperateCompany() {
        return cooperateCompany;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withCooperateCompany(String cooperateCompany) {
        this.setCooperateCompany(cooperateCompany);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.cooperate_company
     *
     * @param cooperateCompany the value for supply_chain..product_flow_instance_sku.cooperate_company
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setCooperateCompany(String cooperateCompany) {
        this.cooperateCompany = cooperateCompany;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.order_master_handler
     *
     * @return the value of supply_chain..product_flow_instance_sku.order_master_handler
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getOrderMasterHandler() {
        return orderMasterHandler;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withOrderMasterHandler(String orderMasterHandler) {
        this.setOrderMasterHandler(orderMasterHandler);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.order_master_handler
     *
     * @param orderMasterHandler the value for supply_chain..product_flow_instance_sku.order_master_handler
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setOrderMasterHandler(String orderMasterHandler) {
        this.orderMasterHandler = orderMasterHandler;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.order_slave_handler
     *
     * @return the value of supply_chain..product_flow_instance_sku.order_slave_handler
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public String getOrderSlaveHandler() {
        return orderSlaveHandler;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withOrderSlaveHandler(String orderSlaveHandler) {
        this.setOrderSlaveHandler(orderSlaveHandler);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.order_slave_handler
     *
     * @param orderSlaveHandler the value for supply_chain..product_flow_instance_sku.order_slave_handler
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setOrderSlaveHandler(String orderSlaveHandler) {
        this.orderSlaveHandler = orderSlaveHandler;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.min_purchase_num
     *
     * @return the value of supply_chain..product_flow_instance_sku.min_purchase_num
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public Integer getMinPurchaseNum() {
        return minPurchaseNum;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withMinPurchaseNum(Integer minPurchaseNum) {
        this.setMinPurchaseNum(minPurchaseNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.min_purchase_num
     *
     * @param minPurchaseNum the value for supply_chain..product_flow_instance_sku.min_purchase_num
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setMinPurchaseNum(Integer minPurchaseNum) {
        this.minPurchaseNum = minPurchaseNum;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.shelf_status
     *
     * @return the value of supply_chain..product_flow_instance_sku.shelf_status
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public Integer getShelfStatus() {
        return shelfStatus;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withShelfStatus(Integer shelfStatus) {
        this.setShelfStatus(shelfStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.shelf_status
     *
     * @param shelfStatus the value for supply_chain..product_flow_instance_sku.shelf_status
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setShelfStatus(Integer shelfStatus) {
        this.shelfStatus = shelfStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.create_time
     *
     * @return the value of supply_chain..product_flow_instance_sku.create_time
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.create_time
     *
     * @param createTime the value for supply_chain..product_flow_instance_sku.create_time
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_sku.update_time
     *
     * @return the value of supply_chain..product_flow_instance_sku.update_time
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public ProductFlowInstanceSku withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_sku.update_time
     *
     * @param updateTime the value for supply_chain..product_flow_instance_sku.update_time
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", flowInstanceId=").append(flowInstanceId);
        sb.append(", flowId=").append(flowId);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", skuCode=").append(skuCode);
        sb.append(", skuName=").append(skuName);
        sb.append(", skuShortName=").append(skuShortName);
        sb.append(", keyCompomentName=").append(keyCompomentName);
        sb.append(", keyComponentServiceInfo=").append(keyComponentServiceInfo);
        sb.append(", salePrice=").append(salePrice);
        sb.append(", saleMinPrice=").append(saleMinPrice);
        sb.append(", saleMaxPrice=").append(saleMaxPrice);
        sb.append(", saleOutOfPriceRange=").append(saleOutOfPriceRange);
        sb.append(", saleProvinceCity=").append(saleProvinceCity);
        sb.append(", deliveryRange=").append(deliveryRange);
        sb.append(", touristPartnerVisible=").append(touristPartnerVisible);
        sb.append(", standardProductName=").append(standardProductName);
        sb.append(", standardProductAttribute=").append(standardProductAttribute);
        sb.append(", skuServiceProvider=").append(skuServiceProvider);
        sb.append(", manageDepartment=").append(manageDepartment);
        sb.append(", standardProductManager=").append(standardProductManager);
        sb.append(", receiveOrderAccount=").append(receiveOrderAccount);
        sb.append(", deliverAccount=").append(deliverAccount);
        sb.append(", aftermarketAccount=").append(aftermarketAccount);
        sb.append(", skuRemark=").append(skuRemark);
        sb.append(", provincePrice=").append(provincePrice);
        sb.append(", productPackageSaleName=").append(productPackageSaleName);
        sb.append(", productPackageServiceContent=").append(productPackageServiceContent);
        sb.append(", sendContactPerson=").append(sendContactPerson);
        sb.append(", hasRemuneration=").append(hasRemuneration);
        sb.append(", remunerationPercent=").append(remunerationPercent);
        sb.append(", cooperateCompany=").append(cooperateCompany);
        sb.append(", orderMasterHandler=").append(orderMasterHandler);
        sb.append(", orderSlaveHandler=").append(orderSlaveHandler);
        sb.append(", minPurchaseNum=").append(minPurchaseNum);
        sb.append(", shelfStatus=").append(shelfStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProductFlowInstanceSku other = (ProductFlowInstanceSku) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFlowInstanceId() == null ? other.getFlowInstanceId() == null : this.getFlowInstanceId().equals(other.getFlowInstanceId()))
            && (this.getFlowId() == null ? other.getFlowId() == null : this.getFlowId().equals(other.getFlowId()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getSkuCode() == null ? other.getSkuCode() == null : this.getSkuCode().equals(other.getSkuCode()))
            && (this.getSkuName() == null ? other.getSkuName() == null : this.getSkuName().equals(other.getSkuName()))
            && (this.getSkuShortName() == null ? other.getSkuShortName() == null : this.getSkuShortName().equals(other.getSkuShortName()))
            && (this.getKeyCompomentName() == null ? other.getKeyCompomentName() == null : this.getKeyCompomentName().equals(other.getKeyCompomentName()))
            && (this.getKeyComponentServiceInfo() == null ? other.getKeyComponentServiceInfo() == null : this.getKeyComponentServiceInfo().equals(other.getKeyComponentServiceInfo()))
            && (this.getSalePrice() == null ? other.getSalePrice() == null : this.getSalePrice().equals(other.getSalePrice()))
            && (this.getSaleMinPrice() == null ? other.getSaleMinPrice() == null : this.getSaleMinPrice().equals(other.getSaleMinPrice()))
            && (this.getSaleMaxPrice() == null ? other.getSaleMaxPrice() == null : this.getSaleMaxPrice().equals(other.getSaleMaxPrice()))
            && (this.getSaleOutOfPriceRange() == null ? other.getSaleOutOfPriceRange() == null : this.getSaleOutOfPriceRange().equals(other.getSaleOutOfPriceRange()))
            && (this.getSaleProvinceCity() == null ? other.getSaleProvinceCity() == null : this.getSaleProvinceCity().equals(other.getSaleProvinceCity()))
            && (this.getDeliveryRange() == null ? other.getDeliveryRange() == null : this.getDeliveryRange().equals(other.getDeliveryRange()))
            && (this.getTouristPartnerVisible() == null ? other.getTouristPartnerVisible() == null : this.getTouristPartnerVisible().equals(other.getTouristPartnerVisible()))
            && (this.getStandardProductName() == null ? other.getStandardProductName() == null : this.getStandardProductName().equals(other.getStandardProductName()))
            && (this.getStandardProductAttribute() == null ? other.getStandardProductAttribute() == null : this.getStandardProductAttribute().equals(other.getStandardProductAttribute()))
            && (this.getSkuServiceProvider() == null ? other.getSkuServiceProvider() == null : this.getSkuServiceProvider().equals(other.getSkuServiceProvider()))
            && (this.getManageDepartment() == null ? other.getManageDepartment() == null : this.getManageDepartment().equals(other.getManageDepartment()))
            && (this.getStandardProductManager() == null ? other.getStandardProductManager() == null : this.getStandardProductManager().equals(other.getStandardProductManager()))
            && (this.getReceiveOrderAccount() == null ? other.getReceiveOrderAccount() == null : this.getReceiveOrderAccount().equals(other.getReceiveOrderAccount()))
            && (this.getDeliverAccount() == null ? other.getDeliverAccount() == null : this.getDeliverAccount().equals(other.getDeliverAccount()))
            && (this.getAftermarketAccount() == null ? other.getAftermarketAccount() == null : this.getAftermarketAccount().equals(other.getAftermarketAccount()))
            && (this.getSkuRemark() == null ? other.getSkuRemark() == null : this.getSkuRemark().equals(other.getSkuRemark()))
            && (this.getProvincePrice() == null ? other.getProvincePrice() == null : this.getProvincePrice().equals(other.getProvincePrice()))
            && (this.getProductPackageSaleName() == null ? other.getProductPackageSaleName() == null : this.getProductPackageSaleName().equals(other.getProductPackageSaleName()))
            && (this.getProductPackageServiceContent() == null ? other.getProductPackageServiceContent() == null : this.getProductPackageServiceContent().equals(other.getProductPackageServiceContent()))
            && (this.getSendContactPerson() == null ? other.getSendContactPerson() == null : this.getSendContactPerson().equals(other.getSendContactPerson()))
            && (this.getHasRemuneration() == null ? other.getHasRemuneration() == null : this.getHasRemuneration().equals(other.getHasRemuneration()))
            && (this.getRemunerationPercent() == null ? other.getRemunerationPercent() == null : this.getRemunerationPercent().equals(other.getRemunerationPercent()))
            && (this.getCooperateCompany() == null ? other.getCooperateCompany() == null : this.getCooperateCompany().equals(other.getCooperateCompany()))
            && (this.getOrderMasterHandler() == null ? other.getOrderMasterHandler() == null : this.getOrderMasterHandler().equals(other.getOrderMasterHandler()))
            && (this.getOrderSlaveHandler() == null ? other.getOrderSlaveHandler() == null : this.getOrderSlaveHandler().equals(other.getOrderSlaveHandler()))
            && (this.getMinPurchaseNum() == null ? other.getMinPurchaseNum() == null : this.getMinPurchaseNum().equals(other.getMinPurchaseNum()))
            && (this.getShelfStatus() == null ? other.getShelfStatus() == null : this.getShelfStatus().equals(other.getShelfStatus()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFlowInstanceId() == null) ? 0 : getFlowInstanceId().hashCode());
        result = prime * result + ((getFlowId() == null) ? 0 : getFlowId().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getSkuCode() == null) ? 0 : getSkuCode().hashCode());
        result = prime * result + ((getSkuName() == null) ? 0 : getSkuName().hashCode());
        result = prime * result + ((getSkuShortName() == null) ? 0 : getSkuShortName().hashCode());
        result = prime * result + ((getKeyCompomentName() == null) ? 0 : getKeyCompomentName().hashCode());
        result = prime * result + ((getKeyComponentServiceInfo() == null) ? 0 : getKeyComponentServiceInfo().hashCode());
        result = prime * result + ((getSalePrice() == null) ? 0 : getSalePrice().hashCode());
        result = prime * result + ((getSaleMinPrice() == null) ? 0 : getSaleMinPrice().hashCode());
        result = prime * result + ((getSaleMaxPrice() == null) ? 0 : getSaleMaxPrice().hashCode());
        result = prime * result + ((getSaleOutOfPriceRange() == null) ? 0 : getSaleOutOfPriceRange().hashCode());
        result = prime * result + ((getSaleProvinceCity() == null) ? 0 : getSaleProvinceCity().hashCode());
        result = prime * result + ((getDeliveryRange() == null) ? 0 : getDeliveryRange().hashCode());
        result = prime * result + ((getTouristPartnerVisible() == null) ? 0 : getTouristPartnerVisible().hashCode());
        result = prime * result + ((getStandardProductName() == null) ? 0 : getStandardProductName().hashCode());
        result = prime * result + ((getStandardProductAttribute() == null) ? 0 : getStandardProductAttribute().hashCode());
        result = prime * result + ((getSkuServiceProvider() == null) ? 0 : getSkuServiceProvider().hashCode());
        result = prime * result + ((getManageDepartment() == null) ? 0 : getManageDepartment().hashCode());
        result = prime * result + ((getStandardProductManager() == null) ? 0 : getStandardProductManager().hashCode());
        result = prime * result + ((getReceiveOrderAccount() == null) ? 0 : getReceiveOrderAccount().hashCode());
        result = prime * result + ((getDeliverAccount() == null) ? 0 : getDeliverAccount().hashCode());
        result = prime * result + ((getAftermarketAccount() == null) ? 0 : getAftermarketAccount().hashCode());
        result = prime * result + ((getSkuRemark() == null) ? 0 : getSkuRemark().hashCode());
        result = prime * result + ((getProvincePrice() == null) ? 0 : getProvincePrice().hashCode());
        result = prime * result + ((getProductPackageSaleName() == null) ? 0 : getProductPackageSaleName().hashCode());
        result = prime * result + ((getProductPackageServiceContent() == null) ? 0 : getProductPackageServiceContent().hashCode());
        result = prime * result + ((getSendContactPerson() == null) ? 0 : getSendContactPerson().hashCode());
        result = prime * result + ((getHasRemuneration() == null) ? 0 : getHasRemuneration().hashCode());
        result = prime * result + ((getRemunerationPercent() == null) ? 0 : getRemunerationPercent().hashCode());
        result = prime * result + ((getCooperateCompany() == null) ? 0 : getCooperateCompany().hashCode());
        result = prime * result + ((getOrderMasterHandler() == null) ? 0 : getOrderMasterHandler().hashCode());
        result = prime * result + ((getOrderSlaveHandler() == null) ? 0 : getOrderSlaveHandler().hashCode());
        result = prime * result + ((getMinPurchaseNum() == null) ? 0 : getMinPurchaseNum().hashCode());
        result = prime * result + ((getShelfStatus() == null) ? 0 : getShelfStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Mar 11 15:02:25 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        flowInstanceId("flow_instance_id", "flowInstanceId", "VARCHAR", false),
        flowId("flow_id", "flowId", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        skuCode("sku_code", "skuCode", "VARCHAR", false),
        skuName("sku_name", "skuName", "VARCHAR", false),
        skuShortName("sku_short_name", "skuShortName", "VARCHAR", false),
        keyCompomentName("key_compoment_name", "keyCompomentName", "VARCHAR", false),
        keyComponentServiceInfo("key_component_service_info", "keyComponentServiceInfo", "VARCHAR", false),
        salePrice("sale_price", "salePrice", "BIGINT", false),
        saleMinPrice("sale_min_price", "saleMinPrice", "BIGINT", false),
        saleMaxPrice("sale_max_price", "saleMaxPrice", "BIGINT", false),
        saleOutOfPriceRange("sale_out_of_price_range", "saleOutOfPriceRange", "VARCHAR", false),
        saleProvinceCity("sale_province_city", "saleProvinceCity", "VARCHAR", false),
        deliveryRange("delivery_range", "deliveryRange", "VARCHAR", false),
        touristPartnerVisible("tourist_partner_visible", "touristPartnerVisible", "VARCHAR", false),
        standardProductName("standard_product_name", "standardProductName", "VARCHAR", false),
        standardProductAttribute("standard_product_attribute", "standardProductAttribute", "VARCHAR", false),
        skuServiceProvider("sku_service_provider", "skuServiceProvider", "VARCHAR", false),
        manageDepartment("manage_department", "manageDepartment", "VARCHAR", false),
        standardProductManager("standard_product_manager", "standardProductManager", "VARCHAR", false),
        receiveOrderAccount("receive_order_account", "receiveOrderAccount", "VARCHAR", false),
        deliverAccount("deliver_account", "deliverAccount", "VARCHAR", false),
        aftermarketAccount("aftermarket_account", "aftermarketAccount", "VARCHAR", false),
        skuRemark("sku_remark", "skuRemark", "VARCHAR", false),
        provincePrice("province_price", "provincePrice", "BIGINT", false),
        productPackageSaleName("product_package_sale_name", "productPackageSaleName", "VARCHAR", false),
        productPackageServiceContent("product_package_service_content", "productPackageServiceContent", "VARCHAR", false),
        sendContactPerson("send_contact_person", "sendContactPerson", "VARCHAR", false),
        hasRemuneration("has_remuneration", "hasRemuneration", "VARCHAR", false),
        remunerationPercent("remuneration_percent", "remunerationPercent", "VARCHAR", false),
        cooperateCompany("cooperate_company", "cooperateCompany", "VARCHAR", false),
        orderMasterHandler("order_master_handler", "orderMasterHandler", "VARCHAR", false),
        orderSlaveHandler("order_slave_handler", "orderSlaveHandler", "VARCHAR", false),
        minPurchaseNum("min_purchase_num", "minPurchaseNum", "INTEGER", false),
        shelfStatus("shelf_status", "shelfStatus", "INTEGER", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Mar 11 15:02:25 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}