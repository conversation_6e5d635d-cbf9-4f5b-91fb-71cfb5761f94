package com.chinamobile.iot.sc.pojo.invoice;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("invoice_reverse_info")
public class InvoiceReverseInfo {
    @TableId
    private String id;

    private String orderSeq;

    private String orderId;

    private String atomOrderId;

    private String frank;

    private String voucherType;

    private String billingDate;

    private String creditNoteId;

    private String creditNoteNum;

    private Long creditNoteSum;

    private String voucherId;

    private String voucherNum;

    private Long voucherSum;

    private String voucherInnerUrl;

    private String voucherOuterUrl;

    private String des;

    private String beId;

    private String homeCity;

    private String remark;

    private String cooperatorId;

    private Date createTime;

    private Date updateTime;
}
