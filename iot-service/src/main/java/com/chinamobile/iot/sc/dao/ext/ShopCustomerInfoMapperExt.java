package com.chinamobile.iot.sc.dao.ext;

import com.chinamobile.iot.sc.pojo.dto.CustCodeCustIdDTO;
import com.chinamobile.iot.sc.pojo.mapper.ShopUserUpdateDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2024/5/31 14:22
 * @description:
 **/
@Mapper
public interface ShopCustomerInfoMapperExt {

    /**
     * 查询客户经理详情集合
     * @param userCode
     * @return
     */
   List<ShopUserUpdateDO>  getShopManagerInfoList(@Param("userCode") String userCode);

    /**
     * 查询客户经理历史集合
     * @param userCode
     * @return
     */
    List<ShopUserUpdateDO>  getShopManagerInfoHistoryList(@Param("userCode") String userCode);


    /**
     * 查询普通，分销详情集合
     * @param userCode
     * @param custId
     * @return
     */
    List<ShopUserUpdateDO>  getShopCustomerInfoList(@Param("userCode") String userCode,@Param("custId") String custId);


    /**
     * 查询普通，分销历史集合
     * @param userCode
     * @param custId
     * @return
     */
    List<ShopUserUpdateDO>  getShopCustomerInfoHistoryList(@Param("userCode") String userCode,@Param("custId") String custId);

    List<CustCodeCustIdDTO> custCodeCustIdList(List<String> custCodeList);

}
