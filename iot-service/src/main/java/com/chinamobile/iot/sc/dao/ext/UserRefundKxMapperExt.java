package com.chinamobile.iot.sc.dao.ext;

import com.chinamobile.iot.sc.pojo.param.KxCanChooseUserParam;
import com.chinamobile.iot.sc.pojo.param.QueryNoticeUserParam;
import com.chinamobile.iot.sc.pojo.vo.KxCanChooseUserVO;
import com.chinamobile.iot.sc.pojo.vo.UserRefundKxVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/14
 * @description 卡+X退货发送短信的相关人员配置mapper扩展类
 */
public interface UserRefundKxMapperExt {

    /**
     * 查询卡+X退货发送短信的可选人员
     * @param kxCanChooseUserParam
     * @return
     */
    List<KxCanChooseUserVO> listKxCanChooseUser(@Param(value = "kxCanChooseUserParam") KxCanChooseUserParam kxCanChooseUserParam);

    /**
     * 查询卡+X退货发送短信的人员
     * @return
     */
    List<UserRefundKxVO> listUserRefundKx(@Param(value = "param") QueryNoticeUserParam param);
}
