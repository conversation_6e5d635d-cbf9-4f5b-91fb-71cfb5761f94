package com.chinamobile.iot.sc.constant.enums;

/**
 * <AUTHOR>
 * @date 2021/11/25 14:51
 */

public enum ProvinceEnum {
    Unknown("未知","Unknown"),
    Henan("河南","Henan"),
    HeBei("河北","HeBei"),
    Qinghai("青海","Qinghai"),
    Beijing("北京","Beijing"),
    Shanghai("上海","Shanghai"),
    Tianhin("天津","<PERSON>ianhin"),
    Chongqing("重庆","Chongqing"),
    Anhui("安徽","Anhui"),
    Fujian("福建","Fujian"),
    Guangdong("广东","Guangdong"),
    Guangxi("广西","Guangxi"),
    Guizhou("贵州","Guizhou"),
    Gansu("甘肃","Gansu"),
    Hainan("海南","Hainan"),
    Heilongjiang("黑龙江","Heilongjiang"),
    Hubei("湖北","Hubei"),
    Hunan("湖南","Hunan"),
    Jilin("吉林","<PERSON><PERSON>"),
    Jiang<PERSON>("江苏","<PERSON><PERSON>"),
    Jiangxi("江西","<PERSON><PERSON>"),
    Liaoning("辽宁","<PERSON><PERSON>"),
    <PERSON><PERSON><PERSON>gu("内蒙古","<PERSON>eimenggu"),
    Ningxia("宁夏","Ningxia"),
    Shanxi1("陕西","Shanxi1"),
    Shanxi2("山西","Shanxi2"),
    Shandong("山东","Shandong"),
    Sichuan("四川","Sichuan"),
    Xizang("西藏","Xizang"),
    Xinjiang("新疆","Xinjiang"),
    Yunnan("云南","Yunnan"),
    Zhejang("浙江","Zhejang");


    private String name;
    private String code;

    ProvinceEnum(String name, String code){
        this.name = name;
        this.code = code;
    }

    public String getName(){
        return name;
    }

    public String getCode(){
        return code;
    }

}
