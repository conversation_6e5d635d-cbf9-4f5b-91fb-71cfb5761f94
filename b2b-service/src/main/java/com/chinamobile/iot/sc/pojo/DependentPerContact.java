package com.chinamobile.iot.sc.pojo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("dependent_per_contact")
public class DependentPerContact {
    @TableId
    private String contactId;

    private String name;

    private String phone;

    private String email;

    private String company;

    private String province;

    private String provinceName;

    private String creator;

    private Date createTime;

    private Date updateTime;
}
