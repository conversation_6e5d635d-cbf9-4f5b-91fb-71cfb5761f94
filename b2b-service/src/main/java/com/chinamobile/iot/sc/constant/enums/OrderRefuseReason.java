package com.chinamobile.iot.sc.constant.enums;

public enum OrderRefuseReason {
    AUDIT_REFUSED(1, "合作伙伴退款申请审核不通过"),
    ACCEPTANCE_REFUSED(2, "合作伙伴退货验收不通过"),
    TIMEOUT(3, "审批超时拒绝"),
    CUSTOMER_CANCEL(4, "客户取消退款"),
    BUYER_TIMEOUT(5, "买家超时未上传物流");

    private Integer code;

    private String name;

    OrderRefuseReason(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        OrderRefuseReason[] reasons = values();
        for (OrderRefuseReason reason : reasons) {
            if (reason.code == code) {
                return reason.name;
            }
        }
        return null;
    }
}
