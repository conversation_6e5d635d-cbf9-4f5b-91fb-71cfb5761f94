package com.chinamobile.iot.sc.dto.request;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;

/**
 * @package: com.chinamobile.iot.sc.request.invoice
 * @ClassName: Request4DepContactUpdate
 * @description: 联系人修改请求
 * @author: zyj
 * @create: 2021/12/14 19:35
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@Accessors(chain = true)
public class Request4DepContactUpdate {
    @NotBlank(message = "非独立履约联系人id不能为空！")
    private String contactId;
    @NotBlank(message = "联系人姓名必填！")
    private String name;
    @NotBlank(message = "手机号必填！")
    private String phone;
    @Length(max = 30, message = "邮箱长度-30内字符")
    @NotBlank(message = "邮箱必填！")
    private String email;
    @NotBlank(message = "公司必填！")
    private String company;
    @NotBlank(message = "省份必填！")
    private String province;

}
