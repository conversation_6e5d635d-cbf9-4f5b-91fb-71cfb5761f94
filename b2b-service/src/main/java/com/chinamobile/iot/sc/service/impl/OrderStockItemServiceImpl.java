package com.chinamobile.iot.sc.service.impl;


import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chinamobile.iot.sc.entity.OrderStockItem;
import com.chinamobile.iot.sc.mapper.OrderStockItemMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 订单备货产品信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
@Service
public class OrderStockItemServiceImpl extends ServiceImpl<OrderStockItemMapper, OrderStockItem> implements IService<OrderStockItem> {


}
