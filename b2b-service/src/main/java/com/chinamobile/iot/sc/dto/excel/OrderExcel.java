package com.chinamobile.iot.sc.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @version 1.0
 * @Author: liang
 * @Date: 2021/8/31 16:34
 */
@Data
public class OrderExcel {
    @ExcelProperty("订单号")
    private String orderNum;

    @ExcelProperty("渠道ID")
    private String orgId;

    @ExcelProperty("渠道类型")
    private String orgType;

    @ExcelProperty("供应商")
    private String supplier;

    /**
     * 订单金额
     */
    @ExcelProperty("订单金额")
    private BigDecimal orderAmount;

    /**
     * 收货人姓名
     */
    @ExcelProperty("收货人")
    private String rcvContact;


    /**
     * 电话号码
     */
    @ExcelProperty("收货电话")
    private String rcvContactPhone;


    /**
     * 收货人地址
     */
    @ExcelProperty("收货地址")
    private String rcvContactAddress;

    /**
     * 订单行id  todo: 订单行是什么?
     * 订单流水
     */
    @ExcelProperty("订单流水")
    private String subOrderNum;
    /**
     * 供应商
     */
    @ExcelProperty("供应商")
    private String vendorId;

    /**
     * 产品编码
     * 商品编号
     */
    @ExcelProperty("产品编码")
    private String itemCode;

    /**
     * 颜色
     * 商品规格
     */
    @ExcelProperty("颜色")
    private String sku;

    /**
     * 单价
     * 商品单价
     */
    @ExcelProperty("商品单价")
    private BigDecimal unitPrice;

    /**
     * 数量
     * 商品数量
     */
    @ExcelProperty("数量")
    private String qty;

    /**
     * 营销活动代码
     */
    @ExcelProperty("营销活动代码")
    private String activityCode;

    /**
     * 营销活动名称
     */
    @ExcelProperty("营销活动名称")
    private String activityName;

    /**
     * 省份名称
     */
    @ExcelProperty("所属省份")
    private String provinceName;


}

