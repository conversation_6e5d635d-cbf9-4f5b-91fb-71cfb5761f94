package com.chinamobile.iot.sc.dto.vo;

import com.alibaba.fastjson.JSON;

import java.util.HashMap;
import java.util.Map;

public class ResponseVo<M> {

    //返回状态：1-成功,0失败 -- update by liang
    private int code;

    private String message;

    private M data;

    private ResponseVo() {
    }

    /**
     * 成功时，返回1
     *
     * @param m
     * @param <M>
     * @return
     */
    public static <M> ResponseVo success(M m) {
        ResponseVo responseVo = new ResponseVo();
        responseVo.setCode(1);
        responseVo.setMessage("success");
        responseVo.setData(m);
        return responseVo;
    }


    /**
     * 成功时，返回1
     *
     * @param msg
     * @param <M>
     * @return
     */
    public static <M> ResponseVo success(M data,String msg) {
        ResponseVo responseVo = new ResponseVo();
        responseVo.setCode(1);
        responseVo.setMessage(msg);
        responseVo.setData(data);
        return responseVo;
    }



    /**
     * 成功时，返回1
     *
     * @param msg
     * @param <M>
     * @return
     */
    public static <M> ResponseVo success(String msg) {
        ResponseVo responseVo = new ResponseVo();
        responseVo.setCode(1);
        responseVo.setMessage(msg);
        return responseVo;
    }

    /**
     * 失败时，返回0
     *
     * @param msg
     * @param <M>
     * @return
     */
    public static <M> ResponseVo fail(String msg) {
        ResponseVo responseVo = new ResponseVo();
        responseVo.setCode(0);
        responseVo.setMessage(msg);
        return responseVo;
    }

    public static <M> ResponseVo fail(int code, String msg) {
        ResponseVo responseVo = new ResponseVo();
        responseVo.setCode(code);
        responseVo.setMessage(msg);
        return responseVo;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public M getData() {
        return data;
    }

    public void setData(M data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    /**
     * 系统暂不需用
     * 将data 转换成 map对象
     *
     * @param data
     * @return
     */
    public Map<String, Object> objectToMap(M data) {

        Map<String, Object> map = new HashMap<>();
        if (data instanceof Map) {
            // 每页条数
            map.put("limit", ((Map) data).get("limit"));
            // 第几页
            map.put("page", ((Map) data).get("page"));
            // 排序参数
            map.put("field", ((Map) data).get("field"));
            // 排序：升降序
            map.put("isAsc", ((Map) data).get("isAsc"));
        }
        return map;
    }

}
