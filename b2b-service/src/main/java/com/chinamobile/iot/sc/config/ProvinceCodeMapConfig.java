package com.chinamobile.iot.sc.config;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * @package: com.chinamobile.iot.sgs.config
 * @ClassName: EventCodeMapConfig
 * @description: 预警事件类型关系映射Map
 * @author: zyj
 * @create: 2021/8/17 14:25
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Slf4j
@Component
public class ProvinceCodeMapConfig implements CommandLineRunner, Ordered {
    //省份名-代码关系映射Map
    private HashMap<String, String> provinceNameCodeMap;
    //代码-省份名关系映射Map
    private HashMap<String, String> provinceCodeNameMap;
    @Resource
    private ProvinceConfig provinceConfig;

    public HashMap<String, String> getProvinceNameCodeMap() {
        return provinceNameCodeMap;
    }
    public HashMap<String, String> getProvinceCodeNameMap() {
        return provinceCodeNameMap;
    }
    @Override
    public void run(String... args) throws Exception {
        log.info("......init provinceCodeMap");
        List<ProvinceConfig.ProvinceCode> provinceCodes = provinceConfig.getProvinceCodes();
        if(ObjectUtils.isEmpty(provinceNameCodeMap)){
            provinceNameCodeMap = Maps.newHashMap();
            for(ProvinceConfig.ProvinceCode provinceCode : provinceCodes){
                provinceNameCodeMap.put(provinceCode.getProvinceName(), provinceCode.getProvinceCode());
            }
        }
        if(ObjectUtils.isEmpty(provinceCodeNameMap)){
            provinceCodeNameMap = Maps.newHashMap();
            for(ProvinceConfig.ProvinceCode provinceCode : provinceCodes){
                provinceCodeNameMap.put(provinceCode.getProvinceCode(), provinceCode.getProvinceName());
            }
        }
    }

    @Override
    public int getOrder() {
        return 1;
    }
}
