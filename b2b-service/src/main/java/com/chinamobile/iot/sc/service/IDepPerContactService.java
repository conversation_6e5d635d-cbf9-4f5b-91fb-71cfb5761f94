package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.config.ProvinceConfig;
import com.chinamobile.iot.sc.dto.dependent.Data4DepPerContact;
import com.chinamobile.iot.sc.dto.request.Request4DepContactAdd;
import com.chinamobile.iot.sc.dto.request.Request4DepContactPage;
import com.chinamobile.iot.sc.dto.request.Request4DepContactUpdate;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.DependentPerContact;

import java.util.List;

/**
 * @package: com.chinamobile.iot.sc.service
 * @ClassName: IDepPerContactService
 * @description: 非独立履约联系人Service
 * @author: zyj
 * @create: 2021/12/14 17:53
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
public interface IDepPerContactService {

    // 分页查询
    BaseAnswer<PageData<Data4DepPerContact>> findPageByProvince(Request4DepContactPage request);
    // 增
    BaseAnswer<Void> addContact(Request4DepContactAdd request, String userId);
    // 删
    BaseAnswer<Void> deleteContact(String contactId);
    // 改
    BaseAnswer<Void> updateContact(Request4DepContactUpdate request, String userId);
    // 根据省查询
    BaseAnswer<List<DependentPerContact>> findContactList(String province);
    // 查询省份代码
    BaseAnswer<List<ProvinceConfig.ProvinceCode>> provinces();
}
