package com.chinamobile.iot.sc.dto;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.dto.vo.ResponseVo;
import com.chinamobile.iot.sc.utils.ToolUtil;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * liang
 * 分页 Dto
 */
@Data
public class PageDto<T> {

    // 每页数量
    private Integer limit;

    // 哪一页
    private Integer page;

    // 排序字段
    private String field;

    // 升降序
    // 1: 升序（true）
    // 2: 降序（false）
    private Integer isAsc;

    @Override
    public String toString() {
        return "PageDto{" +
                "limit=" + limit +
                ", page=" + page +
                ", field='" + field + '\'' +
                ", isAsc=" + isAsc +
                '}';
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public Integer getIsAsc() {
        return isAsc;
    }

    public void setIsAsc(Integer isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 验证字段是否为空
     * 若为空，返回默认值
     *
     * @return
     */
    public ResponseVo check() {

        // 页面条数: 默认为20
        if (ToolUtil.isEmpty(limit) || limit < 0) {
            limit = 20;
        }
        // 默认为第一页
        if (ToolUtil.isEmpty(page) || page <= 0) {
            page = 1;
        }
        // 排序字段: 默认为id
        if (ToolUtil.isEmpty(field)) {
            field = "id";
        }
        // 是否升序: 默认为升序
        // 前端只能传0或1
        // 1代表升序，0代表降序
        if (ToolUtil.isEmpty(isAsc)) {
            isAsc = 1;
        } else if (isAsc != 0 && isAsc != 1) {
            return ResponseVo.fail("排序字段不合法");
        }
        // 1 -- true -- ASC
        // 0 -- false -- DESC
        boolean flag = true;
        if (isAsc == 1) {
            flag = true;
        } else {
            flag = false;
        }

        Map<String, Object> map = new HashMap<>();
        map.put("limit", limit);
        map.put("page", page);
        map.put("field", field);
        map.put("isAsc", flag);
        return ResponseVo.success(map);
    }

    /**
     * 将第几页转化成
     * 数据库查询的Limit条件
     * limit A, B
     * A: 从第几条开始查询
     * B: 每页条数
     *
     * @return
     */
    public Integer pageToSql() {
        // (第几页- 1) * 每页条数
        return (page - 1) * limit;
    }

    /**
     * 自建的sql语句使用
     * 0: DESC
     * 1: ASC
     *
     * @return
     */
    public String isAscToStr() {
        if (isAsc == 1) {
            return "ASC";
        } else {
            return "DESC";
        }
    }

    /**
     * mybatis自带的Page方法使用
     * 0: false
     * 1: true
     */
    public Boolean isAscToBoo() {
        if (isAsc == 1) {
            return true;
        } else {
            return false;
        }
    }

    public Page<T> defaultPage() {
        if (ToolUtil.isEmpty(limit) || limit < 0) {
            limit = 20;
        }
        // 默认为第一页
        if (ToolUtil.isEmpty(page) || page <= 0) {
            page = 1;
        }
        int limit = this.limit;     //每页多少条数据
        if (ToolUtil.isEmpty(field)) {
            Page<T> page = new Page<>(this.page, limit);
//            page.setOpenSort(false);
            return page;
        } else {
            Page<T> page = new Page<T>(this.page, limit);
            if (this.isAsc == 1) {
                page.addOrder(OrderItem.asc(this.field));
            } else {
                page.addOrder(OrderItem.desc(this.field));
            }
            return page;
        }
    }
}
