<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.mapper.Order2cAtomSnMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.entity.Order2cAtomSn">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 15:59:46 CST 2022. by MyBatis Generator, do not modify.
    -->
    <result column="atom_order_id" jdbcType="VARCHAR" property="atomOrderId" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 15:59:46 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 15:59:46 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 15:59:46 CST 2022. by MyBatis Generator, do not modify.
    -->
    atom_order_id, sn
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.entity.Order2cAtomSnExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 15:59:46 CST 2022. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_2c_atom_sn
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.entity.Order2cAtomSnExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 15:59:46 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from order_2c_atom_sn
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.entity.Order2cAtomSn">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 15:59:46 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_atom_sn (atom_order_id, sn)
    values (#{atomOrderId,jdbcType=VARCHAR}, #{sn,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.entity.Order2cAtomSn">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 15:59:46 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_atom_sn
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="atomOrderId != null">
        atom_order_id,
      </if>
      <if test="sn != null">
        sn,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="atomOrderId != null">
        #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.entity.Order2cAtomSnExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 15:59:46 CST 2022. by MyBatis Generator, do not modify.
    -->
    select count(*) from order_2c_atom_sn
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 15:59:46 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_2c_atom_sn
    <set>
      <if test="record.atomOrderId != null">
        atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.sn != null">
        sn = #{record.sn,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 15:59:46 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_2c_atom_sn
    set atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      sn = #{record.sn,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 15:59:46 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_atom_sn
    (atom_order_id, sn)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.atomOrderId,jdbcType=VARCHAR}, #{item.sn,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Oct 19 15:59:46 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_atom_sn (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'atom_order_id'.toString() == column.value">
          #{item.atomOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'sn'.toString() == column.value">
          #{item.sn,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>