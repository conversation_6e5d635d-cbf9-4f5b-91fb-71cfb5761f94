<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.mapper.OrderDictInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.entity.OrderDictInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="associated_order" jdbcType="VARCHAR" property="associatedOrder" />
    <result column="business_order_status" jdbcType="INTEGER" property="businessOrderStatus" />
    <result column="return_order_status" jdbcType="INTEGER" property="returnOrderStatus" />
    <result column="order_status_time" jdbcType="TIMESTAMP" property="orderStatusTime" />
    <result column="refund_type" jdbcType="INTEGER" property="refundType" />
    <result column="refund_reason" jdbcType="INTEGER" property="refundReason" />
    <result column="refuse_reason" jdbcType="INTEGER" property="refuseReason" />
    <result column="orders_logis_info" jdbcType="VARCHAR" property="ordersLogisInfo" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.chinamobile.iot.sc.entity.OrderDictInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    <result column="audit_result_reason" jdbcType="LONGVARCHAR" property="auditResultReason" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    id, order_type, order_id, associated_order, business_order_status, return_order_status, 
    order_status_time, refund_type, refund_reason, refuse_reason, orders_logis_info
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    audit_result_reason
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.chinamobile.iot.sc.entity.OrderDictInfoExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from order_dict_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.entity.OrderDictInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_dict_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from order_dict_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from order_dict_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.entity.OrderDictInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from order_dict_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.entity.OrderDictInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_dict_info (id, order_type, order_id, 
      associated_order, business_order_status, return_order_status, 
      order_status_time, refund_type, refund_reason, 
      refuse_reason, orders_logis_info, audit_result_reason
      )
    values (#{id,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, 
      #{associatedOrder,jdbcType=VARCHAR}, #{businessOrderStatus,jdbcType=INTEGER}, #{returnOrderStatus,jdbcType=INTEGER}, 
      #{orderStatusTime,jdbcType=TIMESTAMP}, #{refundType,jdbcType=INTEGER}, #{refundReason,jdbcType=INTEGER}, 
      #{refuseReason,jdbcType=INTEGER}, #{ordersLogisInfo,jdbcType=VARCHAR}, #{auditResultReason,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.entity.OrderDictInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_dict_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="associatedOrder != null">
        associated_order,
      </if>
      <if test="businessOrderStatus != null">
        business_order_status,
      </if>
      <if test="returnOrderStatus != null">
        return_order_status,
      </if>
      <if test="orderStatusTime != null">
        order_status_time,
      </if>
      <if test="refundType != null">
        refund_type,
      </if>
      <if test="refundReason != null">
        refund_reason,
      </if>
      <if test="refuseReason != null">
        refuse_reason,
      </if>
      <if test="ordersLogisInfo != null">
        orders_logis_info,
      </if>
      <if test="auditResultReason != null">
        audit_result_reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="associatedOrder != null">
        #{associatedOrder,jdbcType=VARCHAR},
      </if>
      <if test="businessOrderStatus != null">
        #{businessOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="returnOrderStatus != null">
        #{returnOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="orderStatusTime != null">
        #{orderStatusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundType != null">
        #{refundType,jdbcType=INTEGER},
      </if>
      <if test="refundReason != null">
        #{refundReason,jdbcType=INTEGER},
      </if>
      <if test="refuseReason != null">
        #{refuseReason,jdbcType=INTEGER},
      </if>
      <if test="ordersLogisInfo != null">
        #{ordersLogisInfo,jdbcType=VARCHAR},
      </if>
      <if test="auditResultReason != null">
        #{auditResultReason,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.entity.OrderDictInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    select count(*) from order_dict_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_dict_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.associatedOrder != null">
        associated_order = #{record.associatedOrder,jdbcType=VARCHAR},
      </if>
      <if test="record.businessOrderStatus != null">
        business_order_status = #{record.businessOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="record.returnOrderStatus != null">
        return_order_status = #{record.returnOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="record.orderStatusTime != null">
        order_status_time = #{record.orderStatusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.refundType != null">
        refund_type = #{record.refundType,jdbcType=INTEGER},
      </if>
      <if test="record.refundReason != null">
        refund_reason = #{record.refundReason,jdbcType=INTEGER},
      </if>
      <if test="record.refuseReason != null">
        refuse_reason = #{record.refuseReason,jdbcType=INTEGER},
      </if>
      <if test="record.ordersLogisInfo != null">
        orders_logis_info = #{record.ordersLogisInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.auditResultReason != null">
        audit_result_reason = #{record.auditResultReason,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_dict_info
    set id = #{record.id,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      associated_order = #{record.associatedOrder,jdbcType=VARCHAR},
      business_order_status = #{record.businessOrderStatus,jdbcType=INTEGER},
      return_order_status = #{record.returnOrderStatus,jdbcType=INTEGER},
      order_status_time = #{record.orderStatusTime,jdbcType=TIMESTAMP},
      refund_type = #{record.refundType,jdbcType=INTEGER},
      refund_reason = #{record.refundReason,jdbcType=INTEGER},
      refuse_reason = #{record.refuseReason,jdbcType=INTEGER},
      orders_logis_info = #{record.ordersLogisInfo,jdbcType=VARCHAR},
      audit_result_reason = #{record.auditResultReason,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_dict_info
    set id = #{record.id,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      associated_order = #{record.associatedOrder,jdbcType=VARCHAR},
      business_order_status = #{record.businessOrderStatus,jdbcType=INTEGER},
      return_order_status = #{record.returnOrderStatus,jdbcType=INTEGER},
      order_status_time = #{record.orderStatusTime,jdbcType=TIMESTAMP},
      refund_type = #{record.refundType,jdbcType=INTEGER},
      refund_reason = #{record.refundReason,jdbcType=INTEGER},
      refuse_reason = #{record.refuseReason,jdbcType=INTEGER},
      orders_logis_info = #{record.ordersLogisInfo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.entity.OrderDictInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_dict_info
    <set>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="associatedOrder != null">
        associated_order = #{associatedOrder,jdbcType=VARCHAR},
      </if>
      <if test="businessOrderStatus != null">
        business_order_status = #{businessOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="returnOrderStatus != null">
        return_order_status = #{returnOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="orderStatusTime != null">
        order_status_time = #{orderStatusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundType != null">
        refund_type = #{refundType,jdbcType=INTEGER},
      </if>
      <if test="refundReason != null">
        refund_reason = #{refundReason,jdbcType=INTEGER},
      </if>
      <if test="refuseReason != null">
        refuse_reason = #{refuseReason,jdbcType=INTEGER},
      </if>
      <if test="ordersLogisInfo != null">
        orders_logis_info = #{ordersLogisInfo,jdbcType=VARCHAR},
      </if>
      <if test="auditResultReason != null">
        audit_result_reason = #{auditResultReason,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.chinamobile.iot.sc.entity.OrderDictInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_dict_info
    set order_type = #{orderType,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      associated_order = #{associatedOrder,jdbcType=VARCHAR},
      business_order_status = #{businessOrderStatus,jdbcType=INTEGER},
      return_order_status = #{returnOrderStatus,jdbcType=INTEGER},
      order_status_time = #{orderStatusTime,jdbcType=TIMESTAMP},
      refund_type = #{refundType,jdbcType=INTEGER},
      refund_reason = #{refundReason,jdbcType=INTEGER},
      refuse_reason = #{refuseReason,jdbcType=INTEGER},
      orders_logis_info = #{ordersLogisInfo,jdbcType=VARCHAR},
      audit_result_reason = #{auditResultReason,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.entity.OrderDictInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_dict_info
    set order_type = #{orderType,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      associated_order = #{associatedOrder,jdbcType=VARCHAR},
      business_order_status = #{businessOrderStatus,jdbcType=INTEGER},
      return_order_status = #{returnOrderStatus,jdbcType=INTEGER},
      order_status_time = #{orderStatusTime,jdbcType=TIMESTAMP},
      refund_type = #{refundType,jdbcType=INTEGER},
      refund_reason = #{refundReason,jdbcType=INTEGER},
      refuse_reason = #{refuseReason,jdbcType=INTEGER},
      orders_logis_info = #{ordersLogisInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_dict_info
    (id, order_type, order_id, associated_order, business_order_status, return_order_status, 
      order_status_time, refund_type, refund_reason, refuse_reason, orders_logis_info, 
      audit_result_reason)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.orderType,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, 
        #{item.associatedOrder,jdbcType=VARCHAR}, #{item.businessOrderStatus,jdbcType=INTEGER}, 
        #{item.returnOrderStatus,jdbcType=INTEGER}, #{item.orderStatusTime,jdbcType=TIMESTAMP}, 
        #{item.refundType,jdbcType=INTEGER}, #{item.refundReason,jdbcType=INTEGER}, #{item.refuseReason,jdbcType=INTEGER}, 
        #{item.ordersLogisInfo,jdbcType=VARCHAR}, #{item.auditResultReason,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Nov 18 11:20:12 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_dict_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'order_type'.toString() == column.value">
          #{item.orderType,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'associated_order'.toString() == column.value">
          #{item.associatedOrder,jdbcType=VARCHAR}
        </if>
        <if test="'business_order_status'.toString() == column.value">
          #{item.businessOrderStatus,jdbcType=INTEGER}
        </if>
        <if test="'return_order_status'.toString() == column.value">
          #{item.returnOrderStatus,jdbcType=INTEGER}
        </if>
        <if test="'order_status_time'.toString() == column.value">
          #{item.orderStatusTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'refund_type'.toString() == column.value">
          #{item.refundType,jdbcType=INTEGER}
        </if>
        <if test="'refund_reason'.toString() == column.value">
          #{item.refundReason,jdbcType=INTEGER}
        </if>
        <if test="'refuse_reason'.toString() == column.value">
          #{item.refuseReason,jdbcType=INTEGER}
        </if>
        <if test="'orders_logis_info'.toString() == column.value">
          #{item.ordersLogisInfo,jdbcType=VARCHAR}
        </if>
        <if test="'audit_result_reason'.toString() == column.value">
          #{item.auditResultReason,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>