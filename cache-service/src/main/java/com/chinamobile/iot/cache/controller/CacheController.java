package com.chinamobile.iot.cache.controller;

import com.chinamobile.iot.cache.service.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/5/11 9:58
 */
@RestController
@Slf4j
public class CacheController {

    @Resource
    private CacheService cacheService;

    @GetMapping("/spuQuantity")
    public String cahceSpuQuantity() {
        cacheService.cacheSpuQuantity(null);
        return "ok";
    }
}
