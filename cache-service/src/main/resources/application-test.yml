#mybatis-plus:
#    configuration:
#        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#spring:
#    cloud:
#        nacos:
#            discovery:
#                namespace: public
#                server-addr: 10.12.57.2:8848
#    datasource:
#        driver-class-name: com.mysql.cj.jdbc.Driver
#        hikari:
#            idle-timeout: 180000
#            max-lifetime: 30000
#            maximum-pool-size: 8
#            minimum-idle: 4
#        password: app_!QAZxsw2
#        url: ************************************************************************************************************************************
#        username: supply
#    jackson:
#        date-format: yyyy-MM-dd HH:mm:ss
#        time-zone: GMT+8
#    redis:
#        cluster:
#            nodes: 10.12.57.3:6379,10.12.57.4:6379,10.12.57.5:6379
#        password: hK@JY2YCamWPNvg
#        pool:
#            max-active: 5
#            max-idle: 5
#            max-wait: -1
#            min-idle: 0
#        timeout: 10000
#    kafka:
#        bootstrap-servers: **********:9092,**********:9092,***********:9092
#        consumer:
#            auto-offset-reset: latest
#            group-id: supplyChainCacheGroups
#            value-deserializer: org.apache.kafka.common.serialization.ByteArrayDeserializer
#        listener:
#            concurrency: 5
#        producer:
#            retries: 3
#            value-serializer: org.apache.kafka.common.serialization.ByteArraySerializer
#    servlet:
#        multipart:
#            max-file-size: 10MB
#            max-request-size: 10MB