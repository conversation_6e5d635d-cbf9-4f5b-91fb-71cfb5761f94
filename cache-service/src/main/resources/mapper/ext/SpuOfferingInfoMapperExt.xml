<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.cache.dao.ext.SpuOfferingInfoMapperExt">
    <select id="querySpuQuantity" resultType="com.chinamobile.iot.cache.pojo.mapper.SpuQuantityDO">
        SELECT
        orderTemp.spu_offering_code,
        SUM(orderTemp.sku_quantity) sku_quantity
        FROM
        (
        SELECT
        atom.spu_offering_code,
        atom.sku_quantity
        FROM
        order_2c_atom_info atom
        <where>
            atom.order_status in ( 2,7)
            <if test="spuCodeList != null and spuCodeList.size() != 0">
                and atom.spu_offering_code in
                <foreach collection="spuCodeList" item="spuCode" index="index" open="(" close=")" separator=",">
                    #{spuCode}
                </foreach>
            </if>
        </where>
        GROUP BY
        atom.order_id
        ) orderTemp
        GROUP BY
        orderTemp.spu_offering_code;
    </select>
</mapper>