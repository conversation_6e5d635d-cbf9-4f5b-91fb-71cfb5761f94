package com.chinamobile.iot.sc.service;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.pojo.dto.ContractMaterialDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/20 11:17
 * @description TODO
 */
@SpringBootTest
public class MaterialServiceTest {

    @Autowired
    private MaterialService materialService;

    @Test
    public void testQueryMaterial() {
        ContractMaterialDTO contractMaterialDTO = materialService.queryMaterial("10483837-0002");
        System.out.println(JSON.toJSON(contractMaterialDTO));
    }

    @Test
    public void testQueryMaterialInternal() {
        List<ContractMaterialDTO> contractMaterialDTOS = materialService.queryMaterialsInternal("10470325-0002","A04");
        System.out.println(JSON.toJSON(contractMaterialDTOS));
    }
}
