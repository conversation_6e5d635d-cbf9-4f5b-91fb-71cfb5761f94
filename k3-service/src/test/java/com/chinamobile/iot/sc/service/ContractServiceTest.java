package com.chinamobile.iot.sc.service;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.dto.ContractDTO;
import com.github.pagehelper.PageInfo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/13 09:09
 * @description TODO
 */
@SpringBootTest
public class ContractServiceTest {

    @Resource
    private ContractService contractService;

    @Test
    public void testGetContracts() {
        /*BaseAnswer<PageInfo<ContractDTO>> baseAnswer = contractService.getContracts(
            0, 2, null, null, "物联网", null, null, null
        );
        System.out.println(JSON.toJSONString(baseAnswer));*/
    }

}
