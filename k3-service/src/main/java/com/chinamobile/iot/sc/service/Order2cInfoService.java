package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.entity.Order2cInfo;
import com.chinamobile.iot.sc.pojo.entity.Order2cInfoExample;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/10
 * @description 订单信息service接口类
 */
public interface Order2cInfoService {

    /**
     * 根据需求更新订单信息
     * @param order2cInfo
     * @param example
     */
    void updateOrder2cInfoByExample(Order2cInfo order2cInfo,
                                    Order2cInfoExample example);

    /**
     * 获取订单信息
     * @param example
     * @return
     */
    List<Order2cInfo> listOrder2cInfo(Order2cInfoExample example);
}
