package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 线上结算省份管理表
 *
 * <AUTHOR>
public class OnlineSettlementProvinceManage implements Serializable {
    /**
     *
     * Corresponding to the database column supply_chain..online_settlement_province_manage.id
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    private Integer id;

    /**
     * 省份id
     *
     * Corresponding to the database column supply_chain..online_settlement_province_manage.be_id
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    private String beId;

    /**
     * 省份名称
     *
     * Corresponding to the database column supply_chain..online_settlement_province_manage.province_name
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    private String provinceName;

    /**
     * 线上结算是否启用0--未启用 1--启用
     *
     * Corresponding to the database column supply_chain..online_settlement_province_manage.settle_status
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    private Integer settleStatus;

    /**
     * 省份代码
     *
     * Corresponding to the database column supply_chain..online_settlement_province_manage.province_code
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    private String provinceCode;

    /**
     * 创建时间
     *
     * Corresponding to the database column supply_chain..online_settlement_province_manage.create_time
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     * Corresponding to the database column supply_chain..online_settlement_province_manage.update_time
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    private Date updateTime;

    /**
     * Corresponding to the database table supply_chain..online_settlement_province_manage
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..online_settlement_province_manage.id
     *
     * @return the value of supply_chain..online_settlement_province_manage.id
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public Integer getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public OnlineSettlementProvinceManage withId(Integer id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_province_manage.id
     *
     * @param id the value for supply_chain..online_settlement_province_manage.id
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_province_manage.be_id
     *
     * @return the value of supply_chain..online_settlement_province_manage.be_id
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public String getBeId() {
        return beId;
    }

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public OnlineSettlementProvinceManage withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_province_manage.be_id
     *
     * @param beId the value for supply_chain..online_settlement_province_manage.be_id
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public void setBeId(String beId) {
        this.beId = beId == null ? null : beId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_province_manage.province_name
     *
     * @return the value of supply_chain..online_settlement_province_manage.province_name
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public OnlineSettlementProvinceManage withProvinceName(String provinceName) {
        this.setProvinceName(provinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_province_manage.province_name
     *
     * @param provinceName the value for supply_chain..online_settlement_province_manage.province_name
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_province_manage.settle_status
     *
     * @return the value of supply_chain..online_settlement_province_manage.settle_status
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public Integer getSettleStatus() {
        return settleStatus;
    }

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public OnlineSettlementProvinceManage withSettleStatus(Integer settleStatus) {
        this.setSettleStatus(settleStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_province_manage.settle_status
     *
     * @param settleStatus the value for supply_chain..online_settlement_province_manage.settle_status
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public void setSettleStatus(Integer settleStatus) {
        this.settleStatus = settleStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_province_manage.province_code
     *
     * @return the value of supply_chain..online_settlement_province_manage.province_code
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public OnlineSettlementProvinceManage withProvinceCode(String provinceCode) {
        this.setProvinceCode(provinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_province_manage.province_code
     *
     * @param provinceCode the value for supply_chain..online_settlement_province_manage.province_code
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_province_manage.create_time
     *
     * @return the value of supply_chain..online_settlement_province_manage.create_time
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public OnlineSettlementProvinceManage withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_province_manage.create_time
     *
     * @param createTime the value for supply_chain..online_settlement_province_manage.create_time
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..online_settlement_province_manage.update_time
     *
     * @return the value of supply_chain..online_settlement_province_manage.update_time
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public OnlineSettlementProvinceManage withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..online_settlement_province_manage.update_time
     *
     * @param updateTime the value for supply_chain..online_settlement_province_manage.update_time
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", beId=").append(beId);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", settleStatus=").append(settleStatus);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OnlineSettlementProvinceManage other = (OnlineSettlementProvinceManage) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getSettleStatus() == null ? other.getSettleStatus() == null : this.getSettleStatus().equals(other.getSettleStatus()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getSettleStatus() == null) ? 0 : getSettleStatus().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..online_settlement_province_manage
     *
     * @mbg.generated Thu May 22 11:24:44 CST 2025
     */
    public enum Column {
        id("id", "id", "INTEGER", false),
        beId("be_id", "beId", "VARCHAR", false),
        provinceName("province_name", "provinceName", "VARCHAR", false),
        settleStatus("settle_status", "settleStatus", "INTEGER", false),
        provinceCode("province_code", "provinceCode", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         * Corresponding to the database table supply_chain..online_settlement_province_manage
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..online_settlement_province_manage
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..online_settlement_province_manage
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..online_settlement_province_manage
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..online_settlement_province_manage
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..online_settlement_province_manage
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu May 22 11:24:44 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}