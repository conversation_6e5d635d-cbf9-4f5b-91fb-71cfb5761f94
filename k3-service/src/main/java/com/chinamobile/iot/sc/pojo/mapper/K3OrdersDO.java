package com.chinamobile.iot.sc.pojo.mapper;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/17
 * @description k3月数据订单
 */
@Data
public class K3OrdersDO {

    /**
     * 订单号
     */
    @Excel(name = "订单号")
    private String orderId;

    /**
     * 下单时间
     */
    @Excel(name = "下单时间")
    private String orderTime;

    /**
     * 订单交易完成时间
     */
    @Excel(name = "订单交易完成时间")
    private String orderFinishTime;

    /**
     * 商品组/销售商品名称
     */
    @Excel(name = "商品组/销售商品名称")
    private String spuName;

    /**
     * 商品组/销售商品编码
     */
    @Excel(name = "商品组/销售商品编码")
    private String spuCode;

    /**
     * 商品类型
     */
    @Excel(name = "商品类型")
    private String spuType;

    /**
     * 商品规格名称
     */
    @Excel(name = "商品规格名称")
    private String skuName;

    /**
     * 商品规格编码
     */
    @Excel(name = "商品规格编码")
    private String skuCode;

    /**
     * 原子商品名称
     */
    @Excel(name = "原子商品名称")
    private String atomName;

    /**
     * 原子商品编码
     */
    @Excel(name = "原子商品编码")
    private String atomCode;

    /**
     * 物料编码
     */
    @Excel(name = "物料编码")
    private String materialNum;

    /**
     * 物料名称
     */
    @Excel(name = "物料名称")
    private String materialName;

    /**
     * 物料所属部门
     */
    @Excel(name = "物料所属部门")
    private String materialDept;

    /**
     * 原子结算单价(厘)
     */
    @Excel(name = "原子结算单价")
    private String unitPrice;

    /**
     * 原子订购数量
     */
    @Excel(name = "原子订购数量")
    private Long quatity;

    /**
     * 结算金额
     */
    @Excel(name = "结算金额")
    private String totalPrice;

    /**
     * 抵扣金额（厘）
     */
    @Excel(name = "抵扣金额")
    private String deductPrice;

    /**
     * 收货人电话
     */
    @Excel(name = "收货人电话")
    private String receiverPhone;

    /**
     * 订单收入所属省份名称
     */
    @Excel(name = "订单收入所属省份名称")
    private String orderProvince;

    /**
     * 订单收入所属地市名称
     */
    @Excel(name = "订单收入所属地市名称")
    private String orderCity;

    /**
     * 操作员编码
     */
    @Excel(name = "操作员编码")
    private String createOperCode;

    /**
     * 操作员工号
     */
    @Excel(name = "操作员工号")
    private String employeeNum;


    /**
     * 合同编号
     */
    @Excel(name = "合同编号")
    private String contractNum;

    /**
     * 合同名称
     */
    @Excel(name = "合同名称")
    private String contractName;

    /**
     * 合同所属部门
     */
    @Excel(name = "合同所属部门")
    private String contractDept;

    @Excel(name = "合同相对方归属省")
    private String buyerProvince;

    @Excel(name = "合同相对方归属地市")
    private String buyerCity;

    @Excel(name = "合同统计方式")
    private String contractSatisType;
}
