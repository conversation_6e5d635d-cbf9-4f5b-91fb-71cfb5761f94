
package com.chinamobile.iot.sc.service.soa.token;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>OUTPUTCOLLECTION_ITEM complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="OUTPUTCOLLECTION_ITEM"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="NEW_TOKEN" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="INVALID_PERIOD" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="OUTPUT_EXT" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OUTPUTCOLLECTION_ITEM", propOrder = {
    "newtoken",
    "invalidperiod",
    "outputext"
})
public class OUTPUTCOLLECTIONITEM {

    @XmlElement(name = "NEW_TOKEN", required = true, nillable = true)
    protected String newtoken;
    @XmlElement(name = "INVALID_PERIOD", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar invalidperiod;
    @XmlElement(name = "OUTPUT_EXT", required = true, nillable = true)
    protected String outputext;

    /**
     * ��ȡnewtoken���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNEWTOKEN() {
        return newtoken;
    }

    /**
     * ����newtoken���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNEWTOKEN(String value) {
        this.newtoken = value;
    }

    /**
     * ��ȡinvalidperiod���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getINVALIDPERIOD() {
        return invalidperiod;
    }

    /**
     * ����invalidperiod���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setINVALIDPERIOD(XMLGregorianCalendar value) {
        this.invalidperiod = value;
    }

    /**
     * ��ȡoutputext���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOUTPUTEXT() {
        return outputext;
    }

    /**
     * ����outputext���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOUTPUTEXT(String value) {
        this.outputext = value;
    }

}
