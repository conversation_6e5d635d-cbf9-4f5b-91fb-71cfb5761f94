package com.chinamobile.iot.sc.dao.ext;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.pojo.dto.*;
import com.chinamobile.iot.sc.pojo.param.HandleNotGenerateOnlineOsOrderParam;
import com.chinamobile.iot.sc.pojo.param.OnlineSettlementOsOrderParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/14
 * @description 线上结算管理商城订单mapper扩展类
 */
public interface OnlineSettlementOsOrderMapperExt {

    /**
     * 获取处理未线上结算订单的历史订单记录
     * @param handleNotGenerateOnlineOsOrderParam
     * @return
     */
    List<NotGenerateOnlineOsOrderDTO> listNotGenerateOnlineOsOrder(@Param(value = "handleNotGenerateOnlineOsOrderParam") HandleNotGenerateOnlineOsOrderParam handleNotGenerateOnlineOsOrderParam);

    /**
     * 获取线上结算管理订单
     * @param onlineSettlementOsOrderParam
     * @return
     */
    List<OnlineSettlementOsOrderDTO> listOnlineSettlementOsOrder(@Param(value = "onlineSettlementOsOrderParam") OnlineSettlementOsOrderParam onlineSettlementOsOrderParam);

    /**
     * 分页获取线上结算管理订单
     * @param onlineSettlementOsOrderParam
     * @return
     */
    List<OnlineSettlementOsOrderDTO> listOnlineSettlementOsOrder(@Param("page") Page page,
                                                                 @Param(value = "onlineSettlementOsOrderParam") OnlineSettlementOsOrderParam onlineSettlementOsOrderParam);

    /**
     * 通过订单信息获取能够生成采购订单订单
     * @param orderList
     * @return
     */
    List<OsOrderCanToPurchaseOrderDTO> listOsOrderCanToPurchaseOrder(@Param(value = "orderList")List orderList);

    /**
     * 获取生成采购订单的数据
     * @param orderList
     * @return
     */
    List<ContractPurchaseOrderDTO> listContractPurchaseOrder(@Param(value = "orderList")List orderList);

    /**
     * 获取订单和合同关系列表
     * @param orderList
     * @return
     */
    List<OrderAndContractRelationDTO> listOrderAndContractRelation(@Param(value = "orderList")List orderList);

    /**
     * 更新线上结算采购订单id为空
     * @param onlineSettlementPurchaseOrderId
     */
    void cancelPurchaseOrderToOsOrder(@Param(value = "onlineSettlementPurchaseOrderId") String onlineSettlementPurchaseOrderId);

    /**
     * 删除线上结算采购订单id为空
     * @param onlineSettlementPurchaseOrderId
     */
    void deletePurchaseOrderToOsOrder(@Param(value = "onlineSettlementPurchaseOrderId") String onlineSettlementPurchaseOrderId);

    List<DraftSubOrderInfoDTO> listDraftOsOrders(@Param(value = "purchaseOrderId")String id);

    List<DraftBaseInfoDTO> listDraftBaseInfo(@Param(value = "purchaseOrderId")String id, @Param(value = "status")String status);

}
