package com.chinamobile.iot.sc.pojo.query;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/17
 * @description k3月数据合同数据库查询
 */
@Data
public class K3syncStatisQuery {
    /**
     * 销售时间
     */
    private String saleDate;

    private Integer productType;

    /**
     * 订单收入省份名称
     */
    private String orderProvince;

    /**
     * 订单收入地市名称
     */
    private String orderCity;

    /**
     * 合同编号
     */
    private String contractNum;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同所属部门
     */
    private String contractDept;

    /**
     * 同步状态 0-未同步 1-成功 2-失败
     */
    private String k3SyncStatus;

    /**
     * 提交状态 0-未同步 1-成功 2-失败
     */
    private String k3CommitStatus;

    /**
     * K3返回订单编号
     */
    private String k3RetNum;

    /**
     * 省采同步状态 0：未同步； 1：成功；2：失败
     */
    private String proSyncStatus;

    /**
     * 省采返回编号
     */
    private String proRetNum;
}
