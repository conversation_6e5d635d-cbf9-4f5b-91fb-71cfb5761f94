package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.K3syncStatisProvince;
import com.chinamobile.iot.sc.pojo.entity.K3syncStatisProvinceExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface K3syncStatisProvinceMapper {
    long countByExample(K3syncStatisProvinceExample example);

    int deleteByExample(K3syncStatisProvinceExample example);

    int deleteByPrimaryKey(String id);

    int insert(K3syncStatisProvince record);

    int insertSelective(K3syncStatisProvince record);

    List<K3syncStatisProvince> selectByExample(K3syncStatisProvinceExample example);

    K3syncStatisProvince selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") K3syncStatisProvince record, @Param("example") K3syncStatisProvinceExample example);

    int updateByExample(@Param("record") K3syncStatisProvince record, @Param("example") K3syncStatisProvinceExample example);

    int updateByPrimaryKeySelective(K3syncStatisProvince record);

    int updateByPrimaryKey(K3syncStatisProvince record);

    int batchInsert(@Param("list") List<K3syncStatisProvince> list);

    int batchInsertSelective(@Param("list") List<K3syncStatisProvince> list, @Param("selective") K3syncStatisProvince.Column ... selective);
}