package com.chinamobile.iot.sc.pojo.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/18
 * @description 线上结算采购订单实体类
 */
@Data
public class OnlineSettlementPurchaseOrderDTO {

    private String id;

    private String sourceType;

    private String scmOrderNum;

    /**
     * 正式的采购订单编号
     */
    private String poOrderNum;

    private String contractNumber;

    private String contractName;

    private String contractType;

    private String contractCode;

    private String provinceName;

    private String cityName;

    private Long settlePrice;

    private BigDecimal taxInclusiveTotalSettlePrice;

    private String settleStatus;

    private Date createTime;
}
