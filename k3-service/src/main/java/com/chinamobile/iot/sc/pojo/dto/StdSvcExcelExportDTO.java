package com.chinamobile.iot.sc.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 标准服务导出
 */
@Data
public class StdSvcExcelExportDTO {

    /**
     * 商品类型
     */
    @Excel(name = "商品类型")
    private String spuOfferingClass;

    /**
     * 商品名称（规格）
     */
    @Excel(name = "商品名称（规格）")
    private String skuOfferingName;
    /**
     * 原子商品名称
     */
    @Excel(name = "原子商品名称")
    private String atomOfferingName;
    /**
     * 原子商品类型
     */
    @Excel(name = "原子商品类型")
    private String atomOfferingClass;
    /**
     * 型号
     */
    @Excel(name = "型号")
    private String model;
    /**
     * 颜色
     */
    @Excel(name = "颜色")
    private String color;

    /**
     * 标准服务配置状态 0--未配置  1--已配置
     */
    @Excel(name = "配置状态")
    private Integer configStatus;

    /**
     * 所属合作伙伴
     */
    @Excel(name = "所属合作伙伴")
    private String cooperatorName;

    /**
     * 合作伙伴名
     */
    @Excel(name = "合作伙伴联系人")
    private String partnerName;

    /**
     * 商品组/销售商品编码
     */
    @Excel(name = "商品组/销售商品编码")
    private String  spuOfferingCode;

    /**
     *商品组/销售商品名称
     */
    @Excel(name = "商品组/销售商品名称")
    private String  spuOfferingName;

    /**
     *商品编码（规格）
     */
    @Excel(name = "商品规格编码")
    private String  skuOfferingCode;

    @Excel(name = "标准服务名称")
    private String stdSvcName;

    @Excel(name = "产品部门")
    private String productDept;

    @Excel(name = "实质性产品名称")
    private String realProductName;

    @Excel(name = "产品属性")
    private String productProperty;

    /**
     * 原子商品结算单价
     */
    @Excel(name = "原子商品结算单价(元)")
    private BigDecimal atomPrice;

    /**
     * 原子商品销售目录价
     */
    @Excel(name = "原子商品销售目录价(元)")
    private BigDecimal atomSalePrice;

}
