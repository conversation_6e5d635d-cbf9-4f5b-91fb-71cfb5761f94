package com.chinamobile.iot.sc.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> x<PERSON>maoh<PERSON>
 * @date : 2024/8/29 17:05
 * @description: 合同导入成功DTO
 **/
@Data
public class ProvinceContractImportNewDTO {

    /**
     * 省份编码
     *
     */
    private String provinceCode = "";

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 地市编码
     *
     */
    private String cityCode = "";

    /**
     * 地市名称
     */
    private String cityName;

    /**
     * 合同类型
     */
    private String contractType;

    /**
     * 物联网合同编码
     */
    private String internetContractCode;

    /**
     * 申请部门代码
     *
     */
    private String deptCode;

    /**
     * 申请部门名称
     *
     */
    private String deptName;

    /**
     * 申请人ID
     *
     */
    private String createdId;

    /**
     * 申请人姓名
     *
     */
    private String createdName;

    /**
     * 物资类别 字典值： E：工程物资 C：业务用品 P：备品备件 O：杂品  "当“开支类型”为“资本类”时
     ，类别必需为“E-工程物资”；当
     “开支类型”为“成本类”时，可
     以为“C-业务用品，P-备品备件
     ，O-杂品” 。产品库上架的物资
     类别需与订单上的物资类别一致
     当为零购订单时不校验产品库的物资类别"
     */
    private String mtlTypeCode;

    /**
     * 物资小类
     */
    private String smallItem;

    /**
     * 合同编号
     *
     */
    private String contractCode;

    /**
     * 供应商编码  固定值 MDM_106201379
     */
    private String vendorCode;

    /**
     * 需求类型  字典值： 1：个人市场业务需求 2：家庭市场业务需求 3：政企市场业务需求 4：新兴市场业务需求 5：支撑多种业务的产品类采购 6：其他业务需求
     */
    private String reqType;

    /**
     * 二级需求类型  当“需求类型”为“政企市场业务 求”时必填 字典值： 1：ICT业务需求 2：其他业务需
     */
    private String reqSecondType;

    /**
     * 接收人员工编号  新员工编号，11位
     */
    private String rcvUserNum;

    /**
     * 接收人姓名
     *
     */
    private String rcvUser;

    /**
     * 接收人电话
     *
     */
    private String rcvContactPhone;

    /**
     *接收人地址
     */
    private String rcvSiteAddress;

    /**
     * MIS主体
     */
    private String misBody;

    /**
     * OU标识
     */
    private String orgId;

    /**
     * 字典值： Capex：资本类 Opex：成本类
     */
    private String expType;

    /**
     * 报账模式 "字典值： 1：ERP采购订单 2：费用池订单
     */
    private String reimbursementMode;

    /**
     * 要求到货时间模式 "字典值：
     DATE：时间点
     RANGE：时间段
     EFFECT：订单生效后X自然日默认
     “时间点”
     当为零购订单时，仅可传
     “DATE”和”RANGE”"
     */
    private String arrivalTimeModel;

    /**
     * 要求到货天数
     */
    private Integer arrivalDays;

    /**
     * 发运组织代码
     */
    private String organizationCode;

    /**
     * INVENTORY：库存 EXPENSE：费用
     */
    private String itemType;

    /**
     * 预算组合ID "当“开支类型”为“成本类”时必
     填"
     */
    private String budgetId;

    /**
     * 预算年份 "当“开支类型”为“成本类”时必
     填"
     */
    private String budgetYear;

    /**
     * 业务活动编码  "当“开支类型”为“成本类”时必填默认预算组织中的业务活动"
     */
    private String activityCode;

    /**
     * 成本中心  "仅适用“报账模式”为“ERP采购订单”当“开支类型”为“成本类”时并且“分配类型”为“费用”时必填"
     */
    private String costCenter;

    /**
     * 费用科目  "仅适用“报账模式”为“ERP采购订单”当“开支类型”为“成本类”时并且“分配类型”为“费用”时必填"
     */
    private String expenseAccount;

    /**
     * 子目"仅适用“报账模式”为“ERP采购
     订单”
     当“开支类型”为“成本类
     ”时并且“分配类型”为“费用
     ”时必填"
     */
    private String costSubject;

    /**
     * 管会业务活动 1、当“开支类型”为“成本类 ”时并且“分配类型”为“费用 ”时必填
     */
    private String manageActivity;

    /**
     * 管会市场维度 1、当“开支类型”为“成本类 ”时并且“分配类型”为“费用 ”时必填
     */
    private String manageMarket;

    /**
     * 管会产品维度 1、当“开支类型”为“成本类 ”时并且“分配类型”为“费用 ”时必填
     */
    private String manageProduct;


    private String failedReason;
}
