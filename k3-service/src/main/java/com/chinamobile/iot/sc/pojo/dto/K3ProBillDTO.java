package com.chinamobile.iot.sc.pojo.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/28
 * @description k3省侧对账单导出
 */
@Data
public class K3ProBillDTO {

    /**
     * 编号
     */
    private String billNum;

    /**
     * 税率
     */
    private String taxRate;

    /**
     * 价格单位
     */
    private String priceUnit;

    /**
     * 销售（采购）主体名称
     */
    private String saleMainName;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 对账周期
     */
    private String reconciliationCycle;

    /**
     * 采购合同名称
     */
    private String purchaseContractName;

    /**
     * 采购合同编号
     */
    private String purchaseContractNum;

    /**
     * 采购合同金额
     */
    private BigDecimal purchaseContractPrice;

    /**
     * 采购合同有效期
     */
    private String purchaseContractValidCycle;

    /**
     * 销售合同编号
     */
    private String saleContractNum;

    /**
     * 订单金额
     */
    private BigDecimal orderTotalPrice;

    /**
     * 采购订单有效期
     */
    private String purchaseOrderValidCycle;

    /**
     * k3省侧对账单订单信息列表
     */
    private List<K3ProBillOrderDTO> k3ProBillOrderDTOList;

}
