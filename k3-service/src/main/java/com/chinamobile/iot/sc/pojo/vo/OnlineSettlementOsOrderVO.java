package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/15
 * @description 线上结算管理订单展示列表实体类
 */
@Data
public class OnlineSettlementOsOrderVO {

    private String id;

    /**
     * 下单时间
     */
    private String orderCreateTimeStr;

    /**
     * 交易完成时间
     */
    private String orderSuccessTimeStr;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 商品名称（规格）
     */
    private String skuOfferingName;

    /**
     * 订购数量（规格）
     */
    private Integer skuQuantity;

    /**
     * 商品类型
     */
    private String spuOfferingClassName;

    /**
     * 订单收入归属省份名称
     */
    private String provinceName;

    /**
     * 订单收入归属地市名称
     */
    private String cityName;

    /**
     * 订单金额
     */
    private BigDecimal totalPriceDec;

    /**
     * 订单类型
     */
    private String orderType;

    private String orderTypeDesc;

}
