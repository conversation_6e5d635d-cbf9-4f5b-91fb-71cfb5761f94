package com.chinamobile.iot.sc.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.enums.log.*;
import com.chinamobile.iot.sc.excel.StandardServiceImportExcel;
import com.chinamobile.iot.sc.excel.StandardServiceImportExcelListener;
import com.chinamobile.iot.sc.exception.StatusContant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.dto.*;
import com.chinamobile.iot.sc.pojo.entity.AtomStdService;
import com.chinamobile.iot.sc.pojo.entity.Department;
import com.chinamobile.iot.sc.pojo.entity.ProductProperty;
import com.chinamobile.iot.sc.pojo.entity.StandardService;
import com.chinamobile.iot.sc.pojo.param.AddStandardServiceParam;
import com.chinamobile.iot.sc.pojo.param.ConfigStdServiceParam;
import com.chinamobile.iot.sc.pojo.param.EditStandardServiceParam;
import com.chinamobile.iot.sc.pojo.param.QueryStandardServiceParam;
import com.chinamobile.iot.sc.pojo.vo.StandardServiceVO;
import com.chinamobile.iot.sc.service.*;
import com.chinamobile.iot.sc.util.ExcelUtils;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import com.chinamobile.iot.sc.utils.K3LogUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.TextUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/13 18:12
 * @description 标准服务Service接口实现类
 */
@Service
@Slf4j
public class StandardServiceServiceImpl implements StandardServiceService {

    @Resource
    private StandardServiceMapperExt standardServiceMapperExt;

    @Resource
    private StandardServiceMapper standardServiceMapper;

    @Autowired
    private AtomStdServiceService atomStdServiceService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private ProductService productService;

    @Resource
    private LogService logService;

    @Resource
    private ProductHandlerMapper productHandlerMapper;

    @Resource
    private ProductPropertyMapper productPropertyMapper;

    @Resource
    private DepartmentMapper departmentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addStandardService(AddStandardServiceParam param) {
        StandardService standardService = new StandardService();
        BeanUtils.copyProperties(param, standardService);
        standardService.setId(BaseServiceUtils.getId());
        Integer departmentId = param.getDepartmentId();
        String productPropertyId = param.getProductPropertyId();
        String remark1 = param.getRemark1();
        String remark2 = param.getRemark2();

        Department department = departmentMapper.selectById(departmentId);
        if (department == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "部门id不存在");
        }
        ProductProperty productProperty = productPropertyMapper.selectById(productPropertyId);
        if (productProperty == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "产品属性不存在");
        }

        String realProductName = param.getRealProductName();
        String name = param.getName();
        List<StandardService> standardServiceDTOS = standardServiceMapperExt.selectList(new QueryWrapper<StandardService>().lambda().eq(StandardService::getRealProductName, realProductName).eq(StandardService::getName, name));
        //根据产品要求：标准服务名称可以重复，但是同一个标准服务下  实质产品名称不能重复
        if (!standardServiceDTOS.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "标准产品[" + name + "]对应的实质产品名称[" + realProductName + "]已存在");
        }
        standardService.setProductDepartmentId(departmentId);
        Date now = new Date();
        standardService.setCreateTime(now);
        standardService.setUpdateTime(now);
        standardServiceMapperExt.insert(standardService);

        StringBuffer content = new StringBuffer();
        content.append("【新建标准服务】\n:");
        content.append("标准服务名称" + name
                + ",实质产品名称"+ (StringUtils.isEmpty(realProductName)?"--":realProductName)
                + ",产品部门" + department.getShortName()
                + ",产品属性" + productProperty.getName()
                + ",备注1" + (StringUtils.isEmpty(remark1)?"--":remark1)
                + ",备注2" + (StringUtils.isEmpty(remark2)?"--":remark2)
                +"\n");
        logService.recordOperateLog(ModuleEnum.OPERATION_KANBAN.code,
                OperationKanbanOperateEnum.STANDARD_SERVICE_CONFIG.code,
                content.toString(), LogResultEnum.LOG_SUCESS.code, null);

        return standardService.getId();
    }

    @Override
    public PageData<StandardServiceVO> queryStandardServices(QueryStandardServiceParam param) {
        if (null == param.getPage()) {
            param.setPage(1);
        }
        if (null == param.getNum()) {
            param.setNum(10);
        }
        // 商品名称搜索支持反斜杠适配
        if(param.getName() != null){
            param.setName(param.getName().replaceAll("\\\\","\\\\\\\\"));
        }
        if(param.getRealProductName() != null){
            param.setRealProductName(param.getRealProductName().replaceAll("\\\\","\\\\\\\\"));
        }
        PageHelper.startPage(param.getPage(), param.getNum());
        List<StandardServiceVO> standardServiceVOS = standardServiceMapperExt.queryStandardServices(param);
        PageInfo<StandardServiceVO> pageInfo = new PageInfo<>(standardServiceVOS);
        PageData<StandardServiceVO> pageData = new PageData<>();
        pageData.setPage(pageInfo.getPageNum());
        pageData.setData(pageInfo.getList());
        pageData.setCount(pageInfo.getTotal());
        return pageData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateStandardService(EditStandardServiceParam param,Boolean isLog) {
        String id = param.getId();
        StandardService standardService = standardServiceMapperExt.selectById(id);
        if (null == standardService) {
            throw new BusinessException(StatusContant.STANDARD_SERVICE_NOT_EXIST);
        }
        BeanUtils.copyProperties(param, standardService);
        Integer departmentId = param.getDepartmentId();
        String productPropertyId = param.getProductPropertyId();

        Department department = departmentMapper.selectById(departmentId);
        if (department == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "部门id不存在");
        }

        ProductProperty productProperty = productPropertyMapper.selectById(productPropertyId);
        if (productProperty == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "产品属性ID不存在");
        }

        standardService.setProductDepartmentId(departmentId);
        String realProductName = param.getRealProductName();
        String name = param.getName();
        List<StandardService> standardServiceDTOS = standardServiceMapperExt.selectList(new QueryWrapper<StandardService>().lambda().eq(StandardService::getRealProductName, realProductName).eq(StandardService::getName, name).ne(StandardService::getId, id));
        //根据产品要求：标准服务名称可以重复，但是同一个标准服务下  实质产品名称不能重复
        if (!standardServiceDTOS.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "标准产品[" + name + "]对应的实质产品名称[" + realProductName + "]已存在");
        }
        Date now = new Date();
        standardService.setUpdateTime(now);
        standardServiceMapperExt.updateById(standardService);

        String remark1 = standardService.getRemark1();
        String remark2 = standardService.getRemark2();

        Integer oldProductDepartmentId = standardService.getProductDepartmentId();
        Department oldDepartment = departmentMapper.selectById(oldProductDepartmentId);

        String oldProductPropertyId = standardService.getProductPropertyId();
        ProductProperty oldProductProperty = productPropertyMapper.selectById(oldProductPropertyId);

        StringBuffer content = new StringBuffer();
        content.append("【修改标准服务】\n:");
        StringBuffer contentBody = new StringBuffer();
        contentBody.append("标准服务编码").append(standardService.getId());
        contentBody.append("标准服务名称由" +standardService.getName()+"修改为"+ name
                + ",实质产品名称由 "+standardService.getRealProductName()+"修改为"+ (StringUtils.isEmpty(realProductName)?"--":realProductName)
                + ",产品部门由"+oldDepartment.getShortName()+"修改为" + department.getShortName()
                + ",产品属性由" + oldProductProperty.getName()+"修改为" + productProperty.getName()
                +"\n");
        content.append(contentBody.toString());
        if (isLog){
            logService.recordOperateLog(ModuleEnum.OPERATION_KANBAN.code,
                    OperationKanbanOperateEnum.STANDARD_SERVICE_CONFIG.code,
                    content.toString(), LogResultEnum.LOG_SUCESS.code, null);
        }
        return contentBody.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStandardService(String id) {
        List<AtomStdService> atomStdServices = atomStdServiceService.selectByServiceId(id);
        if (!CollectionUtils.isEmpty(atomStdServices)) {
            ProductInfoDTO productInfoDTO = productService.selectById(atomStdServices.get(0).getAtomId());
            String hint = "[" + productInfoDTO.getAtomOfferingName() + "]" + (atomStdServices.size() == 1 ? "" : "等多个");
            throw new BusinessException(StatusContant.STD_SVC_CANNOT_DELETE.getStateCode(), hint + "商品已配置该标准服务，请重新配置后再删除");
        }
        standardServiceMapperExt.deleteById(id);
        atomStdServiceService.deleteByServiceId(id);

        StringBuffer content = new StringBuffer();
        content.append("【删除标准服务】\n:");
        content.append("标准服务编码").append(id);
        logService.recordOperateLog(ModuleEnum.OPERATION_KANBAN.code,
                OperationKanbanOperateEnum.STANDARD_SERVICE_CONFIG.code,
                content.toString(), LogResultEnum.LOG_SUCESS.code, null);
    }

    @Override
    public String configStdService(ConfigStdServiceParam param) {
        String result = "";
        if (TextUtils.isBlank(param.getId())) {
            result = atomStdServiceService.insert(param);
        } else {
            atomStdServiceService.update(param);
        }
        //记录日志
        ProductInfoDTO productInfoDTO = productHandlerMapper.selectById(param.getAtomId());
        StandardService standardService = standardServiceMapperExt.selectById(param.getStdServiceId());
        DepartmentDTO departmentDTO = departmentService.findDepartmentById(standardService.getProductDepartmentId());
        ProductProperty productProperty = productPropertyMapper.selectById(standardService.getProductPropertyId());
        logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code, GoodsManageOperateEnum.STANDARD_SERVICE_CONFIG.code,
                K3LogUtil.configStandardServiceContentFromParam(productInfoDTO, standardService, departmentDTO, productProperty));
        return result;
    }

    @Override
    public void removeConfig(String atomId) {
        atomStdServiceService.deleteByAtomId(atomId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer importStandardService(MultipartFile file) {
        log.info("导入文件开始，文件名称:{}", file.getOriginalFilename());
        String filename = file.getOriginalFilename();
        if (!filename.endsWith("xlsx") && !filename.endsWith("xls")) {
            throw new BusinessException(BaseErrorConstant.FILE_FORMAT_ERROR);
        }
        List<Object> list = null;
        try {
            list = EasyExcel.read(file.getInputStream(), ImportStandardServiceDTO.class, null)
                    .sheet(0).headRowNumber(1).doReadSync();
        } catch (IOException e) {
            log.error("读取文件发生错误", e);
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "文件解析异常,请检查内容和格式");
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "请上传有效数据");
        }
        List<StandardService> standardServiceDTOList = new ArrayList<>();
        List<ImportStandardServiceDTO> preparedDtoList = new ArrayList();
        Date now = new Date();
        StringBuffer content = new StringBuffer();
        content.append("【新建标准服务】\n:");
        for (Object o : list) {
            ImportStandardServiceDTO dto = (ImportStandardServiceDTO) o;
            String standardServiceName = dto.getStandardServiceName();
            if (StringUtils.isEmpty(standardServiceName)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "标准服务名称不能为空");
            }
            String departmentName = dto.getDepartmentName();
            if (StringUtils.isEmpty(departmentName)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "部门名称不能为空");
            }
            String propertyName = dto.getPropertyName();
            if (StringUtils.isEmpty(propertyName)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "属性名称不能为空");
            }
            String realProductName = dto.getRealProductName();
            if (StringUtils.isEmpty(realProductName)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "实质产品名称不能为空");
            }
            if (preparedDtoList.contains(dto)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "标准产品[" + standardServiceName + "]对应的实质产品名称[" + realProductName + "]有重复");
            }
            List<StandardService> standardServiceList = standardServiceMapperExt.selectList(new QueryWrapper<StandardService>().lambda().eq(StandardService::getRealProductName, realProductName).eq(StandardService::getName, standardServiceName));
            //根据产品要求：标准服务名称可以重复，但是同一个标准服务下  实质产品名称不能重复
            if (!standardServiceList.isEmpty()) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "标准产品[" + standardServiceName + "]对应的实质产品名称[" + realProductName + "]已存在");
            }
            Department department = departmentMapper.selectOne(new QueryWrapper<Department>().lambda().eq(Department::getShortName, departmentName));
            if (department == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "部门名称[" + departmentName + "]不存在");
            }
            ProductProperty productProperty = productPropertyMapper.selectOne(new QueryWrapper<ProductProperty>().lambda().eq(ProductProperty::getName, propertyName));
            if (productProperty == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "产品属性[" + propertyName + "]不存在");
            }
            StandardService standardService = new StandardService();
            standardService.setId(BaseServiceUtils.getId());
            standardService.setName(standardServiceName);
            standardService.setRealProductName(realProductName);
            standardService.setProductDepartmentId(department.getId());
            standardService.setProductPropertyId(productProperty.getId());
            standardService.setCreateTime(now);
            standardService.setUpdateTime(now);
            String remark1 = dto.getRemark1();
            standardService.setRemark1(remark1);
            String remark2 = dto.getRemark2();
            standardService.setRemark2(remark2);
            standardServiceDTOList.add(standardService);
            preparedDtoList.add(dto);


            content.append("标准服务名称:" + standardServiceName
                    + ",实质产品名称 "+ (StringUtils.isEmpty(realProductName)?"--":realProductName)
                    + ",产品部门" + department.getShortName()
                    + ",产品属性" + productProperty.getName()
                    + ",备注1" + (StringUtils.isEmpty(remark1)?"--":remark1)
                    + ",备注2" + (StringUtils.isEmpty(remark2)?"--":remark2)
                    +"\n");
        }
        if (!standardServiceDTOList.isEmpty()) {
            standardServiceMapper.batchInsert(standardServiceDTOList);
        }

        logService.recordOperateLog(ModuleEnum.OPERATION_KANBAN.code,
                OperationKanbanOperateEnum.STANDARD_SERVICE_CONFIG.code,
                content.toString(), LogResultEnum.LOG_SUCESS.code, null);
        return BaseAnswer.success(null);
    }

    @Override
    public void exportStandardService(QueryStandardServiceParam param) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        try {
            List<StandardServiceVO> standardServiceVOS = standardServiceMapperExt.queryStandardServices(param);
            if (standardServiceVOS.isEmpty()) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "无数据");
            }
            List<ExportStandardServiceDTO> exportList = standardServiceVOS.stream().map(s -> {
                ExportStandardServiceDTO dto = new ExportStandardServiceDTO();
                dto.setStandardServiceId(s.getId());
                dto.setStandardServiceName(s.getName());
                dto.setDepartmentName(s.getDepartment().getShortName());
                dto.setRealProductName(s.getRealProductName());
                dto.setPropertyName(s.getProductProperty().getName());
                dto.setRemark1(s.getRemark1());
                dto.setRemark2(s.getRemark2());
                return dto;
            }).collect(Collectors.toList());
            Workbook workbook = ExcelUtils.exportStandardServiceSimpleExcelFast("标准服务", ExportStandardServiceDTO.class, exportList);
            OutputStream outputStream = null;
            try {
                outputStream = response.getOutputStream();
                workbook.write(outputStream);
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                if (outputStream != null) {
                    try {
                        outputStream.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                try {
                    workbook.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        } catch (BusinessException e) {
            //导出文件的错误信息放在header中，便于前端获取
            try {
                if (e instanceof BusinessException) {
                    BusinessException businessException = (BusinessException) e;
                    response.addHeader("stateCode", businessException.getStatus().getStateCode());
                    response.addHeader("message", URLEncoder.encode(businessException.getStatus().getMessage(), "UTF-8"));
                } else {
                    log.error("导出标准服务发生异常,", e);
                    response.addHeader("stateCode", BaseErrorConstant.INTERNAL_ERROR.getStateCode());
                    response.addHeader("message", URLEncoder.encode(BaseErrorConstant.INTERNAL_ERROR.getMessage(), "UTF-8"));
                }
            } catch (UnsupportedEncodingException ex) {
                ex.printStackTrace();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importStandardServiceUpdate(MultipartFile file) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        if (file.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.FILE_NOT_EXIST);
        }
        String oldName = file.getOriginalFilename();
        boolean fileTypeBool = oldName == null || (!oldName.endsWith(".xlsx") && !oldName.endsWith(".xls"));
        if (fileTypeBool) {
            throw new BusinessException(BaseErrorConstant.FILE_TYPE_ERROR);
        }

        StandardServiceImportExcelListener excelListener = new StandardServiceImportExcelListener(standardServiceMapperExt,standardServiceMapper,departmentMapper);

        try {
            List<Object> list = EasyExcel.read(file.getInputStream(), StandardServiceImportExcel.class, excelListener)
                    .sheet(0).headRowNumber(1).doReadSync();
            List<EditStandardServiceParam> editStandardServiceList = excelListener.getEditStandardServiceSuccessList();
            List<StandardServiceFailDTO> standardServiceFailDTOList = excelListener.getStandardServiceFailDTOList();

            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(standardServiceFailDTOList)){
                String excelName = "批量导入修改标准服务信息失败";
                excelName = URLEncoder.encode(excelName, "UTF-8");
                ClassPathResource classPathResource = new ClassPathResource("template/standard-service-error.xlsx");
                InputStream templateFileName = classPathResource.getInputStream();
                String stateCode = BaseErrorConstant.PARAM_ERROR.getStateCode();
                String message = BaseErrorConstant.PARAM_ERROR.getMessage();
                //构建填充excel参数
                Map<String, Object> map = new HashMap<String, Object>();
                EasyExcelUtils.exportExcel(response, "list", standardServiceFailDTOList, map, excelName, templateFileName,
                        0, "失败描述", stateCode, message);
            }else {
                StringBuffer content = new StringBuffer();
                content.append("【修改标准服务】\n:");

                for (EditStandardServiceParam editStandardServiceParam : editStandardServiceList) {
                    String contentBody = updateStandardService(editStandardServiceParam,false);
                    content.append(contentBody);
                }
                response.setHeader("statecode", "00000");
                response.setHeader("message", URLEncoder.encode("导入成功", "UTF-8").replaceAll("\\+", "%20"));
                logService.recordOperateLog(ModuleEnum.OPERATION_KANBAN.code,
                        OperationKanbanOperateEnum.STANDARD_SERVICE_CONFIG.code,
                        content.toString(), LogResultEnum.LOG_SUCESS.code, null);
            }

        } catch (IOException e) {
            log.error("读取文件异常，文件名: {},异常描述：{}", oldName, e);
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        } catch (ExcelAnalysisException e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.RULE_NOT_ALLOW, e.getCause().getMessage());
        } catch (BusinessException e) {
            e.printStackTrace();
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        }
    }
}
