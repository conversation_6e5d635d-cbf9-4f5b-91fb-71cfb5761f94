package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfo;
import com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProvinceContractInfoMapper {
    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    long countByExample(ProvinceContractInfoExample example);

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    int deleteByExample(ProvinceContractInfoExample example);

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    int insert(ProvinceContractInfo record);

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    int insertSelective(ProvinceContractInfo record);

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    List<ProvinceContractInfo> selectByExample(ProvinceContractInfoExample example);

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    ProvinceContractInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    int updateByExampleSelective(@Param("record") ProvinceContractInfo record, @Param("example") ProvinceContractInfoExample example);

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    int updateByExample(@Param("record") ProvinceContractInfo record, @Param("example") ProvinceContractInfoExample example);

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    int updateByPrimaryKeySelective(ProvinceContractInfo record);

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    int updateByPrimaryKey(ProvinceContractInfo record);

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    int batchInsert(@Param("list") List<ProvinceContractInfo> list);

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    int batchInsertSelective(@Param("list") List<ProvinceContractInfo> list, @Param("selective") ProvinceContractInfo.Column ... selective);
}