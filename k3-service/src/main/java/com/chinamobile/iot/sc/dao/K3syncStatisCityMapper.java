package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.K3syncStatisCity;
import com.chinamobile.iot.sc.pojo.entity.K3syncStatisCityExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface K3syncStatisCityMapper {
    long countByExample(K3syncStatisCityExample example);

    int deleteByExample(K3syncStatisCityExample example);

    int deleteByPrimaryKey(String id);

    int insert(K3syncStatisCity record);

    int insertSelective(K3syncStatisCity record);

    List<K3syncStatisCity> selectByExample(K3syncStatisCityExample example);

    K3syncStatisCity selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") K3syncStatisCity record, @Param("example") K3syncStatisCityExample example);

    int updateByExample(@Param("record") K3syncStatisCity record, @Param("example") K3syncStatisCityExample example);

    int updateByPrimaryKeySelective(K3syncStatisCity record);

    int updateByPrimaryKey(K3syncStatisCity record);

    int batchInsert(@Param("list") List<K3syncStatisCity> list);

    int batchInsertSelective(@Param("list") List<K3syncStatisCity> list, @Param("selective") K3syncStatisCity.Column ... selective);
}