package com.chinamobile.iot.sc.utils;


import com.chinamobile.iot.sc.pojo.dto.DepartmentDTO;
import com.chinamobile.iot.sc.pojo.dto.ProductInfoDTO;
import com.chinamobile.iot.sc.pojo.entity.ProductProperty;
import com.chinamobile.iot.sc.pojo.entity.StandardService;
import com.chinamobile.iot.sc.pojo.param.SaveProMaterialParam;
import org.apache.commons.collections4.CollectionUtils;

/**
 * K3日志记录
 */

public class K3LogUtil {

    /**
     * 构造【配置物料】日志内容
     */
    public static String configMaterialContentFromParam(boolean add, SaveProMaterialParam param) {
        StringBuilder result = new StringBuilder();
        if (add) {
            result.append("【配置物料】\n");
        } else {
            result.append("【重新配置物料】\n");
        }
        result.append("商品组/销售组编码").append(param.getSpuCode()).append("\n")
                .append("商品规格编码").append(param.getSkuCode()).append("\n");

        if (CollectionUtils.isNotEmpty(param.getM2contractList())) {
            result.append("配置物料绑定关系，");
            param.getM2contractList().forEach(dto -> {
                result.append(String.format("物料编码为%s,合同编号为%s;", dto.getMaterialNum(), dto.getContractNum()));
            });
        }

        return result.toString();
    }

    /**
     * 构造【配置标准服务】日志内容
     */
    public static String configStandardServiceContentFromParam(ProductInfoDTO productInfoDTO,
                                                               StandardService standardService,
                                                               DepartmentDTO departmentDTO,
                                                               ProductProperty productProperty) {
        StringBuilder result = new StringBuilder();
        result.append("【配置标准服务】\n");
        result.append("商品组/销售组编码").append(productInfoDTO.getSpuOfferingCode()).append("\n")
                .append("商品规格编码").append(productInfoDTO.getSkuOfferingCode()).append("\n")
                .append(String.format("绑定关系，标准服务名称%s，实质商品名称%s，产品归属部门%s,产品属性%s。",
                        standardService.getName(), standardService.getRealProductName(), departmentDTO.getFullName(), productProperty.getName()));

        return result.toString();
    }


}
