package com.chinamobile.iot.sc.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/19
 * @description 省侧合同二级需求类型枚举类
 */
public enum ReqSecondTypeEnum {

    ICT("1","ICT业务需求"),
    OTHER_NEED("2","其他业务需求"),
    ;

    /**
     * 二级需求类型
     */
    private String type;

    /**
     * 二级需求类型描述
     */
    private String desc;

    ReqSecondTypeEnum(String type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByStatus(String type) {
        for (ReqSecondTypeEnum value : ReqSecondTypeEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}
