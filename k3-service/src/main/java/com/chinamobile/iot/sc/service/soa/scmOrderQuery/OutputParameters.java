
package com.chinamobile.iot.sc.service.soa.scmOrderQuery;

import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for OutputParameters complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="OutputParameters"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="ESB_FLAG" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ESB_RETURN_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ESB_RETURN_MESSAGE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="BIZ_SERVICE_FLAG" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="BIZ_RETURN_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="BIZ_RETURN_MESSAGE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="INSTANCE_ID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="TOTAL_RECORD" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="TOTAL_PAGE" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="PAGE_SIZE" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="CURRENT_PAGE" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="OUTPUTCOLLECTION" type="{http://soa.cmcc.com/OSB_SSCM_ZX_HQ_PageInquiryOrderInfoSrv}OUTPUTCOLLECTION"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OutputParameters", propOrder = {
    "esbflag",
    "esbreturncode",
    "esbreturnmessage",
    "bizserviceflag",
    "bizreturncode",
    "bizreturnmessage",
    "instanceid",
    "totalrecord",
    "totalpage",
    "pagesize",
    "currentpage",
    "outputcollection"
})
public class OutputParameters {

    @XmlElement(name = "ESB_FLAG", required = true, nillable = true)
    protected String esbflag;
    @XmlElement(name = "ESB_RETURN_CODE", required = true, nillable = true)
    protected String esbreturncode;
    @XmlElement(name = "ESB_RETURN_MESSAGE", required = true, nillable = true)
    protected String esbreturnmessage;
    @XmlElement(name = "BIZ_SERVICE_FLAG", required = true, nillable = true)
    protected String bizserviceflag;
    @XmlElement(name = "BIZ_RETURN_CODE", required = true, nillable = true)
    protected String bizreturncode;
    @XmlElement(name = "BIZ_RETURN_MESSAGE", required = true, nillable = true)
    protected String bizreturnmessage;
    @XmlElement(name = "INSTANCE_ID", required = true, nillable = true)
    protected String instanceid;
    @XmlElement(name = "TOTAL_RECORD", required = true, nillable = true)
    protected BigDecimal totalrecord;
    @XmlElement(name = "TOTAL_PAGE", required = true, nillable = true)
    protected BigDecimal totalpage;
    @XmlElement(name = "PAGE_SIZE", required = true, nillable = true)
    protected BigDecimal pagesize;
    @XmlElement(name = "CURRENT_PAGE", required = true, nillable = true)
    protected BigDecimal currentpage;
    @XmlElement(name = "OUTPUTCOLLECTION", required = true)
    protected OUTPUTCOLLECTION outputcollection;

    /**
     * Gets the value of the esbflag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getESBFLAG() {
        return esbflag;
    }

    /**
     * Sets the value of the esbflag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setESBFLAG(String value) {
        this.esbflag = value;
    }

    /**
     * Gets the value of the esbreturncode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getESBRETURNCODE() {
        return esbreturncode;
    }

    /**
     * Sets the value of the esbreturncode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setESBRETURNCODE(String value) {
        this.esbreturncode = value;
    }

    /**
     * Gets the value of the esbreturnmessage property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getESBRETURNMESSAGE() {
        return esbreturnmessage;
    }

    /**
     * Sets the value of the esbreturnmessage property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setESBRETURNMESSAGE(String value) {
        this.esbreturnmessage = value;
    }

    /**
     * Gets the value of the bizserviceflag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBIZSERVICEFLAG() {
        return bizserviceflag;
    }

    /**
     * Sets the value of the bizserviceflag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBIZSERVICEFLAG(String value) {
        this.bizserviceflag = value;
    }

    /**
     * Gets the value of the bizreturncode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBIZRETURNCODE() {
        return bizreturncode;
    }

    /**
     * Sets the value of the bizreturncode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBIZRETURNCODE(String value) {
        this.bizreturncode = value;
    }

    /**
     * Gets the value of the bizreturnmessage property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBIZRETURNMESSAGE() {
        return bizreturnmessage;
    }

    /**
     * Sets the value of the bizreturnmessage property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBIZRETURNMESSAGE(String value) {
        this.bizreturnmessage = value;
    }

    /**
     * Gets the value of the instanceid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getINSTANCEID() {
        return instanceid;
    }

    /**
     * Sets the value of the instanceid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setINSTANCEID(String value) {
        this.instanceid = value;
    }

    /**
     * Gets the value of the totalrecord property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTOTALRECORD() {
        return totalrecord;
    }

    /**
     * Sets the value of the totalrecord property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTOTALRECORD(BigDecimal value) {
        this.totalrecord = value;
    }

    /**
     * Gets the value of the totalpage property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTOTALPAGE() {
        return totalpage;
    }

    /**
     * Sets the value of the totalpage property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTOTALPAGE(BigDecimal value) {
        this.totalpage = value;
    }

    /**
     * Gets the value of the pagesize property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getPAGESIZE() {
        return pagesize;
    }

    /**
     * Sets the value of the pagesize property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setPAGESIZE(BigDecimal value) {
        this.pagesize = value;
    }

    /**
     * Gets the value of the currentpage property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getCURRENTPAGE() {
        return currentpage;
    }

    /**
     * Sets the value of the currentpage property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setCURRENTPAGE(BigDecimal value) {
        this.currentpage = value;
    }

    /**
     * Gets the value of the outputcollection property.
     * 
     * @return
     *     possible object is
     *     {@link OUTPUTCOLLECTION }
     *     
     */
    public OUTPUTCOLLECTION getOUTPUTCOLLECTION() {
        return outputcollection;
    }

    /**
     * Sets the value of the outputcollection property.
     * 
     * @param value
     *     allowed object is
     *     {@link OUTPUTCOLLECTION }
     *     
     */
    public void setOUTPUTCOLLECTION(OUTPUTCOLLECTION value) {
        this.outputcollection = value;
    }

}
