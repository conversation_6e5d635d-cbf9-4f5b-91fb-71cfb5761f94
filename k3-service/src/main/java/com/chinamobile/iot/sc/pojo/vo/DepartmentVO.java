package com.chinamobile.iot.sc.pojo.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/13 17:51
 * @description 产品部门VO
 */
@Data
public class DepartmentVO {
    /**
     * 组织ID
     */
    private Integer id;

    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createdDt;

    /**
     * 修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updatedDt;

    /**
     * 0 有效,   1 失效
     */
    private Integer status;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 组织全称
     */
    private String fullName;

    /**
     * 组织简称
     */
    private String shortName;

}
