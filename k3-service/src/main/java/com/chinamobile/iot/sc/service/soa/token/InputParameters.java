
package com.chinamobile.iot.sc.service.soa.token;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;

import com.chinamobile.iot.sc.service.soa.token.header.MSGHEADER;


/**
 * <p>InputParameters complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="InputParameters"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="MSGHEADER" type="{http://soa.cmcc.com/MsgHeader}MSGHEADER"/&gt;
 *         &lt;element name="SYSTEM_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PROVINCE_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CURRENT_TOKEN" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="QUERY_EXT" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="LAST_UPDATE_START" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="LAST_UPDATE_END" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "InputParameters", propOrder = {
    "msgheader",
    "systemcode",
    "provincecode",
    "currenttoken",
    "queryext",
    "lastupdatestart",
    "lastupdateend"
})
public class InputParameters {

    @XmlElement(name = "MSGHEADER", required = true)
    protected MSGHEADER msgheader;
    @XmlElement(name = "SYSTEM_CODE", required = true)
    protected String systemcode;
    @XmlElement(name = "PROVINCE_CODE", required = true)
    protected String provincecode;
    @XmlElement(name = "CURRENT_TOKEN", required = true)
    protected String currenttoken;
    @XmlElement(name = "QUERY_EXT", required = true, nillable = true)
    protected String queryext;
    @XmlElement(name = "LAST_UPDATE_START", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar lastupdatestart;
    @XmlElement(name = "LAST_UPDATE_END", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar lastupdateend;

    /**
     * ��ȡmsgheader���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link MSGHEADER }
     *     
     */
    public MSGHEADER getMSGHEADER() {
        return msgheader;
    }

    /**
     * ����msgheader���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link MSGHEADER }
     *     
     */
    public void setMSGHEADER(MSGHEADER value) {
        this.msgheader = value;
    }

    /**
     * ��ȡsystemcode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSYSTEMCODE() {
        return systemcode;
    }

    /**
     * ����systemcode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSYSTEMCODE(String value) {
        this.systemcode = value;
    }

    /**
     * ��ȡprovincecode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPROVINCECODE() {
        return provincecode;
    }

    /**
     * ����provincecode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPROVINCECODE(String value) {
        this.provincecode = value;
    }

    /**
     * ��ȡcurrenttoken���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCURRENTTOKEN() {
        return currenttoken;
    }

    /**
     * ����currenttoken���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCURRENTTOKEN(String value) {
        this.currenttoken = value;
    }

    /**
     * ��ȡqueryext���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQUERYEXT() {
        return queryext;
    }

    /**
     * ����queryext���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQUERYEXT(String value) {
        this.queryext = value;
    }

    /**
     * ��ȡlastupdatestart���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLASTUPDATESTART() {
        return lastupdatestart;
    }

    /**
     * ����lastupdatestart���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLASTUPDATESTART(XMLGregorianCalendar value) {
        this.lastupdatestart = value;
    }

    /**
     * ��ȡlastupdateend���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLASTUPDATEEND() {
        return lastupdateend;
    }

    /**
     * ����lastupdateend���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLASTUPDATEEND(XMLGregorianCalendar value) {
        this.lastupdateend = value;
    }

}
