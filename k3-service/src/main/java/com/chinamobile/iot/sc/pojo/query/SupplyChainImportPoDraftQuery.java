package com.chinamobile.iot.sc.pojo.query;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 导入供应链订单草稿
 **/
@Data
public class SupplyChainImportPoDraftQuery {

    /**
     * 省公司编码
     */
    private String provinceCode;

    /**
     * 申请单名称
     */
    private String description;

    /**
     * 来源系统
     */
    private String sourceFrom;

    /**
     * 来源系统单号
     */
    private String sourceFromNo;

    /**
     * MIS主体
     */
    private BigDecimal misBody;

    /**
     * 开支类型
     */
    private String expType;

    /**
     * 报账模式
     */
    private String reimbursementMode;

    /**
     * 申请部门代码
     */
    private String deptCode;

    /**
     * 申请部门名称
     */
    private String deptName;

    /**
     * 申请人ID
     */
    private String createdId;

    /**
     * 申请人姓名
     */
    private String createdName;

    /**
     * 物资类别
     */
    private String mtlTypeCode;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 需求类型
     */
    private String reqType;

    /**
     * 要求到货时间模式
     */
    private String arrivalTimeModel;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 不含税金额(元)
     */
    private BigDecimal amount;

    /**
     * 税额(元)
     */
    private BigDecimal taxSum;

    /**
     * 含税金额(元)
     */
    private BigDecimal amountTax;

    /**
     * 是否全额赠送
     */
    private String isFullPresent;


    /**
     * 结算单行信息
     */
    private List<SupplyChainIotLineInfoQuery> orderLine;
}


