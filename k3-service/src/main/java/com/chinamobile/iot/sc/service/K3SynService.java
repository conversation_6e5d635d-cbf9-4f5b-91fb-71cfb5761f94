package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.pojo.param.K3SynParam;
import com.chinamobile.iot.sc.pojo.query.SupplyChainImportPoDraftIOTQuery;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/7
 * @description k3 service同步接口类
 */
public interface K3SynService {

    /**
     * 同步保存月账单到k3系统
     * @param k3SynParam
     * @param loginIfo4Redis
     * @return
     * @throws Exception
     */
    Map<String, Object> synSaveMonthBillToK3(K3SynParam k3SynParam,
                                             LoginIfo4Redis loginIfo4Redis) throws Exception;

    /**
     * 同步提交月账单到k3系统
     * @param k3SynParam
     * @param loginIfo4Redis
     * @return
     * @throws Exception
     */
    Map<String, Object> synSubmitMonthBillToK3(K3SynParam k3SynParam,
                                               LoginIfo4Redis loginIfo4Redis) throws Exception;

    BaseAnswer<Void> importPoDraft(SupplyChainImportPoDraftIOTQuery request);

    BaseAnswer<Void> getOrderStatus(SupplyChainImportPoDraftIOTQuery request);

    BaseAnswer<Void> cancelOrder(SupplyChainImportPoDraftIOTQuery request);
}
