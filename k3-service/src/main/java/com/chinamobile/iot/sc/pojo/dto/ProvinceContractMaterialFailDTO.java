package com.chinamobile.iot.sc.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> xie<PERSON><PERSON><PERSON>
 * @date : 2023/3/23 11:16
 * @description: 省合同导入失败数据
 **/
@Data
public class ProvinceContractMaterialFailDTO {

    @ExcelProperty(value = "产品编码",index = 0)
    private String productCode;

    @ExcelProperty(value = "产品名称",index = 1)
    private String productName;

    @ExcelProperty(value = "物料编码",index = 2)
    private String materialCode;

    @ExcelProperty(value = "物料描述",index = 3)
    private String materialDescribe;

    @ExcelProperty(value = "配件编码",index = 4)
    private String partCode;

    @ExcelProperty(value = "配件名称（合同标的物名称）",index = 5)
    private String partName;

    @ExcelProperty(value = "套编码",index = 6)
    private String nestedCode;

    @ExcelProperty(value = "物联网公司物料编码",index = 7)
    private String internetMaterialCode  ;

    @ExcelProperty(value = "单位",index = 8)
    private String unit;

    @ExcelProperty(value = "单价（元，不含税）",index = 9)
    private BigDecimal taxExclusiveUnivalence;

    @ExcelProperty(value = "税率",index = 10)
    private String taxRate;

    @ExcelProperty(value = "单价（元，含税）",index = 11)
    private BigDecimal taxInclusiveUnivalence;

    @ExcelProperty(value="失败原因",index = 12)
    private String failedReason;
}
