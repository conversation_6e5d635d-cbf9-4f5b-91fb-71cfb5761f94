package com.chinamobile.iot.sc.pojo.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/15
 * @description 线上结算管理订单实体类
 */
@Data
public class OnlineSettlementOsOrderDTO {

    /**
     * 下单时间
     */
    private Date orderCreateTime;

    /**
     * 交易完成时间
     */
    private Date orderSuccessTime;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 商品名称（规格）
     */
    private String skuOfferingName;

    /**
     * 订购数量（规格）
     */
    private Integer skuQuantity;

    /**
     * 商品类型
     */
    private String spuOfferingClass;

    /**
     * 订单收入归属省份名称
     */
    private String provinceName;

    /**
     * 订单收入归属地市名称
     */
    private String cityName;

    /**
     * 订单金额
     */
    private Long totalPrice;

    /**
     * 订单类型
     */
    private String orderType;

}
