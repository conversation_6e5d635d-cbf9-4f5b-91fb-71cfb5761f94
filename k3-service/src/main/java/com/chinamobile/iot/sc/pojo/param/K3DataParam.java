package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/16
 * @description k3月数据订单查询
 */
@Data
public class K3DataParam {

    /**
     * 销售时间
     */
    @NotEmpty(message = "销售时间不能为空")
    private String saleDate;

    /**
     * 商品组/销售商品名称
     */
    private String spuName;

    /**
     * 商品类型
     */
    private String spuType;

    /**
     * 订单收入省份名称
     */
    private String orderProvince;

    /**
     * 订单收入地市名称
     */
    private String orderCity;

    /**
     * 合同编号
     */
    private String contractNum;

    /**
     * 物料编码
     */
    private String matetrialNum;

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 每页返回条数
     */
    private Integer pageCount;
}
