package com.chinamobile.iot.sc.controller;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.dto.DepartmentDTO;
import com.chinamobile.iot.sc.pojo.entity.Department;
import com.chinamobile.iot.sc.pojo.param.OfficialOrganization;
import com.chinamobile.iot.sc.pojo.vo.DepartmentVO;
import com.chinamobile.iot.sc.service.DepartmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/14 16:43
 * @description 部门信息Controller
 */
@RestController
@RequestMapping("/k3serv/department")
public class DepartmentController {

    @Autowired
    private DepartmentService departmentService;

    /**
     * 查询部门信息
     *
     * @return
     */
    @GetMapping("/list")
    public BaseAnswer<List<DepartmentVO>> queryAllDepartments() {
/*        List<DepartmentDTO> departmentDTOS = departmentService.queryAllDepartments();
        List<DepartmentVO> departmentVOS = Collections.EMPTY_LIST;
        if (!CollectionUtils.isEmpty(departmentDTOS)) {
            departmentVOS = departmentDTOS.stream().filter(departmentDTO -> {
                if (departmentDTO != null && departmentDTO.getOrgLevel() != null) {
                    return 2 == departmentDTO.getOrgLevel();
                } else {
                    return false;
                }
            }).map(departmentDTO -> {
                DepartmentVO departmentVO = new DepartmentVO();
                BeanUtils.copyProperties(departmentDTO, departmentVO);
                return departmentVO;
            }).collect(Collectors.toList());*/

        List<Department> departmentList = departmentService.getDepartmentList();
        if(departmentList.isEmpty()){
            return BaseAnswer.success(null);
        }
        departmentList = departmentList.stream().filter(department -> department.getDeleteDt() == null).collect(Collectors.toList());
        List<DepartmentVO> collect = departmentList.stream().map(d -> {
            DepartmentVO departmentVO = new DepartmentVO();
            BeanUtils.copyProperties(d, departmentVO);
            return departmentVO;
        }).collect(Collectors.toList());
        return BaseAnswer.success(collect);
    }

    /**
     * 统一平台机构推送接口
     * @param param
     * @return
     */
    @PostMapping("/organization/sync")
    public BaseAnswer<String> officialOrganizationSyncMessage(@RequestBody List<OfficialOrganization> param){
        departmentService.officialOrganizationSync(param);
        return BaseAnswer.success("成功");
    }

}
