package com.chinamobile.iot.sc.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.constant.ProductTypeEnum;
import com.chinamobile.iot.sc.dao.K3syncStatisDepartmentMapper;
import com.chinamobile.iot.sc.dao.K3syncStatisDepartmentMapperExt;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.entity.K3syncStatisDepartment;
import com.chinamobile.iot.sc.pojo.entity.K3syncStatisDepartmentExample;
import com.chinamobile.iot.sc.pojo.mapper.K3DepartmentDO;
import com.chinamobile.iot.sc.pojo.param.K3DepartmentParam;
import com.chinamobile.iot.sc.pojo.vo.K3DepartmentVO;
import com.chinamobile.iot.sc.service.K3syncStatisDepartmentService;
import com.chinamobile.iot.sc.util.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/23
 * @description k3部门信息service实现类
 */
@Service
public class K3syncStatisDepartmentServiceImpl implements K3syncStatisDepartmentService {

    @Resource
    private K3syncStatisDepartmentMapper k3syncStatisDepartmentMapper;

    @Resource
    private K3syncStatisDepartmentMapperExt k3syncStatisDepartmentMapperExt;


    @Override
    public void batchInsertK3Department(List<K3syncStatisDepartment> departmentList) {
        k3syncStatisDepartmentMapper.batchInsert(departmentList);
    }

    @Override
    public List<K3syncStatisDepartment> listK3syncStatisDepartmentByNeed(K3syncStatisDepartmentExample departmentExample) {
        return k3syncStatisDepartmentMapper.selectByExample(departmentExample);
    }

    @Override
    public void updateNeedById(K3syncStatisDepartment department, K3syncStatisDepartmentExample departmentExample) {
        k3syncStatisDepartmentMapper.updateByExampleSelective(department, departmentExample);
    }

    @Override
    public PageData<K3DepartmentVO> pageK3DepartmentVO(K3DepartmentParam k3DepartmentParam) {
        PageData<K3DepartmentVO> pageData = new PageData<>();

        Integer pageNum = k3DepartmentParam.getPageNum();
        Integer pageSize = k3DepartmentParam.getPageSize();

        Page<K3DepartmentDO> page = new Page<>(pageNum, pageSize);
        List<K3DepartmentDO> k3DepartmentDOList
                = k3syncStatisDepartmentMapperExt.listK3Department(page, k3DepartmentParam);

        List<K3DepartmentVO> k3DepartmentVOList = new ArrayList();
        BigDecimal oneThousand = new BigDecimal(1000);
        if (CollectionUtils.isNotEmpty(k3DepartmentDOList)){
            k3DepartmentDOList.forEach(k3DepartmentDO -> {
                K3DepartmentVO k3DepartmentVO = new K3DepartmentVO();
                k3DepartmentVO.setId(k3DepartmentDO.getId());
                String createTimeStr = DateUtils.dateToStr(k3DepartmentDO.getCreateTime(), DateUtils.DEFAULT_DATETIME_FORMAT);
                k3DepartmentVO.setCreateTimeStr(createTimeStr);
                String productTypeDes = ProductTypeEnum.getDesc(k3DepartmentDO.getProductType());
                k3DepartmentVO.setProductTypeStr(productTypeDes);
                k3DepartmentVO.setDepartmentName(k3DepartmentDO.getDepartmentName());
                k3DepartmentVO.setOrderCount(k3DepartmentDO.getOrderCount());
                k3DepartmentVO.setTotalPrice(new BigDecimal(k3DepartmentDO.getTotalPrice()).divide(oneThousand,2, RoundingMode.HALF_UP));

                k3DepartmentVOList.add(k3DepartmentVO);
            });
        }

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(k3DepartmentVOList);
        return pageData;

    }

    @Override
    public K3syncStatisDepartment getK3DepartmentById(String id) {
        return k3syncStatisDepartmentMapper.selectByPrimaryKey(id);
    }
}
