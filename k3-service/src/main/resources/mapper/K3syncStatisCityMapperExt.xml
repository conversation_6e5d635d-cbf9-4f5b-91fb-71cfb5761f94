<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.K3syncStatisCityMapperExt">
  <select id="listK3syncStatisCity" resultType="com.chinamobile.iot.sc.pojo.mapper.K3syncStatisDO">
    SELECT
      id,
      contract_num contractNum,
      contract_name contractName,
      contract_dept contractDept,
      contract_statis_type contractStatisType,
      product_type productType,
      case
        when product_type = 0 then 'OneNET独立服务'
        when product_type = 1 then 'DICT产品增值服务包'
        when product_type = 2 then 'OnePark独立服务'
      else '未定义的商品类型'
      end productTypeName,
      order_province_name orderProvinceName,
      order_city_name orderCityName,
      total_price totalPrice,
      k3_ret_num k3RetNum,
      k3_sync_status k3SyncStatus,
      case
          when isnull(k3_sync_status) then ''
          when k3_sync_status = 0 then '未同步'
          when k3_sync_status = 1 then '成功'
          when k3_sync_status = 2 then '失败'
      else '未定义状态'
      end k3SyncStatusName,
      k3_commit_status k3CommitStatus,
      case
          when isnull(k3_commit_status) then ''
          when k3_commit_status = 0 then '未同步'
          when k3_commit_status = 1 then '成功'
          when k3_commit_status = 2 then '失败'
      else '未定义状态'
      end k3CommitStatusName,
      create_time createTime,
      pro_ret_num proRetNum,
      case
          when isnull(pro_sync_status) then ''
          when pro_sync_status = 0 then '未同步'
          when pro_sync_status = 1 then '成功'
          when pro_sync_status = 2 then '失败'
      else '未定义状态'
      end proSyncStatusName,
      case
          when isnull(pro_submit_account_status) then ''
          when pro_submit_account_status = 0 then '待报账'
          when pro_submit_account_status = 1 then '已报账'
      else '未定义状态'
      end proSubmitAccountStatusName,
      commit_k3_user_name commitK3UserName,
      commit_suc_time commitSucTime,
      k3_status k3Status,
      pro_data_code proDataCode,
      material_dept materialDept
  FROM
      k3sync_statis_city
    where
      k3_status = 1
    <if test="k3SyncStatisQuery.saleDate != null and k3SyncStatisQuery.saleDate != ''">
      and DATE_FORMAT(date_sub(create_time, interval 1 month),'%Y-%m') = #{k3SyncStatisQuery.saleDate}
    </if>
    <if test="k3SyncStatisQuery.orderProvince != null and k3SyncStatisQuery.orderProvince != ''">
      and order_province_name like '%${k3SyncStatisQuery.orderProvince}%'
    </if>
    <if test="k3SyncStatisQuery.orderCity != null and k3SyncStatisQuery.orderCity != ''">
      and order_city_name like '%${k3SyncStatisQuery.orderCity}%'
    </if>
    <if test="k3SyncStatisQuery.contractNum != null and k3SyncStatisQuery.contractNum != ''">
      and contract_num like '%${k3SyncStatisQuery.contractNum}%'
    </if>
    <if test="k3SyncStatisQuery.productType != null">
      and product_type = #{k3SyncStatisQuery.productType}
    </if>
    <if test="k3SyncStatisQuery.contractName != null and k3SyncStatisQuery.contractName != ''">
      and contract_name like '%${k3SyncStatisQuery.contractName}%'
    </if>
    <if test="k3SyncStatisQuery.k3SyncStatus != null and k3SyncStatisQuery.k3SyncStatus != ''">
      and k3_sync_status = #{k3SyncStatisQuery.k3SyncStatus}
    </if>
    <if test="k3SyncStatisQuery.k3CommitStatus != null and k3SyncStatisQuery.k3CommitStatus != ''">
      and case when #{k3SyncStatisQuery.k3CommitStatus} = 0 then k3_commit_status in (0,2)
                when #{k3SyncStatisQuery.k3CommitStatus} = 1 then k3_commit_status = 1
          end
    </if>
    <if test="k3SyncStatisQuery.k3RetNum != null and k3SyncStatisQuery.k3RetNum != ''">
      and k3_ret_num like '%${k3SyncStatisQuery.k3RetNum}%'
    </if>
    <if test="k3SyncStatisQuery.contractDept != null and k3SyncStatisQuery.contractDept != ''">
      and contract_dept = #{k3SyncStatisQuery.contractDept}
    </if>
    order by contract_num
  </select>

  <select id="listProK3syncStatisCity" resultType="com.chinamobile.iot.sc.pojo.mapper.K3syncStatisProDO">
      SELECT
          create_time createTime,
          pro_data_code proDataCode,
          contract_num contractNum,
          contract_name contractName,
          contract_dept contractDept,
          contract_statis_type contractStatisType,
          product_type productType,
          case
            when product_type = 0 then 'OneNET独立服务'
            when product_type = 1 then 'DICT产品增值服务包'
            when product_type = 2 then 'OnePark独立服务'
          else '未定义的商品类型'
          end productTypeName,
          order_province_name orderProvinceName,
          order_city_name orderCityName,
          sum(total_price) totalPrice,
          pro_ret_num proRetNum,
          pro_sync_status proSyncStatus,
          case
              when isnull(pro_sync_status) then ''
              when pro_sync_status = 0 then '未同步'
              when pro_sync_status = 1 then '成功'
              when pro_sync_status = 2 then '失败'
          else '未定义状态'
          end proSyncStatusName,
          pro_submit_account_status proSubmitAccountStatus,
          case
              when isnull(pro_submit_account_status) then ''
              when pro_submit_account_status = 0 then '待报账'
              when pro_submit_account_status = 1 then '已报账'
          else '未定义状态'
          end proSubmitAccountStatusName,
          k3_status k3Status,
          pro_material_status proMaterialStatus
      FROM
          k3sync_statis_city
        where 1= 1
      <if test="k3SyncStatisQuery.orderProvince != null and k3SyncStatisQuery.orderProvince != ''">
          and order_province_name like '%${k3SyncStatisQuery.orderProvince}%'
      </if>
      <if test="k3SyncStatisQuery.orderCity != null and k3SyncStatisQuery.orderCity != ''">
          and order_city_name like '%${k3SyncStatisQuery.orderCity}%'
      </if>
      <if test="k3SyncStatisQuery.contractNum != null and k3SyncStatisQuery.contractNum != ''">
          and contract_num like '%${k3SyncStatisQuery.contractNum}%'
      </if>
      <if test="k3SyncStatisQuery.productType != null">
          and product_type = #{k3SyncStatisQuery.productType}
      </if>
      <if test="k3SyncStatisQuery.contractName != null and k3SyncStatisQuery.contractName != ''">
          and contract_name like '%${k3SyncStatisQuery.contractName}%'
      </if>
      <if test="k3SyncStatisQuery.contractDept != null and k3SyncStatisQuery.contractDept != ''">
          and contract_dept = #{k3SyncStatisQuery.contractDept}
      </if>

      <if test="k3SyncStatisQuery.proRetNum != null and k3SyncStatisQuery.proRetNum != ''">
          and pro_ret_num like '%${k3SyncStatisQuery.proRetNum}%'
      </if>
      <if test="k3SyncStatisQuery.proSyncStatus != null and k3SyncStatisQuery.proSyncStatus != ''">
          and pro_sync_status = #{k3SyncStatisQuery.proSyncStatus}
      </if>
        group by pro_data_code
  </select>

  <select id="listSynCity" resultType="com.chinamobile.iot.sc.pojo.mapper.K3SynDO">
    select
        id,
        contract_num contractNum,
        sell_unit sellUnit,
        contract_seller contractSeller,
        custom_code customCode,
        contract_type contractType,
        seller_org_id sellerOrgId,
        seller_team_id sellerTeamId,
        seller_dept_id sellerDeptId,
        seller_phone sellerPhone,
        cost_center costCenter,
        buyer_province_code buyerProvinceCode,
        buyer_city_code buyerCityCode,
        project,
        sub_project subProject,
        money_unit moneyUnit,
        create_time createTime,
        pro_data_code proDataCode,
        material_dept materialDept
    from
        k3sync_statis_city
    where
        k3_sync_status in (0,2)
    <if test="k3SynQuery.saleDate != null and k3SynQuery.saleDate != ''">
      and DATE_FORMAT(date_sub(create_time, interval 1 month),'%Y-%m') = #{k3SynQuery.saleDate}
    </if>
    <if test="k3SynQuery.idList != null">
      and id in
      <foreach item ="item" index ="index" collection ="k3SynQuery.idList" open ="(" separator ="," close =")" >
        #{item}
      </foreach>
    </if>
  </select>

    <select id="listSynCommitCity" resultType="com.chinamobile.iot.sc.pojo.mapper.K3SynCommitDO">
        select
            k3_ret_num k3RetNum
        from
          k3sync_statis_city
        where
          k3_sync_status = 1
        and k3_commit_status in (0,2)
        <if test="k3SynQuery.saleDate != null and k3SynQuery.saleDate != ''">
            and DATE_FORMAT(date_sub(create_time, interval 1 month),'%Y-%m') = #{k3SynQuery.saleDate}
        </if>
        <if test="k3SynQuery.idList != null">
            and id in
            <foreach item ="item" index ="index" collection ="k3SynQuery.idList" open ="(" separator ="," close =")" >
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getK3ProBillRelatedCity" resultType="com.chinamobile.iot.sc.pojo.mapper.K3ProBillContractDO">
        select distinct
            ksc.contract_num contractNum,
            sum(ksc.total_price) totalPrice,
            ksc.order_province_name orderProvinceName,
            pci.province_contract_no provinceContractNo,
            pci.province_contract_name provinceContractName,
            crt.amount_including_tax amountIncludingTax,
            crt.end_date endDate
        from
            k3sync_statis_city ksc
        left join province_contract_info pci on ksc.contract_num = pci.Internet_contract_code
        left join contract crt on ksc.contract_num = crt.number
        where 1=1
        <if test="k3ProBillParam.proDataCode != null and k3ProBillParam.proDataCode != ''">
            and ksc.pro_data_code = #{k3ProBillParam.proDataCode}
        </if>
        group by ksc.contract_num

    </select>
    
    <select id="listK3CityByProDataCode" resultType="com.chinamobile.iot.sc.pojo.mapper.GenK3DO">
        select distinct
            order_province_name orderProvinceName,
            pro_sync_status proSyncStatus,
            k3_status k3Status
        from
            k3sync_statis_city
        where
            pro_data_code = #{proDataCode}
    </select>

    <select id="listSynOrderCity" resultType="com.chinamobile.iot.sc.pojo.mapper.K3SynOrderDO">
        select
            ko.order_id orderId,
            ko.contract_num contractNum,
            ko.material_num materialNum,
            ko.material_unit materialUnit,
            ko.material_dept materialDept,
            ko.quatity,
            ko.contract_tax contractTax,
            ko.unit_price unitPrice
        from
            k3_orders ko,
            k3sync_statis_city ksc
        where
            ko.contract_num = ksc.contract_num
        and IFNULL(ko.contract_dept,-1) = IFNULL(ksc.contract_dept,-1)
        and ko.material_dept = ksc.material_dept
        <if test="k3SynOrderQuery.saleDate != null and k3SynOrderQuery.saleDate != ''">
            and DATE_FORMAT(date_sub(ko.create_time, interval 1 month),'%Y-%m') = #{k3SynOrderQuery.saleDate}
        </if>

        <if test="k3SynOrderQuery.orderIdList != null">
            and ko.order_id in
            <foreach item ="item" index ="index" collection ="k3SynOrderQuery.orderIdList" open ="(" separator ="," close =")" >
                #{item}
            </foreach>
        </if>

        <if test="k3SynOrderQuery.toK3Date != null and k3SynOrderQuery.toK3Date != ''">
            and DATE_FORMAT(ko.create_time,'%Y-%m') = #{k3SynOrderQuery.toK3Date}
        </if>

        <if test="k3SynOrderQuery.materialDept != null and k3SynOrderQuery.materialDept != ''">
            and ko.material_dept = #{k3SynOrderQuery.materialDept}
        </if>
    </select>
</mapper>