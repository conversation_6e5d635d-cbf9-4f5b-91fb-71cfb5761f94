<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.export.dao.ext.AtomOfferingInfoMapperExt">
   <select id="selectProductScreenExport" resultType="com.chinamobile.export.pojo.mapper.ProductScreenExportDO">
       SELECT
       spu.offering_name spuOfferingName,
       spu.offering_code spuOfferingCode,
       spu.secretly_listed as secretlyListed,
       spu.product_description as productDescription,
       (SELECT GROUP_CONCAT(DISTINCT d.`NAME`) FROM product_navigation_directory d JOIN navigation_info n ON d.id = n.level1_navigation_code WHERE n.spu_offering_code = spu.offering_code ) firstNavigationName,
       (SELECT GROUP_CONCAT(DISTINCT d.`NAME`) FROM product_navigation_directory d JOIN navigation_info n ON d.id = n.level2_navigation_code WHERE n.spu_offering_code = spu.offering_code ) secondNavigationName,
       (SELECT GROUP_CONCAT(DISTINCT d.`NAME`) FROM product_navigation_directory d JOIN navigation_info n ON d.id = n.level3_navigation_code WHERE n.spu_offering_code = spu.offering_code ) thirdNavigationName,
       (SELECT GROUP_CONCAT(label) FROM spu_sale_label where (type = 0 or type is null) and spu_code = spu.offering_code) mainSaleLabel,
       (SELECT GROUP_CONCAT(label) FROM spu_sale_label where type = 1 and spu_code = spu.offering_code) subSaleLabel,
       spu.product_keywords productKeywords,
       ca.offering_class spuOfferingClass,
       DATE_FORMAT( atom.create_time, '%Y/%m/%d %H:%i:%s') createTime,
       spu.offering_status spuOfferingStatus,
       sku.offering_name skuOfferingName,
       sku.offering_code skuOfferingCode,
       sku.price/1000 price,
       sku.offering_status skuOfferingStatus,
       srt.province_code skuReleaseProvince,
       srt.city_code skuReleaseCity,
       atom.offering_name atomOfferingName,
       atom.id atomId,
       atom.offering_code atomOfferingCode,
       atom.offering_class atomOfferingClass,
       atom.ext_soft_offering_code extSoftOfferingCode,
       atom.ext_hard_offering_code extHardOfferingCode,
       atom.quantity atomQuantity,
       atom.model,
       atom.color,
       atom.settle_price/1000 settlePrice,
       atom.offeringSaleRegion,
       sku.supplier_name supplierName,
       CASE
       WHEN (CONCAT(ca.offering_class,atom.offering_class) !="A06H") THEN
       atom.atom_sale_price/1000  ELSE 0 end calculationCollectionPrice,
       atom.atom_sale_price atomSalePrice,
       atom.charge_code chargeCode,
       atom.charge_id chargeId,
       atomh.atom_offering_version         as atomOfferingVersion,
       atomh.spu_offering_version          as spuOfferingVersion,
       atomh.sku_offering_version          as skuOfferingVersion,
       ss.name standardServiceName,
       ss.real_product_name realProductName,
       case when ss.product_property_id = '101' then '自研'
        when ss.product_property_id = '102' then '生态'
       ELSE '其他'
       end productProperty,
       d.short_name productDepartmentName
       FROM
       atom_offering_info atom
       LEFT JOIN sku_offering_info sku ON atom.sku_code = sku.offering_code AND atom.spu_code = sku.spu_code AND sku.delete_time is null
       LEFT JOIN spu_offering_info spu ON spu.offering_code = atom.spu_code AND spu.delete_time is null
       LEFT JOIN category_info ca ON ca.spu_id = spu.id
       LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
       left join atom_offering_info_history atomh on  atomh.id = (
       select id from atom_offering_info_history atomh where atomh.offering_code = atom.offering_code
       and atomh.spu_code = atom.spu_code
       and atomh.sku_code = atom.sku_code
       order by atomh.atom_offering_version desc,atomh.sku_offering_version desc, atomh.spu_offering_version desc
       limit 1)
       LEFT JOIN atom_std_service ass ON ass.atom_id = atom.id
       LEFT JOIN standard_service ss ON ass.std_service_id = ss.id
       JOIN department d ON d.id = ss.product_department_id
       WHERE atom.delete_time is null
       <if test=" areaCode != null and areaCode.size() != 0">
           and  ( srt.province_code in
           <foreach collection="areaCode" item="item" index="index" open="(" close=")" separator=",">
               #{item}
           </foreach>
           or srt.province_code ='000')
       </if>
       ORDER BY atom.create_time DESC
   </select>

</mapper>
