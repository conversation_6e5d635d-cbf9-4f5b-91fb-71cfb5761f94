package com.chinamobile.export.service.impl;


import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.SdkClientException;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.BucketCrossOriginConfiguration;
import com.amazonaws.services.s3.model.CORSRule;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.CopyObjectRequest;
import com.amazonaws.services.s3.model.CopyObjectResult;
import com.amazonaws.services.s3.model.DeleteObjectsRequest;
import com.amazonaws.services.s3.model.DeleteObjectsResult;
import com.amazonaws.services.s3.model.MultiObjectDeleteException;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import com.amazonaws.services.s3.transfer.Upload;
import com.amazonaws.services.s3.transfer.model.UploadResult;
import com.chinamobile.export.config.OneNetStorageConfig;
import com.chinamobile.export.config.ThreadPoolConfig;
import com.chinamobile.export.service.IStorageService;
import com.chinamobile.export.util.FileUtil;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.Base64Upload;
import com.chinamobile.iot.sc.entity.ByteArrayUpload;
import com.chinamobile.iot.sc.entity.CopyUpload;
import com.chinamobile.iot.sc.entity.DelResult;
import com.chinamobile.iot.sc.entity.QueryResult;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.common.utils.BaseServiceUtils.doFailure;
import static com.chinamobile.iot.sc.common.utils.BaseServiceUtils.doSuccess;
import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.OSS_DEL_OR_SETEXPIRED_ERROR;


/**
 * OneNet对象存储服务  默认开启OneNet对象存储
 */
@Service
@Slf4j
public class OneNetObjectStorageService implements IStorageService {

    @Autowired
    private OneNetStorageConfig config;
    @Autowired
    private ThreadPoolConfig poolConfig;

    private AmazonS3 client;
    private TransferManager transferManager;

    @PostConstruct
    public void init(){
        this.client = getAmazonClient();
        ThreadPoolExecutor threadPoolExecutor  = new ThreadPoolExecutor(poolConfig.getCodeSize(),
                    poolConfig.getMaxSize(), poolConfig.getKeepAliveTime(), TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(poolConfig.getQueueSize()),
                    new ThreadFactoryBuilder().setNameFormat("handler-pool-%d").build(),
                    (r, executor) -> {
                        try {
                            TimeUnit.MILLISECONDS.sleep(poolConfig.getRejectedTime());
                            executor.execute(r);
                        } catch (Throwable e) {
                            log.warn("线程池拒绝策略中发生运行时异常", e);
                        }
                    });

        this.transferManager = buildTransferManager(client,threadPoolExecutor);
    }
    @Override
    public BaseAnswer<UpResult> uploadFile(File file, String fname, boolean isCover, int expiredDay) throws Exception {
        try {
            Upload upload = transferManager.upload(buildFileRequest(fname,file));
            return doSuccess(doResult(fname, upload,expiredDay));
        } catch (AmazonClientException e) {
            log.error("oneNet 对象存储失败：",e);
            return doFailure(BaseErrorConstant.OSS_UPLOAD_ERR);
        }
    }

    @Override
    public BaseAnswer<UpResult>  uploadByte(ByteArrayUpload data) throws Exception {
        try {
            String fname = data.getFileName();
            String key = fname;
            byte[] bytes = data.getBytes();
            if (!data.isCover()) {
                key = FileUtil.getKey(bytes, fname);
            }
            try(InputStream in = new ByteArrayInputStream(bytes)){
                Upload upload = transferManager.upload(buildPutRequest(key, in));
                return doSuccess(doResult(fname, upload, data.getExpiredDay()));
            }
        } catch (AmazonClientException e) {
            log.error("oneNet 对象存储失败：",e);
            return doFailure(BaseErrorConstant.OSS_UPLOAD_ERR);
        }
    }

    @Override
    public BaseAnswer<UpResult> uploadBase64(Base64Upload base64) throws Exception {
        final String[] info = base64.getBase64().split(",");
        byte[] data = Base64.getDecoder().decode(info.length==2?info[1]:info[0]);
        return uploadByte(new ByteArrayUpload(data, base64.getFileName(), base64.isCover(),base64.getExpiredDay()));
    }

    @Override
    public BaseAnswer<DelResult> delete(String... keys) throws Exception {
        BaseAnswer<DelResult> answer = new BaseAnswer<>();
        DelResult delResult = new DelResult();
        try {
            DeleteObjectsRequest request = new DeleteObjectsRequest(config.getBucketName());
            request.withKeys(keys);
            DeleteObjectsResult result = client.deleteObjects(request);

            List<String> successKeys = result.getDeletedObjects().stream()
                    .map(DeleteObjectsResult.DeletedObject::getKey)
                    .collect(Collectors.toList());
            List<String> failKeys = Arrays.stream(keys).filter(key -> !successKeys.contains(key)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(failKeys)) {
                delResult.setFailKeys(failKeys);
                answer.setStatus(OSS_DEL_OR_SETEXPIRED_ERROR);
            }
        } catch (MultiObjectDeleteException e) {
            List<MultiObjectDeleteException.DeleteError> errors = e.getErrors();
            delResult.setFailKeys(errors.stream()
                    .map(MultiObjectDeleteException.DeleteError::getKey)
                    .collect(Collectors.toList()));
            answer.setStatus(OSS_DEL_OR_SETEXPIRED_ERROR);
            log.error("删除对象存储数据失败：",e);
        } catch (AmazonServiceException e) {
            log.error("删除对象存储数据失败：",e);
            answer.setStatus(OSS_DEL_OR_SETEXPIRED_ERROR);
        } catch (SdkClientException e) {
            log.error("删除对象存储数据失败：",e);
            answer.setStatus(OSS_DEL_OR_SETEXPIRED_ERROR);
        }
        return answer.setData(delResult);
    }

    @Override
    public BaseAnswer<DelResult> deleteAfterDays(int days, String... keys) throws Exception {
        DelResult delResult = new DelResult();
        BaseAnswer<DelResult> answer = new BaseAnswer<>();
        for (String key: keys) {
            CopyObjectRequest request = new CopyObjectRequest(config.getBucketName(), key, config.getBucketName(), key);
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setExpirationTime(DateUtils.addDays(new Date(), days));
            request.setCannedAccessControlList(CannedAccessControlList.PublicRead);
            request.setNewObjectMetadata(metadata);
            try {
                client.copyObject(request);
            } catch (SdkClientException e) {
                log.error("删除对象存储数据失败：",e);
                delResult.setFailKey(key);
                answer.setStatus(OSS_DEL_OR_SETEXPIRED_ERROR);
            }
        }
        return answer.setData(delResult);
    }

    @Override
    public BaseAnswer<QueryResult> getUrl(String key) {
        //URL url = client.getUrl(config.getBucketName(), key);
       // final String strUrl = url.toString();
        QueryResult ret = new QueryResult();
        ret.setInnerUrl(config.getQueryHttpInner(key));
        ret.setOuterUrl(config.getQueryHttpOuter(key));
        return doSuccess(ret);
    }

    @Override
    public void preview(String key, HttpServletResponse response) {}

    @Override
    public BaseAnswer<UpResult> copy(CopyUpload copyUpload) throws Exception {
        CopyObjectRequest copyObjectRequest = new CopyObjectRequest(config.getBucketName(), copyUpload.getFromKey(),
                config.getBucketName(), copyUpload.getToKey())
                .withCannedAccessControlList(CannedAccessControlList.PublicRead);
        CopyObjectResult result = client.copyObject(copyObjectRequest);
        return doSuccess(doResult(copyUpload.getToKey(),copyUpload.getExpiredDay(),copyUpload.getToKey()));
    }

    private UpResult doResult(String fname,Upload upload,int expiredDay) throws Exception {
        UploadResult uploadResult = upload.waitForUploadResult();
        UpResult result = doResult(fname, expiredDay, uploadResult.getKey());
        return result;
    }

    private UpResult doResult(String fname, int expiredDay, String key) throws Exception {
        //URL url = client.getUrl(config.getBucketName(), key);
        //final String strUrl = url.toString();
        UpResult result = new UpResult();
        result.setKey(key);
        result.setFileName(fname);
        result.setInnerUrl(config.getQueryHttpInner(key));
        result.setOuterUrl(config.getQueryHttpOuter(key));
        //设置过期
        if(expiredDay>0 ){
            deleteAfterDays(expiredDay,key);
        }
        return result;
    }

    /**
     * 返回新的S3客户端对象 {@link AmazonS3}
     * @return AmazonS3 Client
     */
    private AmazonS3 getAmazonClient() {
        if(client==null ) {
            AWSCredentials credentials = new BasicAWSCredentials(config.getAccessKey(), config.getSecretKey());
            AmazonS3 amazonS3client = AmazonS3ClientBuilder
                    .standard()
                    .withCredentials(new AWSStaticCredentialsProvider(credentials))
                    .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(config.getEndpoint(), null))
                    .withPathStyleAccessEnabled(true)
                    .build();
            this.client = amazonS3client;
            //跨域设置
            BucketCrossOriginConfiguration crossOriginConfiguration = new BucketCrossOriginConfiguration();
            ArrayList<CORSRule> putCorsRules = new ArrayList<CORSRule>();
            CORSRule corRule = new CORSRule();
            ArrayList<String> allowedOrigin = new ArrayList<String>(); //指定允许跨域请求的来源
            allowedOrigin.add( "*"); //通配符
            ArrayList<CORSRule.AllowedMethods> allowedMethod = new ArrayList<CORSRule.AllowedMethods>(); //指定允许的跨域请求方法(GET/PUT/DELETE/POST/HEAD)
            allowedMethod.add(CORSRule.AllowedMethods.GET);
            allowedMethod.add(CORSRule.AllowedMethods.PUT);
            allowedMethod.add(CORSRule.AllowedMethods.DELETE);
            allowedMethod.add(CORSRule.AllowedMethods.HEAD);
            allowedMethod.add(CORSRule.AllowedMethods.POST);
            ArrayList<String> allowedHeader = new ArrayList<String>(); //控制在OPTIONS预取指令中Access-Control-Request-Headers头中指定的header是否允许。
            allowedHeader.add("*");
            corRule.setAllowedMethods(allowedMethod);
            corRule.setAllowedOrigins(allowedOrigin);
            corRule.setAllowedHeaders(allowedHeader);
            //指定浏览器对特定资源的预取(OPTIONS)请求返回结果的缓存时间,单位为秒。获取CORS规则 删除CORS
            corRule.setMaxAgeSeconds(100);
            putCorsRules.add(corRule);
            crossOriginConfiguration.setRules(putCorsRules);
            client.setBucketCrossOriginConfiguration(config.getBucketName(), crossOriginConfiguration);
        }
        return client;
    }

    /**
     * 使用已有的{@link AmazonS3}构造{@link TransferManager}
     * @param client AmazonS3客户端
     * @param threadPoolExecutor
     * @return TransferManager
     */
    private TransferManager buildTransferManager(AmazonS3 client, ThreadPoolExecutor threadPoolExecutor) {
        return TransferManagerBuilder.standard().withS3Client(client).withExecutorFactory(() -> threadPoolExecutor).build();
    }

    private PutObjectRequest buildPutRequest(String key, InputStream in) throws IOException {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(in.available());
        PutObjectRequest request = new PutObjectRequest(config.getBucketName(), key, in, metadata);
        request.setCannedAcl(CannedAccessControlList.PublicRead);
        return request;
    }
    //

    private PutObjectRequest buildFileRequest(String key, File file) throws IOException {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(file.length());
        metadata.setRestoreExpirationTime(new Date(System.currentTimeMillis()+20000));
        PutObjectRequest request = new PutObjectRequest(config.getBucketName(), key, file);
        request.withCannedAcl(CannedAccessControlList.PublicRead).withMetadata(metadata);
        return request;
    }

    /*public static void main(String[] agrs){
        System.out.println(new Date().toGMTString());
        System.out.println(new Date().toString());
    }*/
}
