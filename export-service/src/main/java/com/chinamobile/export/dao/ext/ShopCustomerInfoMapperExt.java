package com.chinamobile.export.dao.ext;

import com.chinamobile.export.pojo.mapper.CustCodeCustIdDO;
import com.chinamobile.export.pojo.mapper.DistributorChannelDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * created by l<PERSON><PERSON>ng on 2024/6/18 09:16
 */
@Mapper
public interface ShopCustomerInfoMapperExt {

    List<CustCodeCustIdDO> custCodeCustIdList(List<String> custCodeList);

    /**
     * 获取分销渠道相关信息
     * @param userIdList
     * @return
     */
    List<DistributorChannelDO> getDistributorChannelInfo(List<String> userIdList);
}
