package com.chinamobile.export.controller;

import com.chinamobile.export.config.ThreadExecutorConfig;
import com.chinamobile.export.exception.StatusConstant;
import com.chinamobile.export.pojo.param.OrderRocExportParam;
import com.chinamobile.export.service.OrderRocService;
import com.chinamobile.export.util.SmsValidUtil;
import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/25
 * @description 订单退货售后controller类
 */
@RestController
public class OrderRocController {

    @Resource
    private OrderRocService orderRocService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 订单退货售后导出
     * @param orderRocExportParam
     * @param loginIfo4Redis
     */
    @GetMapping(value = "/orderRoc/export")
//    @Auth(authCode = BaseConstant.REAFTER_CONSIGNMENT_QUERY)
    public BaseAnswer exportOrderRoc(@Valid OrderRocExportParam orderRocExportParam,
                                     @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                               HttpServletResponse response) throws Exception {
        String userId = loginIfo4Redis.getUserId();

        String importRedisKey = "import_order_roc_export_".concat(userId);
        Boolean isUserImport = stringRedisTemplate.hasKey(importRedisKey);
        if (isUserImport != null && isUserImport){
            /*response.setHeader("statecode", "99999");
            response.setHeader("message", URLEncoder.encode("有文件正在导入中，在该文件成功前请不要再次导入", "UTF-8").replaceAll("\\+", "%20"));*/
            throw new BusinessException("10004","有文件正在导出中，在该文件成功前请不要再次导出");
        }

        stringRedisTemplate.opsForValue().set(importRedisKey,importRedisKey,12, TimeUnit.HOURS);

        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (ObjectUtils.isEmpty(dataPermissionCodes) || (
                !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_REFUND_SYSTEM)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_REFUND_COMPANY)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_REFUND_PERSONAL)
        )) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }

        String exportPhone = orderRocExportParam.getExportPhone();
        String exportMask = orderRocExportParam.getExportMask() == null?"":orderRocExportParam.getExportMask()+"";
        Boolean isAdmin = loginIfo4Redis.getIsAdmin();
        // 验证当前用户是否超管，及验证码是否正确
        SmsValidUtil.checkSmsValid(isAdmin,exportMask,exportPhone,redisTemplate);

        ThreadExecutorConfig.executorService.execute(() -> {
            try {
                orderRocService.exportOrderRoc(orderRocExportParam,loginIfo4Redis,importRedisKey);
            } catch (Exception e) {
                stringRedisTemplate.delete(importRedisKey);
                throw new RuntimeException(e);
            }
        });
        return BaseAnswer.success(null);
    }

}
