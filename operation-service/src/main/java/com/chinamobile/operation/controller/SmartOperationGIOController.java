package com.chinamobile.operation.controller;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.operation.pojo.response.GioGroupDetailResponse;
import com.chinamobile.operation.service.SmartOperationGIOService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
/**
 */
@RestController
@RequestMapping("/gio")
public class SmartOperationGIOController {
    @Autowired
    private SmartOperationGIOService smartOperationGIOService;


    /**
     * 获取列表
     */
    @GetMapping("/group/list")
    public BaseAnswer<List<GioGroupDetailResponse>> getGroupDetailList(String name){
        return smartOperationGIOService.getGroupDetailList(name);
    }
}
