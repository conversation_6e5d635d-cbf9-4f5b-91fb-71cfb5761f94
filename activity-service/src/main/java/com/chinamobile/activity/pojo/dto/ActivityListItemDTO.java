package com.chinamobile.activity.pojo.dto;

import lombok.Data;

import java.util.Date;

/**
 * 活动专区列表表项
 * <AUTHOR>
 */
@Data
public class ActivityListItemDTO {
    private String id;
    /**
     * 活动专区名称
     */
    private String activityName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String userName;

    /**
     * 渠道类型，1 --PC; 2-- 移动
     */
    private Integer channelType;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 发布省范围
     */
    private String provinceName;

    /**
     * 发布省编码
     */
    private String provinceCode;

    /**
     * 状态, 1-- 待发布； 2-- 已发布
     */
    private Integer status;
}
