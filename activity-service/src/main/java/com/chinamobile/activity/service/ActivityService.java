package com.chinamobile.activity.service;

import com.chinamobile.activity.pojo.dto.ActivityListItemDTO;
import com.chinamobile.activity.pojo.dto.FloorProductDTO;
import com.chinamobile.activity.pojo.param.ActivityListParam;
import com.chinamobile.activity.pojo.param.SaveOrPublishActivityParam;
import com.chinamobile.activity.pojo.param.UpdateNameParam;
import com.chinamobile.activity.pojo.vo.ActivityDetailVO;
import com.chinamobile.activity.pojo.vo.ActivityNavigationVO;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.mode.ActivityFeignDTO;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ActivityService {

    BaseAnswer<Void> save(SaveOrPublishActivityParam param, String userId);

    BaseAnswer<ActivityDetailVO> getDetail(String id, Boolean inner,Boolean needFloor);

    BaseAnswer<String> uploadPicture(MultipartFile file);
    BaseAnswer<PageInfo<ActivityListItemDTO>> getList(ActivityListParam param);

    /**
     * 查询专区添加商品时以销售额排序时返回销售额降序数据。自定义模式先返回空数据吧。自定义以前端数据为准。以前的逻辑
     * @param spuCode
     * @param sortMode
     * @return
     */
    BaseAnswer<List<FloorProductDTO>> getActivitySpuSort(List<String> spuCode,String sortMode);

    List<ActivityNavigationVO> getActivityList(Integer channelType);

    BaseAnswer<Void> delete(List<String> ids);

    /**服务内部使用*/
    BaseAnswer<UpResult> uploadPictureInner(MultipartFile file,String rename);

    BaseAnswer<Void> updateName(UpdateNameParam param);


    List<ActivityNavigationVO> searchActivity(Integer channel,String keyWord,String provinceCode);

    BaseAnswer<ActivityFeignDTO> getActivityById(String id);
}
