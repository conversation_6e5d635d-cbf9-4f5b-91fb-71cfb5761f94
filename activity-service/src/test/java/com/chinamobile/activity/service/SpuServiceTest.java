package com.chinamobile.activity.service;

import com.alibaba.fastjson.JSON;
import com.chinamobile.activity.dao.ext.SpuOfferingInfoMapperExt;
import com.chinamobile.activity.pojo.param.QuerySpuByCategoryParam;
import com.chinamobile.activity.pojo.param.QuerySpuByFloorParam;
import com.chinamobile.activity.pojo.vo.ActivityDetailSpuListVO;
import com.chinamobile.activity.pojo.vo.ActivityFloorSpuVO;
import com.chinamobile.activity.pojo.vo.CategorySpuListVO;
import com.chinamobile.activity.pojo.vo.SpuOfferingInfoVO;
import com.chinamobile.iot.sc.mode.PageData;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/15 15:15
 * @description TODO
 */
@SpringBootTest
public class SpuServiceTest {

    @Autowired
    private SpuService spuService;

    @Test
    public void testQueryCategories() {
        List<String> categories = spuService.queryCategories();
        System.out.println(JSON.toJSON(categories));
    }

    @Test
    public void testQuerySpuByCategory() {
        QuerySpuByCategoryParam param = new QuerySpuByCategoryParam();
        param.setOfferingClass("A08");
        param.setProvinceCode("000");
        //param.setKeyWord("8000000071");
        PageData<SpuOfferingInfoVO> data = spuService.querySpuByCategory(param);
        System.out.println(JSON.toJSON(data));
    }

    @Test
    public void testQuerySpuByFloor() {
        List<ActivityFloorSpuVO> activityFloorSpuVOS = spuService.querySpuByFloor("1008757447378137088");
        System.out.println(JSON.toJSON(activityFloorSpuVOS));
    }

    @Test
    public void testQuerySpuList() {
        QuerySpuByFloorParam param = new QuerySpuByFloorParam();
        param.setActivityId("1298298823801737216");
        param.setFloorId("1352571676503777280");
//        param.setSort(2);
//        param.setKeyWord("测试");
        ActivityDetailSpuListVO activityDetailSpuListVO = spuService.querySpuList(param);
        System.out.println(JSON.toJSON(activityDetailSpuListVO));
    }
}
