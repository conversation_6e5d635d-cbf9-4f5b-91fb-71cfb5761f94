# dockerfile配置
#FROM hub.iot.chinamobile.com/runtime/jre:8
#解决验证码的API用到了AWT的东西，openjdk基础镜像无AWT组件
#增加skywalking agent 基于镜像hub.iot.chinamobile.com/ai_team/iotmall-jdk:v1.0.1
FROM hub.iot.chinamobile.com/stqdyy/skywalking-agent:8.15
#时区设置
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
&& echo "Asia/Shanghai" > /etc/timezone
COPY ./activity-service/target/activity-service.jar /opt/java/
WORKDIR /opt/java/
ENTRYPOINT ["java","-XX:+UnlockExperimentalVMOptions", "-XX:+UseCGroupMemoryLimitForHeap","-jar","activity-service.jar"]
EXPOSE 9797
